import { HttpUrlEncodingCodec } from "@angular/common/http";
import { Component, ElementRef, OnInit, Renderer2 } from "@angular/core";
import { NavigationStart, Router } from "@angular/router";
import { NgbModal, NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";

import StoreMenu from "../../../shared/data/store-menu.json";
import { AuthService } from "src/app/core/authentication/auth.service";
import { CartService } from "../services/cart.service";
import { SettingsService } from "../../backoffice/settings/settings.service";
import { SelectCustomerComponent } from "src/app/shared/components/manage-users/select-customer/select-customer.component";

@Component({
  selector: "app-customer-store-layout",
  templateUrl: "./customer-store-layout.component.html",
  styleUrls: ["./customer-store-layout.component.scss"],
})
export class CustomerStoreLayoutComponent implements OnInit {
  public isExpanded: boolean = false;
  public miniMenuOpen = false;
  public links: any = StoreMenu;
  public userName = "";
  public showSidebar = true;
  public cart: any = null;
  public homeUrl = "";
  public privacyPolicyUrl = "";
  public contactUrl = "";
  public termsUrl = "";
  public loggedInUser: any = null;

  private isDialogOpen: boolean = false;
  private modalOption: NgbModalOptions = {};

  protected readonly codec = new HttpUrlEncodingCodec();

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private router: Router,
    private dialog: NgbModal,
    private authService: AuthService,
    private cartService: CartService,
    private settingService: SettingsService
  ) {
    this.loggedInUser = this.authService.userDetail;
    this.renderer.listen("window", "click", (e: Event) => {
      const isMiniMenuElement =
        (e.target as Element).classList.contains("child-link") ||
        (e.target as Element).parentElement?.classList.contains("child-link") ||
        (e.target as Element).parentElement?.parentElement?.classList.contains(
          "child-link"
        );
      const isParentLink =
        (e.target as Element).classList.contains("parent-link") ||
        (e.target as Element).parentElement?.classList.contains(
          "parent-link"
        ) ||
        (e.target as Element).parentElement?.parentElement?.classList.contains(
          "parent-link"
        );
      if (!isParentLink && !isMiniMenuElement && this.miniMenuOpen) {
        this.miniMenuOpen = false;
        this.closeMiniMenu();
      }
    });

    router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.closeMiniMenu();
      }
      this.showSidebar = true;
      if (router.url.indexOf("/store/dashboard") > -1) {
        this.showSidebar = false;
      }
    });
  }

  isChildRouteActive(): any {
    const childRoutes: any = ["/tickets/create", "tickets"];
    return childRoutes.some((route: any) => location.href.includes(route));
  }

  ngOnInit(): void {
    this.userName = this.authService.userDetail.display_name;
    this.cartService.getCart().subscribe((cart) => {
      this.cart = cart;
    });
    this.settingService.getSettings().subscribe((data) => {
      this.homeUrl = data.home_url;
      this.contactUrl = data.contact_url;
      this.privacyPolicyUrl = data.privacy_policy_url;
      this.termsUrl = data.terms_url;
    });
  }

  handleSidebarToggle() {
    this.isExpanded = !this.isExpanded;
  }

  logout() {
    this.authService.doLogout();
  }

  getLinkParams(value: any) {
    return this.getParams(value ?? "");
  }

  getParams(query: string): { [key: string]: string } {
    return {
      query: this.codec
        .decodeValue(this.decodeUriComponentSafe(query))
        .replace(/\+/g, " "),
    };
  }

  protected decodeUriComponentSafe(query: string): string {
    return query.replace(/%(?![0-9a-fA-F]{2})/g, "%25");
  }

  toggle(e: HTMLElement, event: Event) {
    e.classList.toggle("hidden");
    (event.target as Element).classList.toggle("chevron-open");
  }

  closeMiniMenu() {
    const curMiniMenu = this.el.nativeElement.querySelectorAll(".mini-menu");

    if (curMiniMenu) {
      curMiniMenu.forEach(
        (element: { classList: { remove: (arg0: string) => void } }) => {
          element.classList.remove("mini-menu");
          element.classList.remove("border");
        }
      );
    }
  }

  openMiniMenu(e: HTMLElement, event: any) {
    this.closeMiniMenu();
    if (this.miniMenuOpen) {
      this.miniMenuOpen = false;
    }
    setTimeout(() => {
      this.miniMenuOpen = true;
      e.classList.toggle("mini-menu");
      e.classList.toggle("border");
      e.style.top = event.clientY + 20;
    }, 0);
  }

  openDialog() {
    if (!this.isDialogOpen) {
      this.isDialogOpen = true;
      this.modalOption.backdrop = "static";
      this.modalOption.keyboard = false;
      this.modalOption.size = "xl";
      const dialogRef = this.dialog.open(
        SelectCustomerComponent,
        this.modalOption
      );
      dialogRef.result
        .then(() => {
          this.isDialogOpen = false;
        })
        .catch(() => {
          this.isDialogOpen = false;
        });
    }
  }
}
