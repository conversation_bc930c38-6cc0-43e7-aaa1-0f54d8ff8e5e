<div class="counter-container d-flex justify-content-between">
  <div class="cart-quantity d-flex justify-content-evenly align-items-center flex-grow-1">
    <button class="bg-transparent border-0 px-1" type="button" (click)="decrement()"
      [disabled]="control.disabled || control.value <= min"
      [tabindex]="control.disabled || control.value <= min ? -1 : 0">
      <span class="material-icons-outlined">remove</span>
    </button>
    <input class="form-control" #qty [ngModel]="control.value" (ngModelChange)="changeVal($event)" type="number"
      (focusout)="setInputValue(control.value)" [min]="min" [max]="max">
    <button class="bg-transparent border-0 px-1" type="button" (click)="increment()"
      [disabled]="control.disabled || control.value >= max" tabindex="0">
      <span class="material-icons-outlined">add</span>
    </button>
  </div>
</div>