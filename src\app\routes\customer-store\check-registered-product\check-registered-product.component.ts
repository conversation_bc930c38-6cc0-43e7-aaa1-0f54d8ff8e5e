import { Component } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { Router } from "@angular/router";
import { Subject, takeUntil, take } from "rxjs";
import moment from "moment";

import { AuthService } from "src/app/core/authentication/auth.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { ProductRegisterService } from "../services/product-register/product-register.service";
import { PagerService } from "../services/pager.service";

@Component({
  selector: "app-check-registered-product",
  templateUrl: "./check-registered-product.component.html",
  styleUrls: ["./check-registered-product.component.scss"],
})
export class CheckRegisteredProductComponent {
  private ngUnsubscribe = new Subject<void>();
  public moment: any = moment;
  public submitted: boolean = false;
  public saving: boolean = false;
  public loading: boolean = false;
  public sellerDetails: any = null;
  public registerProductList: any[] = [];
  public prPaginated: any = [];
  public totalRecords: number = 0;
  public params: any = { perPage: 10, pageNo: 1 };
  public pager: any = {};
  public filterForm: FormGroup;

  constructor(
    private _snackBar: AppToastService,
    public router: Router,
    private formBuilder: FormBuilder,
    public authService: AuthService,
    public productRegisterService: ProductRegisterService,
    private pagerService: PagerService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit(): void {
    this.createForm();
    this.getRegisterProductList();
  }

  // convenience getter for easy access to form fields
  get f(): any {
    return this.filterForm?.controls;
  }

  createForm() {
    this.filterForm = this.formBuilder.group({
      serial: [""],
      product_id: [""],
    });
  }

  goToTicket(ticket: any) {
    this.router.navigate([`/store/tickets/${ticket.TICKET_ID}`]);
  }

  clear() {
    this.filterForm.patchValue({ serial: "", product_id: "" });
  }

  getRegisterProductList() {
    this.submitted = true;
    if (this.filterForm.invalid) {
      return;
    }
    const payload: any = this.filterForm.value;
    const search: any[] = [];
    const searchBy: any[] = [];
    for (let key in payload) {
      if (payload.hasOwnProperty(key) && payload[key]) {
        search.push(payload[key]);
        searchBy.push(key);
      }
    }
    if (search.length) {
      this.params.search = search.join(",");
    } else {
      delete this.params.search;
    }
    if (searchBy.length) {
      this.params.searchBy = searchBy.join(",");
    } else {
      delete this.params.searchBy;
    }
    this.loading = true;
    return this.productRegisterService
      .getAlls(this.params)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.loading = false;
          this.submitted = false;
          this.registerProductList = res?.data || [];
          this.totalRecords = res?.total;
          this.setPage(this.params.pageNo, false);
        },
        error: () => {
          this.loading = false;
          this.submitted = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  setPage(page: number, load: boolean) {
    this.pager = this.pagerService.getPager(
      this.totalRecords,
      page,
      this.params.perPage
    );
    this.prPaginated = this.registerProductList;
    if (load) {
      this.params.pageNo = page;
      this.getRegisterProductList();
    }
  }

  downloadPurchaseProof(serial: any) {
    this.productRegisterService
      .downloadPurchaseProof(serial)
      .pipe(takeUntil(this.ngUnsubscribe), take(1))
      .subscribe((response) => {
        const downloadLink = document.createElement("a");
        //@ts-ignore
        downloadLink.href = URL.createObjectURL(
          new Blob([response.body], { type: response.body.type })
        );
        const extension = response.body.type.split("/")[1];
        downloadLink.download = `${serial}_purchase_proof.${extension}`;
        downloadLink.click();
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
