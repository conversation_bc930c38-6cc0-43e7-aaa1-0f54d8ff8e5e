import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormGroup, Validators, FormBuilder, FormArray, FormControl } from '@angular/forms';
import { ApiConstant } from 'src/app/constants/api.constants';
import { ConfigService } from '../config.service';
import moment from 'moment';
import { AppToastService } from 'src/app/shared/services/toast.service';

@Component({
  selector: 'app-categories',
  templateUrl: './categories.component.html',
  styleUrls: ['./categories.component.scss']
})
export class CategoriesComponent {
  @Input() model: any = {};
  @Input() categories: any = [];
  @Input() catalogs: any = [];
  @Output() dataUpdated = new EventEmitter<any>();
  form = new FormGroup({});
  cardOpen = true;
  submitted = false;
  aciveTab = "General";
  currentForm: any;
  moment = moment;

  generalForm: any = this.fb.group({
    id: [""],
    name: ["", Validators.required],
    parent_category_id: [""],
    parentCategoryHierarchy: [[]],
    catalogs: [""],
  });

  catalogsForm = this.fb.group({
    id: [""],
    catalogs: [""],
    parent_category_id: [""],
    selectedCatalogs: ['']
  });

  formMap: Record<string, any> = {
    General: this.generalForm,
    Catalogs: this.catalogsForm
  };

  constructor(
    public fb: FormBuilder,
    private service: ConfigService,
    private _snackBar: AppToastService
  ) { }

  ngOnInit(): void {
    this.setActiveform('General');
    this.onParentCategoryIDChange();
  }

  ngOnChanges(changes: SimpleChanges) {
    this.generalForm.patchValue({
      id: this.model.id,
      name: this.model.name,
      parent_category_id: this.model.parent_category_id,
      catalogs: this.model?.catalogs?.map((c: any) => c.id)
    });
    this.catalogsForm.patchValue({
      id: this.model.id,
      catalogs: this.model?.catalogs?.map((c: any) => c.id),
      parent_category_id: this.model?.parent_category_id
    });
    this.setSelectedPCH(this.model.parent_category_id);
    this.submitted = false;
  }

  setSelectedPCH(parent_category_id: any) {
    const pch = this.categories.find((o: any) => parent_category_id == o.id);
    if (pch) {
      this.generalForm.patchValue({
        parentCategoryHierarchy: pch.parent_category_hierarchy,
      });
    } else {
      this.generalForm.patchValue({
        parentCategoryHierarchy: [],
      });
    }
  }

  onParentCategoryIDChange() {
    this.generalForm
    .get("parent_category_id")
    .valueChanges
    .subscribe((value: any) => {
      this.setSelectedPCH(value);
    });
  }

  get f() {
    return this.currentForm.controls;
  }

  setActiveform(name: string) {
    this.aciveTab = name;
    this.formMap[name] && (this.currentForm = this.formMap[name]);
  }

  update(data: any) {
    const id = data.id;
    const newObj = {
      ...data
    };
    if (data.catalogs) {
      newObj.product_catalogs_ids = data.catalogs.join(',');
    }
    delete newObj.id;
    delete newObj.catalogs;
    delete newObj.selectedCatalogs;
    delete newObj.parentCategoryHierarchy;
    this.service.update(ApiConstant.GET_CATEGORIES, newObj, id).subscribe({
      complete: () => {
        this.submitted = false;
        this.dataUpdated.emit({
          url: ApiConstant.GET_CATEGORIES,
          id
        });
        this._snackBar.open("Changes saved successfully!");
      },
      error: (err) => {
        this.submitted = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  submitForm() {
    this._markAsTouched(this.currentForm);
    if (this.currentForm.valid) {
      this.submitted = true;
      this.update(this.currentForm.value);
    }
  }

  private _markAsTouched(group: FormGroup | FormArray) {
    group.markAsTouched({ onlySelf: true });

    Object.keys(group.controls).map((field) => {
      const control = group.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this._markAsTouched(control);
      }
    });
  }

  toogleDetails() {
    this.cardOpen = !this.cardOpen;
  }

  getCatalogName(id: string) {
    const catalog = this.catalogs.find((catalog: any) => catalog.id === id)
    return catalog?.name || '';
  }

  removeCatalog(id: string) {
    let pushedCatalogs = this.f.catalogs.value;
    pushedCatalogs.splice(pushedCatalogs.indexOf(id), 1);
    this.catalogsForm.patchValue({
      catalogs: pushedCatalogs
    });
  }

  addCatalog() {
    console.log(this.f.catalogs.value);
    let pushedCatalogs = this.f.catalogs.value;
    let selectedCatalogs = this.f.selectedCatalogs.value;
    if (selectedCatalogs.length) {
      for (let i = 0; i < selectedCatalogs.length; i++) {
        const element = selectedCatalogs[i];
        if (pushedCatalogs.indexOf(element) === -1) {
          pushedCatalogs.push(element);
        }
      }
    };
    this.catalogsForm.patchValue({
      catalogs: pushedCatalogs,
      selectedCatalogs: ''
    });
  }

}
