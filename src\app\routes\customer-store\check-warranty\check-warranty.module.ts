import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { ReactiveFormsModule } from "@angular/forms";

import { CheckWarrantyComponent } from "./check-warranty.component";
import { SharedModule } from "src/app/shared/shared.module";

@NgModule({
  declarations: [CheckWarrantyComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: CheckWarrantyComponent }]),
  ],
})
export class CheckWarrantyModule {}
