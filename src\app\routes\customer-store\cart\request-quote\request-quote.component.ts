import { Component, Input, OnInit } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { CartService } from "../../services/cart.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
  selector: "app-request-quote",
  templateUrl: "./request-quote.component.html",
  styleUrls: ["./request-quote.component.scss"],
})
export class RequestQuoteComponent implements OnInit {
  @Input() sosRes: any = null;
  @Input() salesQuoteTypeCode: any = null;
  @Input() quoteTextCode: any = null;

  public submitting = false;
  public form: FormGroup = this.fb.group({
    name: ["", Validators.required],
    description: ["", Validators.required],
    salesQuoteTypeCode: [""],
    quoteTextCode: [""],
  });

  constructor(
    public router: Router,
    private fb: FormBuilder,
    public dialogRef: NgbActiveModal,
    private cartService: CartService
  ) {
  }

  ngOnInit(): void {
    this.form.patchValue({ salesQuoteTypeCode: this.salesQuoteTypeCode });
    this.form.patchValue({ quoteTextCode: this.quoteTextCode });
  }

  get f(): any {
    return this.form.controls;
  }

  trackBy(index: number, product: any) {
    return product.Material;
  }

  goToCataloguesPage() {
    this.dialogRef.dismiss();
    this.router.navigate([`store/catalogues`]);
  }

  goToCatalogDetailPage(Material: string) {
    this.dialogRef.dismiss(false);
    this.router.navigate([`store/product-details`, Material]);
  }

  onSubmit() {
    if (this.form.invalid) {
      return;
    }
    this.submitting = true;
    this.cartService.salesQuoteCreation(this.form.value).subscribe({
      next: (data: any) => {
        this.dialogRef.close({ data });
      },
      error: (e) => {
        console.error("Error while processing sales quote create request.", e);
      },
    });
  }
}
