import { Component, HostListener, Input, SimpleChanges } from "@angular/core";
import { CellClickedEvent, ColDef, GridReadyEvent } from "ag-grid-community";
import { Subject, takeUntil } from "rxjs";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

import { BannerService } from "./banner.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { BannerModalComponent } from "./banner-modal/banner-modal.component";
import { ActionComponent } from "./banner-modal/action/action.component";

@Component({
  selector: "app-banner",
  templateUrl: "./banner.component.html",
  styleUrls: ["./banner.component.scss"],
})
export class BannerComponent {
  private ngUnsubscribe = new Subject<void>();
  @Input() custGroup: any = null;
  @Input() custAccountGroup: any = null;
  @Input() isActive: any = null;
  @Input() placement: any = "HOME";

  public defaultColDef: ColDef = {
    flex: 1,
  };

  public columnDefs: ColDef[] = [
    {
      field: "id",
      headerName: "ID",
      sortable: true,
      width: 50,
      minWidth: 50,
    },
    {
      field: "name",
      headerName: "Name",
      sortable: true,
      width: 285,
      minWidth: 285,
    },
    {
      field: "type",
      headerName: "Type",
      sortable: true,
      width: 75,
      minWidth: 75,
    },
    {
      field: "sequence",
      headerName: "Sequence",
      sortable: true,
      width: 100,
      minWidth: 100,
    },
    {
      field: "customer_group",
      headerName: "Cust. Grp",
      sortable: true,
      width: 100,
      minWidth: 100,
    },
    {
      field: "customer_account_group",
      headerName: "Cust. Acc Grp",
      sortable: true,
      width: 125,
      minWidth: 125,
    },
    {
      field: "url",
      headerName: "URL",
      headerComponentParams: { menuIcon: "article" },
      sortable: false,
      cellRenderer: (params: any) => {
        if (params?.data?.url) {
          return `<a href="javascript:void(0)" title="${params.data.url}"
                    class="text-primary py-2 d-flex align-items-center gap-1 grid-cell">
                      <i class="material-icons-outlined download">content_copy</i>
                  </a>`;
        }
        return "";
      },
      onCellClicked: (event: CellClickedEvent) => {
        this.copyURL(event.data.url);
      },
      width: 75,
      minWidth: 75,
    },
    {
      headerName: "Action",
      cellRenderer: ActionComponent,
      cellRendererParams: {
        onClick: this.gridAction.bind(this),
        label: "Delete",
      },
      sortable: false,
      width: 160,
      minWidth: 160,
    },
  ];

  private gridApi!: any;
  public isLoading = true;
  public rowData: any[] = [];

  constructor(
    private dialog: NgbModal,
    private bannerService: BannerService,
    private _snackBar: AppToastService
  ) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    const isActive: any = changes["isActive"];
    if (isActive) {
      const previousValue = isActive.previousValue;
      const currentValue = isActive.currentValue;
      if (
        this.gridApi &&
        previousValue !== currentValue &&
        isActive.currentValue
      ) {
        this.getCustGroupBanner();
      }
    }
  }

  @HostListener("window:resize", ["$event"])
  onResize(event: any) {
    this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params: GridReadyEvent<any>) {
    this.gridApi = params.api;
  }

  getRowId(params: any) {
    return params.data.id;
  }

  getCustGroupBanner() {
    this.gridApi.showLoadingOverlay();
    this.bannerService
      .getCustGroupBanner(this.placement, this.custGroup, this.custAccountGroup)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          this.rowData = data;
          this.gridApi.setGridOption("rowData", data);
          var params = {
            force: true,
            suppressFlash: true,
          };
          this.gridApi.refreshCells(params);
          this.gridApi.hideOverlay();
        },
        error: (err: any) => {
          this.gridApi.hideOverlay();
          this._snackBar.open(
            err.error?.message ||
              "Error while processing get user's customer request.",
            { type: "Error" }
          );
        },
      });
  }

  openDialog(data: any = null) {
    const dialogRef = this.dialog.open(BannerModalComponent);
    dialogRef.componentInstance.data = data;
    dialogRef.componentInstance.placement = this.placement;
    dialogRef.componentInstance.customer_group = this.custGroup;
    dialogRef.componentInstance.customer_account_group = this.custAccountGroup;
    dialogRef.result.then((result) => {
      if (result?.id) {
        this.updateBanner(result);
      } else {
        this.addBanner(result);
      }
    });
  }

  addBanner(data: any) {
    const payload: any = { ...data };
    delete payload.id;
    this.bannerService
      .createBanner(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          this.getCustGroupBanner();
        },
        error: (err: any) => {
          this._snackBar.open(
            err.error?.message ||
              "Error while processing get user's customer request.",
            { type: "Error" }
          );
        },
      });
  }

  updateBanner(data: any) {
    const payload: any = { ...data };
    delete payload.id;
    this.bannerService
      .updateBanner(data.id, payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          this.getCustGroupBanner();
        },
        error: (err: any) => {
          this._snackBar.open(
            err.error?.message ||
              "Error while processing get user's customer request.",
            { type: "Error" }
          );
        },
      });
  }

  deleteBanner(id: any) {
    this.bannerService
      .deleteBanner(id)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          this.getCustGroupBanner();
        },
        error: (err: any) => {
          this._snackBar.open(
            err.error?.message ||
              "Error while processing get user's customer request.",
            { type: "Error" }
          );
        },
      });
  }

  gridAction(e: any) {
    switch (e.action) {
      case "EDIT":
        this.openDialog(e.rowData);
        break;
      case "DELETE":
        this.deleteBanner(e.rowData.id);
        break;
      default:
      // code block
    }
  }

  copyURL(val: string) {
    const selBox = document.createElement("textarea");
    selBox.style.position = "fixed";
    selBox.style.left = "0";
    selBox.style.top = "0";
    selBox.style.opacity = "0";
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand("copy");
    document.body.removeChild(selBox);
    this._snackBar.open("URL Copied");
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
