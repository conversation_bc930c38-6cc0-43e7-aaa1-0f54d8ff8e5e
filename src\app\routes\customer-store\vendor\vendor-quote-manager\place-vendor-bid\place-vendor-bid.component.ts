import { Component, Input } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Subject } from "rxjs";

@Component({
  selector: "app-place-vendor-bid",
  templateUrl: "./place-vendor-bid.component.html",
  styleUrls: ["./place-vendor-bid.component.scss"],
})
export class PlaceVendorBidComponent {
  private ngUnsubscribe = new Subject<void>();
  @Input() data: any = null;
  public permissions: any[] = [];
  public saving: any = false;

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit(): void {}

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
