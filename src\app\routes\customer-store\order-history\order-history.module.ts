import { NgModule } from '@angular/core';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { RouterModule } from '@angular/router';

import { OrderHistoryComponent } from './order-history.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

@NgModule({
  declarations: [OrderHistoryComponent],
  providers: [CurrencyPipe],
  imports: [
    CommonModule,
    SharedModule,
    NgbDatepickerModule,
    RouterModule.forChild([{ path: '', component: OrderHistoryComponent }]),
  ]
})
export class OrderHistoryModule { }
