<ng-container *ngIf="!loading">
  <div class="all-main-title-sec product-details-main-title">
    <h1>Order ID: {{orderDetails?.SOLDTO?.SD_DOC}}</h1>
    <div class="all-bedcrumbs">
      <ul>
        <li>
          <a [routerLink]="['/store/dashboard']">
            <span class="material-icons-outlined">home</span> Home
          </a>
        </li>
        <li>
          <a [routerLink]="['/store/order-history']">
            <span class="material-icons-outlined">list_alt</span> Orders
          </a>
        </li>
        <li>Order Details</li>
      </ul>
    </div>
  </div>

  <div class="q-order-buttons-container">
    <ng-container *checkPermission="'P0013'">
      <button type="button" class="q-order-btn" (click)="reorder()" [disabled]="isReordering">
        <span class="material-icons-outlined" *ngIf="!isReordering">add_shopping_cart</span>
        <i class="material-icons-outlined spin m-1" *ngIf="isReordering">sync</i>
        Reorder
      </button>
    </ng-container>
    <ng-container *checkAppFeature="'F0005'">
      <ng-container *checkPermission="'P0010'">
        <button type="button" class="q-order-btn"
          [routerLink]="['/store/product-return', orderDetails?.SOLDTO?.SD_DOC, orderType]"
          *ngIf="orderDetails?.ORDER_HDR?.DOC_STAT === 'C'">
          <span class="material-icons-outlined">settings_backup_restore</span> Return
        </button>
      </ng-container>
    </ng-container>
  </div>

  <div class="order-id-sec">
    <div class="order-id-body all-details-page">
      <div class="order-id-info">
        <div class="order-details">
          <h3>Order Details</h3>
          <ul>
            <li><span class="material-icons-outlined">pin</span> Order # <span>{{orderDetails?.SOLDTO?.SD_DOC}}</span>
            </li>
            <li><span class="material-icons-outlined">pin</span> Customer #
              <span>{{orderDetails?.SOLDTO?.SOLDTOPARTY}}</span>
            </li>
            <li><span class="material-icons-outlined">person_outline</span> Customer Name
              <span>{{customer?.name}}</span>
            </li>
            <li><span class="material-icons-outlined">confirmation_number</span> Purchase Order #
              <span>{{orderDetails?.ORDER_HDR?.PURCH_NO}}</span>
            </li>
            <li><span class="material-icons-outlined">event_note</span> Requested Delivery Date
              <span>{{moment(orderDetails?.ORDER_HDR?.REQ_DATE, 'YYYYMMDD').format('MM/DD/YYYY')}}</span>
            </li>
            <li><span class="material-icons-outlined">event_note</span> Date Placed
              <span>{{moment(orderDetails?.ORDER_HDR?.DOC_DATE, 'YYYYMMDD').format('MM/DD/YYYY')}}</span>
            </li>
            <li>
              <span class="material-icons-outlined">event_note</span>
              Special Instruction
              <span *ngIf="orderDetails?.ORDER_HDR?.ORDER_HDR_TEXT?.length">
                {{ orderDetails?.ORDER_HDR?.ORDER_HDR_TEXT[0]?.TEXT }}
              </span>
              <span></span>
            </li>
          </ul>
        </div>
        <div class="order-details">
          <h3>Shipping Details</h3>
          <ul>
            <li><span class="material-icons-outlined">pin</span> Business Partner #
              <span>{{shipToParty?.bp_customer_number}}</span>
            </li>
            <li><span class="material-icons-outlined">person_outline</span> Name <span>{{shipToParty?.name}}</span></li>
            <li><span class="material-icons-outlined">home</span> Address <span>{{shipToParty?.address}}</span></li>
          </ul>
        </div>
        <div class="order-details">
          <h3>Items to be shipped</h3>
          <div class="item-box" *ngFor="let item of orderDetails.ORDER_LINE_DETAIL">
            <div class="item-box-img" [ngStyle]="{'background-image': 'url('+ item.imageUrl + ')'}">
            </div>
            <div class="item-box-content">
              <h4>{{item.SHORT_TEXT}}</h4>
              <small>{{item.MATERIAL}}</small>
              <div class="item-box-bottom-content">
                <div class="quantity">Quantity: <span>{{item.REQ_QTY}}</span></div>
                <div class="item-price">{{ item.formatted_base_price }} <span>{{ item.formatted_base_price_each
                    }} each</span></div>
              </div>
              <a href="https://www.fedex.com/en-in/tracking.html" target="_blank" class="tracking-d">Tracking Details</a>
            </div>
          </div>
        </div>
      </div>
      <div class="order-id-summary">
        <div class="d-flex flex-wrap justify-content-between align-items-center">
          <h3>Order Summary</h3>
          <small><strong>Status:</strong> <span class="material-icons-outlined">motion_photos_on</span>
            {{statuses[orderDetails?.ORDER_HDR?.DOC_STAT] || ''}}</small>
        </div>
        <div class="order-summary-price">
          <ul>
            <li>Subtotal <span>{{ orderDetails?.formatted_sub_total }}</span></li>
            <li>Shipping <span>{{ orderDetails?.formatted_shipping }}</span></li>
            <li>Tax <span>{{ orderDetails?.formatted_sales_tax }}</span></li>
          </ul>
          <ul>
            <li class="total-price">Total <span>{{ orderDetails?.formatted_total }}</span></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</ng-container>
<app-loader *ngIf="loading"></app-loader>