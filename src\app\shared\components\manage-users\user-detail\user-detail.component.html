<div>
  <div class="d-flex justify-content-between">
    <span (click)="toogleDetails()" class="expand-title d-flex align-items-center">
      <i class="material-icons-outlined">{{cardOpen ? 'expand_more': 'chevron_right'}}</i>&nbsp;{{
      'form.details' | translate }}
    </span>
    <div *ngIf="cardOpen" class="d-flex gap-1">
      <button class="btn btn-primary" (click)="submitForm()">
        {{ 'form.action.submit' | translate }}
      </button>
      <button class="btn btn-primary" (click)="refresh()" [disabled]="refreshing">
        <span class="material-icons-outlined">
          refresh
        </span>
      </button>
    </div>
  </div>
  <div *ngIf="cardOpen">
    <asar-tabs (activeTab)="setActiveform($event)">
      <asar-tab [tabTitle]="'General'">
        <form [formGroup]="generalForm">
          <div class="row">
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.firstName" | translate }}</label>
              <input class="form-control" type="text" formControlName="first_name" />
              <span class="text-error" *ngIf="f.first_name?.touched && f.first_name.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.lastName" | translate }}</label>
              <input class="form-control" type="text" formControlName="last_name" />
              <span class="text-error" *ngIf="f.last_name?.touched && f.last_name.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.email" | translate }}</label>
              <input class="form-control" type="text" formControlName="email" />
              <span class="text-error" *ngIf="f.email?.touched && f.email.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
              <span class="text-error" *ngIf="f.email?.touched && f.email.hasError('pattern')">
                {{ 'validation.email.pattern' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.address" | translate }}</label>
              <input class="form-control" type="text" formControlName="contact_address" />
              <span class="text-error" *ngIf="f.contact_address?.touched && f.contact_address.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.validFrom" | translate }}</label>
              <input class="form-control" type="date" formControlName="valid_from" />
              <span class="text-error" *ngIf="f.valid_from?.touched && f.valid_from.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.validTo" | translate }}</label>
              <input class="form-control" type="date" formControlName="valid_to" />
              <span class="text-error" *ngIf="f.valid_to?.touched && f.valid_to.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
              <span class="text-error" *ngIf="f.valid_to?.touched && f.valid_to.hasError('invalidRange')">{{
                'validation.invalidRange' | translate }}</span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.status" | translate }}</label>
              <select formControlName="status" class="form-select">
                <option value="ACTIVE">ACTIVE</option>
                <option value="INACTIVE">INACTIVE</option>
              </select>
              <span class="text-error" *ngIf="f.status?.touched && f.status.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label">Role</label>
              <select formControlName="role" class="form-select" *ngIf="roleHierarchy.includes(f?.role?.value)"
                (change)="changeUserRole()">
                <ng-container *ngFor="let ur of userRoles">
                  <option [value]="ur.id" *ngIf="roleHierarchy.includes(ur.id)">{{ur.role_name}}</option>
                </ng-container>
              </select>
              <select class="form-select" *ngIf="!roleHierarchy.includes(f?.role?.value)" disabled>
                <option *ngFor="let ur of userRoles" [value]="ur.id" [selected]="f?.role?.value === ur.id">
                  {{ur.role_name}}</option>
              </select>
              <span class="text-error" *ngIf="f.role?.touched && f.role.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
            </div>
          </div>
        </form>
      </asar-tab>
      <asar-tab [tabTitle]="'Customer Details'">
        <form [formGroup]="generalForm">
          <div class="row">
            <div class="col-10">
              <div class="form-group mb-3">
                <label class="input-label">Customer ID</label>
                <ng-select [items]="customers$ | async" bindLabel="customer_id" [multiple]="userRoleType!=='STOREFRONT'"
                  [hideSelected]="true" [loading]="customerLoading" [minTermLength]="0" [typeahead]="customerInput$"
                  formControlName="customers" (add)="onAddCustOption($event)" [maxSelectedItems]="10"
                  [placeholder]="userRoleType!=='STOREFRONT' ? 'Select \'ALL\' or enter 2 or more chars to search customer' : ''"
                  typeToSearchText="Enter 2 or more chars to search customer">
                  <ng-template ng-option-tmp let-item="item">
                    <span>{{ item.customer_id }}</span>
                    <span *ngIf="item.customer_name">: {{ item.customer_name }}</span>
                  </ng-template>
                </ng-select>
              </div>
            </div>
            <div class="col-2">
              <ng-container *ngIf="userRoleType!=='STOREFRONT'">
                <div>&nbsp;</div>
                <button type="button" class="btn btn-danger" (click)="removeUserAllCustomer()">Remove ALL</button>
              </ng-container>
            </div>
          </div>
        </form>
        <app-manage-user-customer [model]="model" [userRoleType]="userRoleType"></app-manage-user-customer>
      </asar-tab>
      <asar-tab [tabTitle]="'Password'">
        <form [formGroup]="passwordForm">
          <div class="row">
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.password" | translate }}</label>
              <input class="form-control" type="password" formControlName="password" />
              <span class="text-error" *ngIf="f.password?.touched && f.password.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group mb-3">
              <label class="form-label"> {{ "backoffice.contact.confirmPassword" | translate }}</label>
              <input class="form-control" type="password" formControlName="confirmPassword" />
              <span class="text-error" *ngIf="f.confirmPassword?.touched && f.confirmPassword.hasError('required')">
                {{ 'validation.required' | translate }}
              </span>
              <span class="text-error"
                *ngIf="f.confirmPassword?.touched && !f.confirmPassword.hasError('required') && currentForm.errors && currentForm.errors?.passwordNotMatch">
                {{ 'validation.passwordNotMatched' | translate }}
              </span>
            </div>
          </div>
        </form>
      </asar-tab>
    </asar-tabs>
  </div>
</div>