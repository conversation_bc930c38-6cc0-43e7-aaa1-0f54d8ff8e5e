import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";

@Component({
  selector: "app-btn-del-renderer",
  templateUrl: "./btn-del-renderer.component.html",
  styleUrls: ["./btn-del-renderer.component.scss"],
})
export class BtnDelRendererComponent implements ICellRendererAngularComp {
  private params: any;
  public label: string;

  agInit(params: any): void {
    this.params = params;
    this.label = this.params.label || null;
  }

  refresh(params?: any): boolean {
    return true;
  }

  onClick($event: any) {
    if (this.params.onClick instanceof Function) {
      // put anything into params u want pass into parents component
      const params = {
        event: $event,
        rowData: this.params.node.data,
        // ...something
      };
      this.params.onClick(params);
    }
  }
}
