label.tick {
	margin: 0 0 10px 0;
	padding: 5px 9px 5px 30px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 0 6px;
	border: 1px solid #f1f1f1;
	border-radius: 7px;
	cursor: pointer;
	transition: all 0.3s ease-in-out;
	position: relative;
	z-index: 1;
	overflow: hidden;
	height: 30px;

	span:not(.material-icons-outlined) {
		position: absolute;
		content: '';
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border: 1px solid #146ebe;
		background: #146ebe;
		color: var(--snjy-color-white);
		z-index: -1;
		transition: all 0.3s ease-in-out;
		opacity: 0;
	}

	input {
		position: absolute;
		left: 0px;
		top: 0;
		bottom: 0;
		margin: auto 0;
		display: none;
	}

	.material-icons-outlined {
		padding: 0;
		position: absolute;
		left: 9px;
		top: 0;
		bottom: 0;
		margin: auto 0;
		width: 16px;
		height: 16px;
		opacity: 1;
		font-size: var(--snjy-font-size-1);
		transition: all 0.3s ease-in-out;
	}

	.material-icons-outlined.checked {
		opacity: 0;
	}

	input[type=checkbox]:checked~.material-icons-outlined.checked {
		opacity: 1;
		color: var(--snjy-color-white);
	}

	.material-icons-outlined.unchecked {
		opacity: 1;
	}

	input[type=checkbox]:checked~.material-icons-outlined.unchecked {
		opacity: 0;
	}

	input[type=checkbox]:checked~span {
		opacity: 1;
	}

	small {
		font-size: var(--snjy-font-size-0-75);
		font-weight: var(--snjy-font-weight-medium);
		color: #58606a;
		position: absolute;
		left: 30px;
	}

	small.title-label {
		left: 10px !important;
		color: var(--snjy-button-color-primary) !important;
	}

	input[type=checkbox]:checked~small {
		color: var(--snjy-color-white);
	}
}

form.child-brand {
	padding: 0 0 0 22px;
}

.facet {
	.section {
		border-bottom: 1px solid #d7d7d7;
		position: relative;

		h6 {
			margin-bottom: 15px;
			padding: 0;
			font-size: var(--snjy-font-size-0-875);
			font-weight: var(--snjy-font-weight-bold);
			color: var(--snjy-color-dark-secondary);
			position: relative;
			text-transform: uppercase;
		}

		a {
			color: var(--snjy-button-color-primary);
			margin-top: 15px;
			display: flex;
			width: 100%;
			height: 30px;
			align-items: center;
			justify-content: space-evenly;
			background: #f5f9ff;
			border: 1px solid rgba(27, 126, 205, 0.3411764706);
			border-radius: 50px;
			font-size: var(--snjy-font-size-0-8);
			font-weight: var(--snjy-font-weight-medium);
			text-decoration: none;
			cursor: pointer;
		}
	}
}