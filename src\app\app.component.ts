import { Component, OnInit } from "@angular/core";
import {
  Router,
  NavigationStart,
  NavigationEnd,
  NavigationCancel,
  NavigationError,
} from "@angular/router";

import { LoadingService } from "./shared/components/loading-bar/loading.service";
import { AppToastService } from "./shared/services/toast.service";

@Component({
  selector: "app-root",
  templateUrl: "./app.component.html",
  styleUrls: ["./app.component.scss"],
})
export class AppComponent implements OnInit {
  constructor(
    private _snackBar: AppToastService,
    private loadingService: LoadingService,
    public router: Router
  ) {
    // Display error message while user try to login with "Microsoft SSO" button.
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has("msalerror")) {
      const msalerror: any = urlParams.get("msalerror");
      this._snackBar.open(msalerror, { type: "Error" });
    }
  }

  ngOnInit(): void {
    this.router.events.subscribe((ev) => {
      if (ev instanceof NavigationStart) {
        this.loadingService.show();
      }
      if (
        ev instanceof NavigationEnd ||
        ev instanceof NavigationCancel ||
        ev instanceof NavigationError
      ) {
        this.loadingService.hide();
      }
    });
  }
}
