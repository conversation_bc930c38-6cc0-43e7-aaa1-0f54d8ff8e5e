import { Pipe, PipeTransform } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { lastValueFrom, map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Pipe({
  name: "getCompanyLogo",
})
export class GetCompanyLogoPipe implements PipeTransform {
  constructor(private http: HttpClient) {}

  async transform(value: unknown, ...args: unknown[]) {
    return await lastValueFrom(
      this.http
        .get<any>(`${ApiConstant.COMPANY_LOGO}`)
        .pipe(
          map((res) => {
            if (res?.url) {
              return res?.url;
            }
            return "/assets/images/monster-energy-logo.png";
          })
        )
    );
  }
}
