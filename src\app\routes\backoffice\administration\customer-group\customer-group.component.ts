import { Component } from "@angular/core";
import { Subject, takeUntil } from "rxjs";
import { CustomerGroupService } from "./customer-group.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-customer-group",
  templateUrl: "./customer-group.component.html",
  styleUrls: ["./customer-group.component.scss"],
})
export class CustomerGroupComponent {
  private ngUnsubscribe = new Subject<void>();
  public custGroup: any[] = [];
  public activeId = 0;
  public placements: any[] = [
    { name: "Home", value: "HOME" },
    { name: "Customer Service", value: "CUST_SERVICE" },
    { name: "Sales", value: "SALES" },
    { name: "Vendor", value: "VENDOR" },
  ];

  constructor(
    private custGroupService: CustomerGroupService,
    private _snackBar: AppToastService
  ) {}

  ngOnInit(): void {
    this.getSalesAreas();
  }

  getSalesAreas() {
    this.custGroupService
      .getSalesAreas()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.custGroup = res || [];
        },
        error: () => {
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  toggleAccordian(data: any, event: any, index: number) {
    const element = event.target;
    element.classList.toggle("active");
    if (data[index].isActive) {
      data[index].isActive = false;
    } else {
      data[index].isActive = true;
    }

    const panel = element.parentElement.nextElementSibling;

    if (panel.style.maxHeight) {
      panel.style.maxHeight = null;
    } else {
      panel.style.maxHeight = panel.scrollHeight + "px";
    }
  }

  navChange(e: any) {
    this.custGroup = this.custGroup.map((o: any) => {
      o.isActive = false;
      return o;
    });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
