import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { SharedModule } from "src/app/shared/shared.module";
import { SalesOrderSchedulerConfirmationComponent } from "./sales-order-scheduler-confirmation.component";

@NgModule({
  declarations: [SalesOrderSchedulerConfirmationComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([
      { path: "", component: SalesOrderSchedulerConfirmationComponent },
    ]),
  ],
})
export class SalesOrderSchedulerConfirmationModule {}
