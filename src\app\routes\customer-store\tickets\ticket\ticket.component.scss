.seller-detail-title {
  color: var(--snjy-color-text-secondary);
  font-size: var(--snjy-font-size-0-875);
  font-weight: var(--snjy-font-weight-bold);
  font-family: var(--snjy-font-family);
}

::ng-deep {
  .mat-mdc-form-field-infix,
  .mat-mdc-text-field-wrapper {
    padding: 0 !important;
  }

  .mat-mdc-form-field-subscript-wrapper,
  .mdc-line-ripple {
    display: none;
  }

  .mat-mdc-form-field-infix {
    min-height: 44px !important;
  }
}

.associate-input {
  margin: 0;
  padding: 0 11px;
  height: 44px !important;
  background: #f0f7ff;
  border-radius: 8px;
  font-size: 14px !important;
  font-weight: var(--snjy-font-weight-medium) !important;
  border: 1px solid rgba(134, 153, 169, 0.3803921569) !important;
}

.seller-detail-details {
  color: #5e79a1;
  font-size: var(--snjy-font-size-0-75);
  font-family: var(--snjy-font-family);
}

.search-container {
  background-color: var(--snjy-color-main-background-ternary);

  .form-label {
    color: #5e79a1;
    font-size: var(--snjy-font-size-0-875);
    font-family: var(--snjy-font-family);
  }

  select,
  input.form-control {
    border: 1px solid var(--snjy-border-color);
    box-shadow: 0px 3px 3px rgba(0, 0, 0, 0.03);
    padding: 0.75rem;
    color: var(--snjy-font-color-seconday);
    font-family: var(--snjy-font-family-ternary);
  }

  mat-form-field {
    display: block;
    background-color: var(--snjy-color-main-background);
  }
}

table {
  min-width: 700px;

  thead {
    background: var(--snjy-color-text-secondary);
    color: var(--snjy-color-main-background);
    font-family: var(--snjy-font-family);

    th {
      padding: 1rem 2rem;
    }
  }

  td {
    font-size: var(--snjy-font-size-0-875);
    padding: 2.5rem 2rem;
    color: var(--snjy-color-dark-secondary);
    font-family: var(--snjy-font-family);
    font-weight: var(--snjy-font-weight-medium);

    .circle {
      height: 12px;
      width: 12px;
      border-radius: 50%;
      display: inline-block;

      &.Cancelled {
        background-color: red;
      }

      &.Completed {
        background-color: green;
      }
    }
  }

  tbody tr {
    cursor: pointer;
  }
}

/*-----------------ORDER STATUS SEC--------------*/
.order-status-sec {
  margin: 0;
  padding: 0;
  position: relative;
}

.order-status-sec .order-status-body {
  margin: 0 auto;
  padding: 0 25px 50px 25px;
  position: relative;
  max-width: 100%;
}

.order-contact-list {
  margin: 0;
  padding: 18px 20px;
  background: #e5ecf3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);
}

.order-contact-list .order-c-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0 10px;
  flex: 1;
}

.order-c-box.address-box {
  flex: 0 0 42% !important;
}

.order-contact-list .order-c-box .order-c-icon {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--snjy-font-color-primary);
  border-radius: 50px;
  box-shadow: 0 1px 3px rgba(97, 134, 177, 0.1882352941);
}

.order-c-details {
  margin: 0;
  padding: 6px 0 0 0;
}

.order-c-details h4 {
  margin: 0;
  padding: 0;
  font-size: var(--snjy-font-size-0-8);
  text-transform: uppercase;
  font-weight: var(--snjy-font-weight-bold);
  color: var(--snjy-button-color-primary);
  line-height: 13px;
}

.order-c-details small {
  margin: 0;
  padding: 0;
  font-size: var(--snjy-font-size-0-8);
  text-transform: uppercase;
  font-weight: var(--snjy-font-weight-medium);
  color: #2e3237;
  line-height: 20px;
}

.order-status-form {
  margin: 15px 0 0 0;
  padding: 30px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);
}
.order-status-form h3 {
  margin: 0 0 15px 0;
  padding: 0;
  position: relative;
  font-size: var(--snjy-font-size-1-125);
  font-weight: var(--snjy-font-weight-bold);
  color: #00216c;
  line-height: 20px;
}
.order-status-form>p {
  margin: 0 0 25px 0;
  padding: 0;
  position: relative;
  font-size: var(--snjy-font-size-0-9);
  font-weight: var(--snjy-font-weight-semi-bold);
  color: #999999;
  line-height: 20px;
}
.order-status-form .form {
  display: flex;
  justify-content: space-between;
  gap: 0 2%;
  flex-wrap: wrap;
}


.warranty-form form {
  flex: 1 !important;
}

.f-full-width {
  flex: 0 0 100% !important;
}

.order-status-form .form .form-group:before {
  position: absolute;
  content: "";
  font-family: "Material Icons Outlined";
  top: 39px;
  right: 10px;
  bottom: 0;
  margin: auto 0;
  font-size: var(--snjy-font-size-1-25);
  color: var(--snjy-color-dark-secondary);
  font-weight: var(--snjy-font-weight-normal);
  line-height: 23px;
}

.order-status-form .form .form-group label {
  margin: 0 0 8px 0;
  padding: 0;
  font-size: var(--snjy-font-size-0-875);
  font-weight: var(--snjy-font-weight-medium);
  color: var(--snjy-color-dark-secondary);
  line-height: 14px;
  display: flex;
  align-items: center;
  gap: 0 2px;
}

.order-status-form .form .form-group label .material-icons-outlined {
  margin: 0;
  padding: 0;
  font-size: var(--snjy-font-size-1-25);
  color: #b3b3b3;
  width: fit-content;
  height: fit-content;
}

.order-status-form .form .form-group .form-control {
  margin: 0;
  padding: 0 11px;
  height: 44px;
  background: #f0f7ff;
  border-radius: 8px;
  font-size: var(--snjy-font-size-0-875);
  font-weight: var(--snjy-font-weight-medium);
  border: 1px solid #8699a961;
}

.order-status-form .form .form-group textarea.form-control {
  height: 120px;
}

.order-status-form .form .form-group .form-control::placeholder {
  color: #a1a1a1;
  font-weight: var(--snjy-font-weight-semi-bold);
}

.order-status-form .form .form-group select.form-control {
  -webkit-appearance: none;
}

.order-status-form .form .form-group:before {
  position: absolute;
  content: "";
  font-family: "Material Icons Outlined";
  top: 39px;
  right: 10px;
  bottom: 0;
  margin: auto 0;
  font-size: var(--snjy-font-size-1-25);
  color: #b7b7b7;
  font-weight: var(--snjy-font-weight-normal);
  line-height: 23px;
}

.order-status-form .form .form-group.o-calendar:before {
  content: "\ebcc";
}

.order-status-form .form .form-group.p-o-num:before {
  content: "\f0c5";
  top: 40px;
}

.order-status-form .form .form-group.o-num:before {
  content: "\f045";
  top: 40px;
}

.order-status-form .form .form-group.o-status:before {
  content: "\e5cf";
  color: var(--snjy-button-color-primary);
  font-size: 26px;
  line-height: 23px;
  height: -moz-fit-content;
  height: fit-content;
  top: 28px;
}

.order-status-form .form .form-btn-sec {
  margin: 15px 0 0 0;
  flex: 0 0 100%;
  max-width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 0 2%;
}

.order-s-btn {
  border-radius: 10px;
  display: flex;
  font-size: var(--snjy-font-size-0-875);
  height: 48px;
  align-items: center;
  justify-content: center;
  line-height: 14px;
  min-width: 263px;
  padding-bottom: 0;
  padding-top: 0;
  cursor: pointer;
  color: var(--snjy-color-white);
  background: var(--snjy-button-gradient);
  position: relative;
  text-transform: uppercase;
  font-weight: var(--snjy-font-weight-medium);
  transition: all0 0.3s ease-in-out;
  border: none;
}

.order-s-btn.back-btn {
  background: linear-gradient(45deg, #bbbbbb, #afafaf);
}

/*--------------ORDER STATUS SEC------------*/

.file-input {
  display: none;
}

.red {
  color: red;
}

.upload-file-link {
  border-radius: 8px;
  margin: 0 !important;
  padding: 0 40px;
  min-height: 44px;
  font-family: var(--snjy-font-family);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0 10px;
  font-size: var(--snjy-font-size-0-875);
  line-height: 16px;
  font-weight: var(--snjy-font-weight-medium) !important;
  background-color: var(--snjy-font-color-primary) !important;
  color: var(--snjy-button-color-primary) !important;
  border: 1px solid var(--snjy-button-color-primary) !important;
  width: 100%;
}

.file-name-container {
  width: 100%;
  max-width: 300px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .text-truncate {
    width: 100%;
    display: inline-block;
  }
}
