.bck-table-body .ag-root-wrapper {
    border: none;

    .ag-root.ag-layout-normal {
        border-radius: 7px;
        border: 1px solid #d6dbe3 !important;
        box-shadow: 0 1px 3px rgba(163, 163, 163, 0.431372549);
    }

    .ag-header {
        border: none !important;
        background: #e6e6e6;
        margin: 0 0 2px 0;
        border-bottom: 1px solid #0b1c331c !important;
        box-shadow: 0 1px 2px rgb(11 28 51 / 15%);

        span:not(.material-icons-outlined).ag-header-cell-text {
            font-size: var(--snjy-font-size-0-8);
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-color-dark-secondary);
        }

        span:not(.material-icons-outlined).ag-icon {
            color: #858585;
        }

        .ag-icon-menu {
            color: var(--snjy-button-color-primary);
        }
    }

    .ag-body-viewport {
        padding: 0;

        .ag-row {
            background: var(--snjy-font-color-primary);
            overflow: hidden;

            .ag-cell {
                line-height: 35px;
                font-size: var(--snjy-font-size-0-75);
                font-weight: var(--snjy-font-weight-medium);
                color: #515151;

                &:first-child {
                    color: var(--snjy-button-color-primary);
                }
            }
        }
    }

    .ag-paging-panel {
        justify-content: center;
        margin: 20px 0;
        border: none;

        span:not(.material-icons-outlined).ag-paging-row-summary-panel {
            display: none;
        }

        .ag-paging-page-summary-panel {
            gap: 0 5px;

            .ag-paging-button {
                margin: 0;
                padding: 0 8px;
                height: 30px !important;
                width: fit-content;
                background: #d4d6d9;
                display: inline-flex;
                align-items: center;
                border-radius: 5px;
            }

            .ag-paging-description {
                margin: 0;
                padding: 0 14px;
                height: 30px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 0 4px;
                background: var(--snjy-font-color-primary);
                color: var(--snjy-button-color-primary);
                border-radius: 6px;
                border: 1px solid #d4d6d9;
                box-shadow: 0 2px 2px #00000014;
            }
        }
    }
}