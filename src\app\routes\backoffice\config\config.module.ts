import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { ConfigComponent } from './config.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { CatalogComponent } from './catalog/catalog.component';
import { CategoriesComponent } from './categories/categories.component';

@NgModule({
  declarations: [
    ConfigComponent,
    CatalogComponent,
    CategoriesComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: '', component: ConfigComponent }]),
  ]
})
export class ConfigModule { }
