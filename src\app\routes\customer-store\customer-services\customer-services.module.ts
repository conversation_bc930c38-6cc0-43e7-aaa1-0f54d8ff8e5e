import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { CustomerServicesComponent } from "./customer-services.component";
import { SharedModule } from "src/app/shared/shared.module";
import { CustomerServiceDashboardComponent } from "./customer-service-dashboard/customer-service-dashboard.component";
import { CustomerServiceReportsComponent } from "./customer-service-reports/customer-service-reports.component";

const routes: Routes = [
  { path: "", component: CustomerServicesComponent },
  { path: "dashboard", component: CustomerServiceDashboardComponent },
  { path: "reports", component: CustomerServiceReportsComponent }
];

@NgModule({
  declarations: [
    CustomerServicesComponent,
    CustomerServiceDashboardComponent,
    CustomerServiceReportsComponent,
  ],
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
})
export class CustomerServicesModule {}
