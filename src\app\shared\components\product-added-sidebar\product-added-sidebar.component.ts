import {
  Component,
  ElementRef,
  On<PERSON>nit,
  <PERSON><PERSON><PERSON>roy,
  Renderer2,
} from "@angular/core";
import { Subject, takeUntil } from "rxjs";
import { Router } from "@angular/router";
import { CartService } from "src/app/routes/customer-store/services/cart.service";

@Component({
  selector: "app-product-added-sidebar",
  templateUrl: "./product-added-sidebar.component.html",
  styleUrls: ["./product-added-sidebar.component.scss"],
})
export class ProductAddedSidebarComponent implements OnInit, OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  public isExapndable = false;
  public product: any = null;
  public loading: any = false;

  constructor(
    private elRef: ElementRef,
    private renderer: Renderer2,
    public router: Router,
    private cartService: CartService
  ) {}

  ngOnInit(): void {
    this.cartService
      .getMaterial()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((id: any) => {
        if (id) {
          this.isExapndable = true;
          this.loading = true;
          this.product = null;
          this.enableBackdrop();
        }
      });
    this.cartService
      .getProduct()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((product) => {
        if (product) {
          this.product = product;
          this.loading = false;
        }
      });
  }

  enableBackdrop() {
    this.elRef.nativeElement.addEventListener(
      "click",
      this.onBackdropClick.bind(this)
    );
    this.renderer.setStyle(this.elRef.nativeElement, "width", "100%");
    this.renderer.setStyle(this.elRef.nativeElement, "height", "100%");
    this.renderer.setStyle(this.elRef.nativeElement, "position", "fixed");
    this.renderer.setStyle(this.elRef.nativeElement, "left", "0");
    this.renderer.setStyle(this.elRef.nativeElement, "top", "0");
    this.renderer.setStyle(this.elRef.nativeElement, "z-index", "9999");
    this.renderer.setStyle(
      this.elRef.nativeElement,
      "background",
      "rgba(0,0,0,0.5)"
    );
  }

  disableBackdrop() {
    this.renderer.setAttribute(this.elRef.nativeElement, "style", "");
  }

  onBackdropClick(event: any) {
    if (this.product) {
      this.reset();
    }
  }

  reset() {
    this.disableBackdrop();
    this.product = null;
    this.isExapndable = false;
    this.loading = false;
    this.cartService.setMaterial(null);
  }

  goToCart() {
    this.reset();
    this.router.navigate([`store/cart`]);
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
