import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { CartService } from "../services/cart.service";
import { AbstractControl, FormBuilder, Validators } from "@angular/forms";
import { CustomerService } from "../../backoffice/customer/customer.service";
import { lastValueFrom } from "rxjs";
import { SettingsService } from "../../backoffice/settings/settings.service";
import {
  NgbCalendar,
  NgbDateStruct,
  NgbModal,
} from "@ng-bootstrap/ng-bootstrap";
import { OrderHistoryService } from "../order-history/order-history.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { SalesOrdersSchedulerComponent } from "src/app/shared/components/sales-orders-scheduler/sales-orders-scheduler.component";

@Component({
  selector: "app-checkout",
  templateUrl: "./checkout.component.html",
  styleUrls: ["./checkout.component.scss"],
})
export class CheckoutComponent implements OnInit {
  public sosRes: any = null;
  public steps = 3;
  public currentStep = 1;
  public addresses: any = [];
  public checkoutForm: any;
  public submitted = false;
  public isOrderPlaced = false;
  public isSOScheduled = false;
  public settings: any = null;
  public minRequestedDeliveryDate: NgbDateStruct;
  private isShippingChange: any = false;

  constructor(
    public fb: FormBuilder,
    public router: Router,
    private dialog: NgbModal,
    private _snackBar: AppToastService,
    private cartService: CartService,
    private settingsService: SettingsService,
    private customerService: CustomerService,
    calendarService: NgbCalendar,
    private orderHistoryService: OrderHistoryService
  ) {
    this.minRequestedDeliveryDate = calendarService.getToday();
  }

  ngOnInit(): void {
    this.createCheckoutForm();
    this.getOrderTypes();
    this.getSettings();
    this.getSalesOrderSimulation();
  }

  async next() {
    this.submitted = true;
    if (this.checkoutForm.invalid) {
      return;
    }
    this.currentStep++;
    if (this.isShippingChange && this.currentStep > 2) {
      this.isShippingChange = false;
      this.isOrderPlaced = true;
      this.isSOScheduled = true;
      this.sosRes = await this.cartService.salesOrderSimulation();
      this.isOrderPlaced = false;
      this.isSOScheduled = false;
    }
  }

  back() {
    this.currentStep--;
  }

  async confirm() {
    try {
      this.isOrderPlaced = true;
      const obj = this.checkoutForm.value;
      if (obj.RequestedDeliveryDate) {
        obj.RequestedDeliveryDate = new Date(
          obj.RequestedDeliveryDate["year"],
          obj.RequestedDeliveryDate["month"] - 1,
          obj.RequestedDeliveryDate["day"]
        );
      }
      await this.cartService.salesOrderCreation(this.checkoutForm.value);
      this.router.navigate([`store/checkout-confirm`]);
    } catch (e: any) {
      this.isOrderPlaced = false;
      if (e?.error?.message) {
        this._snackBar.open(e?.error?.message, {
          type: "Error",
        });
      } else {
        this._snackBar.open("Error while creating shipping address.", {
          type: "Error",
        });
      }
    }
  }

  createCheckoutForm() {
    this.checkoutForm = this.fb.group({
      paymentType: ["Account Payment"],
      PurchaseOrderByCustomer: ["", Validators.required],
      RequestedDeliveryDate: [],
      shippingAddress: [],
      specialInstructions: [""],
      textCode: [""],
      salesOrderTypeCode: ["OR"],
    });
  }

  get f(): { [key: string]: AbstractControl } {
    return this.checkoutForm.controls;
  }

  onChangeshipping() {
    this.isShippingChange = true;
    const shippingAddress = this.f["shippingAddress"].value;
    const toPartner = [
      {
        PartnerFunction: "SH",
        Customer: shippingAddress.bp_customer_number,
      },
    ];
    this.setToPartner(toPartner);
  }

  setToPartner(toPartner: any) {
    const sosReq: any = this.cartService.getSOSReq();
    sosReq.to_Partner = toPartner;
    this.cartService.setSOSReq(sosReq);
  }

  getOrderTypes() {
    this.orderHistoryService.getAllOrderType().subscribe({
      next: (res: any) => {
        const data: any = res?.data || [];
        const docType: any = data.find((o: any) => o.is_creation);
        if (docType) {
          this.checkoutForm.patchValue({
            salesOrderTypeCode: docType?.code,
          });
        }
      },
      error: (e: any) => {
        console.error("Error while processing order type GET request.", e);
      },
    });
  }

  getSettings() {
    this.settingsService.getSettings().subscribe({
      next: (data: any) => {
        this.settings = data;
        this.checkoutForm.patchValue({ textCode: data?.text_code });
      },
      error: (e) => {
        console.error("Error while processing settings request.", e);
      },
    });
  }

  async getSalesOrderSimulation() {
    this.setToPartner([]);
    this.sosRes = await this.cartService.salesOrderSimulation();
    this.getAddresses(this.sosRes.SoldToParty);
  }

  async getAddresses(customerID: string) {
    const addresses = await lastValueFrom(
      this.customerService.getPartnerFunction(customerID)
    );
    this.addresses = addresses.filter((o) => o.partner_function === "SH");
    const shippingAddress = this.addresses[0] || null;
    this.checkoutForm.patchValue({ shippingAddress });
  }

  trackBy(index: number, product: any) {
    return product.Material;
  }

  openDialog() {
    const dialogRef = this.dialog.open(SalesOrdersSchedulerComponent, {
      modalDialogClass: "sales-orders-scheduler-modal",
    });
    dialogRef.result.then((res) => {
      if (res.status === "success") {
        this.createSalesOrderScheduler(res.data);
      }
    });
  }

  async createSalesOrderScheduler(data: any) {
    try {
      this.isSOScheduled = true;
      const obj = this.checkoutForm.value;
      if (obj.RequestedDeliveryDate) {
        obj.RequestedDeliveryDate = new Date(
          obj.RequestedDeliveryDate["year"],
          obj.RequestedDeliveryDate["month"] - 1,
          obj.RequestedDeliveryDate["day"]
        );
      }
      await this.cartService.createSalesOrderScheduler(
        this.checkoutForm.value,
        data
      );
      this.isSOScheduled = false;
      this.router.navigate([`store/sales-order-scheduler-confirm`]);
    } catch (e: any) {
      this.isSOScheduled = false;
      if (e?.error?.message) {
        this._snackBar.open(e?.error?.message, {
          type: "Error",
        });
      } else {
        this._snackBar.open("Error while creating shipping address.", {
          type: "Error",
        });
      }
    }
  }
}
