.navigation-panel {
  background-color: #0b1c33;

  .fixed-sidebar {
    background: #000c1b;
    width: 60px;
    box-shadow: 1px 0px 2px rgba(42, 52, 59, 0.04);

    .toggler {
      height: 75px;

      .material-icons-outlined {
        font-size: var(--snjy-font-size-3);
        width: fit-content;
        height: fit-content;
        color: rgb(255 255 255 / 70%);

        &:hover {
          color: var(--snjy-button-color-primary);
        }
      }
    }

    ul {
      height: calc(100vh - 75px);

      li {
        margin: 0 0 10px 0;
        list-style: none;
        cursor: pointer;

        &:nth-child(2) {
          margin-top: auto;
        }

        a {
          font-size: var(--snjy-font-size-0-75);
          font-weight: var(--snjy-font-weight-normal);
          color: var(--snjy-color-white);

          .material-icons-outlined {
            margin: 0 auto 3px auto;
            font-size: var(--snjy-font-size-1-25);
            color: var(--snjy-color-white);
            height: 32px;
            width: 32px;
            background: var(--snjy-button-color-primary);
            border-radius: 5px;
          }

          span:not(.material-icons-outlined) {
            color: rgb(255 255 255 / 75%);
            font-weight: var(--snjy-font-weight-semi-bold);
          }
        }

        &:hover a span {
          color: rgb(255 255 255 / 100%);
        }

        &:nth-child(2) a .material-icons-outlined {
          background: #40a978;
        }

        &:nth-child(3) a .material-icons-outlined {
          background: #d32f2f;
        }
      }
    }

  }

  .navigation-panel-menu {
    padding: 24px;

    li {
      list-style: none;
      margin: 0 0 10px 0;
      border-radius: 6px;
      background: rgb(18 36 60);
      border: 1px solid rgba(255, 255, 255, .1490196078);

      &.sub-link {
        border: none;
      }
      
      a {
        padding: 8px 12px;
        border-radius: 6px;
        display: flex;
        transition: background 0.2s;
        font-size: var(--snjy-font-size-0-8);
        color: rgb(255 255 255 / 76%);
        font-weight: var(--snjy-font-weight-semi-bold);
        align-items: center;
        justify-content: space-between;

        .material-icons-outlined {
          font-size: var(--snjy-font-size-1-25);
          color: var(--snjy-font-color-primary);
          width: fit-content;
          height: fit-content;
        }

        &:hover {
          background: var(--snjy-button-color-primary);
          color: var(--snjy-font-color-primary) !important;

          .material-icons-outlined {
            color: var(--snjy-color-white);
          }
        }
      }

      .active {
        background: var(--snjy-button-color-primary);
        color: var(--snjy-font-color-primary) !important;

        .material-icons-outlined {
          color: var(--snjy-color-white);
        }
      }
    }
  }
}