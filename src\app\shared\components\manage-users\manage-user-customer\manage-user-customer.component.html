<div class="grid-toolbar d-flex justify-content-end align-items-center pb-3">
    <input type="text" [ngModel]="searchText" class="form-control me-2 mb-0"
        placeholder="Search by...Contains, Starts with, Equal to" (ngModelChange)="onSearchChange($event)">
    <select [ngModel]="searchBy" (ngModelChange)="onSearchByChange($event)" class="form-select me-2 mb-0"
        aria-label="Default select example">
        <option value="customer_id">Customer ID</option>
        <option value="customer_name" selected>Customer Name</option>
        <option value="cpf_address">Address</option>
        <option value="phone">Phone</option>
    </select>
    <app-grid-column #columnMenu class="btn-column-sec" [columns]="columnDefs" buttonText="Columns"
        selectableChecked="show" (columnChange)="_onColumnChange($event)"
        (columnPositionChange)="_columnPositionChange($event)">
    </app-grid-column>
</div>
<ag-grid-angular [getRowId]="getRowId" style="width: 100%; height: 400px;" class="ag-theme-alpine bck-table-body"
    [columnDefs]="columnDefs" [defaultColDef]="defaultColDef" [pagination]="true" [paginationPageSize]="10"
    [gridOptions]="gridOptions" (gridReady)="onGridReady($event)" [paginationPageSizeSelector]="[10, 20, 50, 100]"></ag-grid-angular>