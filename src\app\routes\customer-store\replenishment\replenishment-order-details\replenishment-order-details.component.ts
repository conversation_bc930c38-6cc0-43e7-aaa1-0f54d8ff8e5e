import { Component, OnDestroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { CustomerService } from 'src/app/routes/backoffice/customer/customer.service';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { OrderDetailsService } from '../../order-details/order-details.service';
import { SettingsService } from 'src/app/routes/backoffice/settings/settings.service';
import { Subject, takeUntil } from 'rxjs';
import { SalesOrderService } from '../../services/sales-order/sales-order.service';

@Component({
  selector: 'app-replenishment-order-details',
  templateUrl: './replenishment-order-details.component.html',
  styleUrls: ['./replenishment-order-details.component.scss']
})
export class ReplenishmentOrderDetailsComponent implements OnDestroy {
  public loading = false;
  public orderDetails: any = {};
  public order: any = {};
  public moment = moment;
  public customer: any = null;
  public shipToParty: any = null;
  public settings: any = null;
  private ngUnsubscribe = new Subject<void>();

  constructor(
    public router: Router,
    private service: OrderDetailsService,
    private _snackBar: AppToastService,
    private activatedRoute: ActivatedRoute,
    private customerService: CustomerService,
    private salesOrderService: SalesOrderService,
    private settingsService: SettingsService
  ) {  }

  ngOnInit() {
    this.getSettings();
    this.activatedRoute.paramMap.subscribe((params) => {
      const id = params.get("replenishment-order-id");
      if (id) {
        this.getOrderDetails(id);
      }
    });
  }

  getSettings() {
    this.settingsService.getSettings().subscribe({
      next: (data: any) => {
        this.settings = data;
      },
      error: (e) => {
        console.error("Error while processing settings request.", e);
      },
    });
  }

  getPartnerFunction(soldToParty: string, shipToParty: string) {
    this.customerService.getPartnerFunction(soldToParty).subscribe({
      next: (value: any) => {
        this.customer = value.find(
          (o: any) =>
            o.customer_id === soldToParty && o.partner_function === "SP"
        );
        this.shipToParty = value.find(
          (o: any) =>
            o.bp_customer_number === shipToParty && o.partner_function === "SH"
        );
      },
      error: (err) => {
        this._snackBar.open("Error while processing get ship to request.", { type: 'Error' });
      },
    });
  }

  getOrderDetails(orderId: string) {
    this.loading = true;
    this.service.getScheduledOrderDetail(orderId).subscribe({
      next: (value: any) => {
        this.orderDetails = value.data;
        this.getPartnerFunction(this.orderDetails?.customer_id, this.orderDetails?.req_data.to_Partner[0].Customer);
        this.getSalesOrderSimulation(value.data);
      },
      error: (err) => {
        this.loading = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  getSalesOrderSimulation(data: any) {
    const sosReq = { ...data.req_data };
    sosReq.to_Partner = [];
    sosReq.to_Pricing = {};
    delete sosReq.User;
    delete sosReq.to_Text;
    delete sosReq.RequestedDeliveryDate;
    this.salesOrderService
      .salesOrderSimulation(sosReq)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.order =
            res?.data?.SALESORDERSIMULATE?.A_SalesOrderSimulationType || [];
          this.loading = false;
        },
        error: (err: any) => {
          this.loading = false;
          this._snackBar.open("Error while processing get customer request.", {
            type: "Error",
          });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
