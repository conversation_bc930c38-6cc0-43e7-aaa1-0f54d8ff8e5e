<ng-container *ngIf="!loading">
    <div class="all-main-title-sec product-details-main-title">
        <h1>Cart ID: {{cartDetails?.id}}</h1>
        <div class="all-bedcrumbs">
            <ul>
                <li>
                    <a [routerLink]="['/store/dashboard']">
                        <span class="material-icons-outlined">home</span> Home
                    </a>
                </li>
                <li>
                    <a [routerLink]="['/store/saved-cart']">
                        <span class="material-icons-outlined">shopping_cart</span>
                        Saved Carts
                    </a>
                </li>
                <li>Cart Details</li>
            </ul>
        </div>
    </div>

    <div class="order-id-sec">
        <div class="order-id-body">
            <div class="order-id-info">
                <div class="order-details">
                    <h3>Cart Details</h3>
                    <ul>
                        <li><span class="material-icons-outlined">pin</span> ID
                            <span>{{cartDetails?.id}}</span>
                        </li>
                        <li><span class="material-icons-outlined">pin</span> Name
                            <span>{{cartDetails?.name}}</span>
                        </li>
                        <li><span class="material-icons-outlined">event_note</span> Description
                            <span>{{cartDetails.description}}</span>
                        </li>
                        <li><span class="material-icons-outlined">shopping_bag</span> Items
                            <span>{{cartDetails.payload?.length || 0}}</span>
                        </li>
                        <li><span class="material-icons-outlined">person_outline</span> Date saved
                            <span>{{moment.utc(cartDetails?.created_at).local().format("MM/DD/YYYY HH:mm:ss A")}}</span>
                        </li>
                    </ul>
                    <div class="d-flex justify-content-between mt-3">
                        <button type="button" (click)="delete(cartDetails)" class="btn btn-light">Delete</button>
                        <div class="d-flex">
                            <button type="button" [routerLink]="['../..', 'saved-cart']" class="btn btn-light me-3">Back
                                to saved cart</button>
                            <button type="button" (click)="restore(cartDetails)" class="btn">Restore</button>
                        </div>
                    </div>
                </div>
                <div class="order-details">
                    <h3>Items</h3>
                    <div class="item-box" *ngFor="let item of cartDetails.payload">
                        <div class="item-box-img" [ngStyle]="{'background-image': 'url(' + (item.Material | getProductImage | async) + ')'}">
                        </div>
                        <div class="item-box-content">
                            <h4>{{item.SalesOrderItemText}}</h4>
                            <small>{{item.Material}}</small>
                            <div class="item-box-bottom-content">
                                <div class="quantity">Quantity: <span>{{item?.RequestedQuantity || 0}}</span></div>
                                <div class="item-price">{{ item.formatted_base_price }} <span>{{
                                        item.formatted_base_price_each  }} each</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-container>
<div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>