import { environment } from '../../environments/environment';

export const ApiConstant = {
    ORDER_HISTORY: `${environment.apiEndpoint}/orders`,
    GET_ALL_PRODUCTS: `${environment.apiEndpoint}/products`,
    GET_PRODUCT_IMAGES: `${environment.apiEndpoint}/products/{id}/media`,
    PRODUCT_IMAGE: `${environment.apiEndpoint}/media`,
    PRODUCT_PLANT: `${environment.apiEndpoint}/product-plants`,
    PRODUCT_DESCRIPTION: `${environment.apiEndpoint}/product-descriptions`,
    USERS: `${environment.apiEndpoint}/users`,
    SINGIN: `${environment.apiEndpoint}/auth/login`,
    SINGOUT: `${environment.apiEndpoint}/auth/logout`,
    PARTNERS: `${environment.apiEndpoint}/business-partners`,
    CUSTOMER_COMPANIES: `${environment.apiEndpoint}/customer-companies`,
    CUSTOMER_TEXT: `${environment.apiEndpoint}/customer-text`,
    CUSTOMER_PARTNER_FUNCTION: `${environment.apiEndpoint}/customer-partner-functions`,
    CUSTOMER_SALES_AREA: `${environment.apiEndpoint}/customer-sales-areas`,
    RESET_PASSWORD_REQUEST: `${environment.apiEndpoint}/auth/reset-password-request`,
    RESET_PASSWORD: `${environment.apiEndpoint}/auth/reset-password`,
    GET_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,
    GET_INVOICE_FORM_TYPES: `${environment.apiEndpoint}/invoice-form-types`,
    RELATIONSHIP_TYPES: `${environment.apiEndpoint}/relationship-types`,
    CONDITIONS: `${environment.apiEndpoint}/conditions`,
    GET_CATALOGS: `${environment.apiEndpoint}/product-catalogs`,
    GET_CATEGORIES: `${environment.apiEndpoint}/product-categories`,
    PRODUCT_CATEGORIES: `${environment.apiEndpoint}/products/{product_id}/categories`,
    PRODUCT_CATALOGS: `${environment.apiEndpoint}/products/{product_id}/catalogs`,
    GET_INVOICE_STATUSES: `${environment.apiEndpoint}/invoice-statuses`,
    GET_SALE_ORDER_STATUSES: `${environment.apiEndpoint}/sale-order-statuses`,
    GET_INVOICE_TYPES: `${environment.apiEndpoint}/invoice-types`,
    GET_ORDER_TYPES: `${environment.apiEndpoint}/order-types`,
    ORDER_DETAILS: `${environment.apiEndpoint}/orders`,
    SCHEDULED_ORDER_DETAILS: `${environment.apiEndpoint}/sales-orders-scheduler`,
    INVOICE: `${environment.apiEndpoint}/invoices`,
    IMAGES: `${environment.apiEndpoint}/media`,
    SALES_ORDER_SIMULATION: `${environment.apiEndpoint}/sales-order/simulation`,
    SALES_ORDER_CREATION: `${environment.apiEndpoint}/sales-order/creation`,
    ADD_SHIPPING_ADDRESS: `${environment.apiEndpoint}/sales-order/add-shipping-address`,
    CARTS: `${environment.apiEndpoint}/carts`,
    GET_MATERIAL_STOCK: `${environment.apiEndpoint}/products/{id}/material-stock`,
    GET_SALES_PRICE: `${environment.apiEndpoint}/sales-price`,
    CUSTOMERS: `${environment.apiEndpoint}/customers`,
    SETTINGS: `${environment.apiEndpoint}/settings`,
    COMPANY_LOGO: `${environment.apiEndpoint}/company-logo`,
    SIMILAR_PRODUCTS: `${environment.apiEndpoint}/similar-products`,
    TICKET_LIST: `${environment.apiEndpoint}/tickets/list`,
    TICKETS: `${environment.apiEndpoint}/tickets`,
    TICKET_STATUSES: `${environment.apiEndpoint}/ticket-statuses`,
    SALES_QUOTE_CREATION: `${environment.apiEndpoint}/sales-quote/create`,
    SALES_QUOTE: `${environment.apiEndpoint}/sales-quote`,
    QUOTE: `${environment.apiEndpoint}/quote-statuses`,
    RETURN_STATUS: `${environment.apiEndpoint}/return-statuses`,
    RETURN_REASON: `${environment.apiEndpoint}/return-reason`,
    REFUND_PROGRESS: `${environment.apiEndpoint}/refund-progress`,
    RETURN_ORDER: `${environment.apiEndpoint}/return-order`,
    NOTIFICATION: `${environment.apiEndpoint}/notification`,
    PRODUCT_REGISTER: `${environment.apiEndpoint}/product-register`,
    BULK_UPLOAD: `${environment.uploadApiEndpoint}/media/upload`,
    EXPORT_FILE_SUMMARY: `${environment.uploadApiEndpoint}/media/export`,
    BULK_UPLOAD_STATUS: `${environment.apiEndpoint}/media-upload-status`,
    USER_ROLES: `${environment.apiEndpoint}/user-roles`,
    CUSTOMER_TEXT_DESCR: `${environment.apiEndpoint}/customer-text-description`,
    SALES_ORDER_SCHEDULER_CREATION: `${environment.apiEndpoint}/sales-orders-scheduler`,
    SALES_ORDER_SCHEDULER: `${environment.apiEndpoint}/sales-orders-scheduler`,
    APP_FEATURES: `${environment.apiEndpoint}/app-features`,
    USER_PERMISSIONS: `${environment.apiEndpoint}/users-permissions`,
    BANNER: `${environment.apiEndpoint}/banner`,
};

export const AppConstant = {
    SESSION_TIMEOUT: 3600 * 1000, // in MS
    PRODUCT_IMAGE_FALLBACK: '/assets/images/demo-product.png'
}

export const RolesType = {
    BACKOFFICE: 'BACKOFFICE',
    STOREFRONT: 'STOREFRONT',
    CUST_SERVICE: 'CUST_SERVICE',
    SALES: 'SALES',
    VENDOR: 'VENDOR'
}