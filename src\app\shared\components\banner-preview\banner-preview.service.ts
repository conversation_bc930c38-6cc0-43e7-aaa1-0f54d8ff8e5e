import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable, map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class BannerPreviewService {
  constructor(private http: HttpClient) {}

  getBannerByCustId(id: string, placement: string): Observable<any[]> {
    let params = new HttpParams().appendAll({ placement });
    return this.http
      .get<any[]>(`${ApiConstant.BANNER}/customer/${id}`, { params })
      .pipe(map((res: any) => res?.data || []));
  }
}
