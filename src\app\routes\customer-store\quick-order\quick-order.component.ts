import { Component } from "@angular/core";
import { AuthService } from "src/app/core/authentication/auth.service";
import { StoreProductService } from "../services/store.product.service";
import { CartService } from "../services/cart.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import {
  Observable,
  catchError,
  debounceTime,
  distinctUntilChanged,
  map,
  of,
  switchMap,
} from "rxjs";

@Component({
  selector: "app-quick-order",
  templateUrl: "./quick-order.component.html",
  styleUrls: ["./quick-order.component.scss"],
})
export class QuickOrderComponent {
  emptyRow = {
    skuNo: "",
    price: 0,
    qty: 0,
    total: 0,
    loading: false,
    error: false,
  };
  products = [
    {
      ...this.emptyRow,
    },
  ];
  saving = false;

  constructor(
    private _snackBar: AppToastService,
    private authService: AuthService,
    private cartService: CartService,
    private storeProductService: StoreProductService
  ) {}

  addSku() {
    if (this.products.length == 20) {
      this._snackBar.open("You can only add upto 20 SKUs.", {
        type: "Warning",
      });
    } else {
      this.products.push({ ...this.emptyRow });
    }
  }

  resetForm() {
    this.products = [
      {
        ...this.emptyRow,
      },
    ];
  }

  clearRow(index: number) {
    this.products.splice(index, 1);
  }

  searchProduct(product: any) {
    product.price = 0;
    product.total = 0;
    if (!product.skuNo) {
      return;
    }
    const userDetail = this.authService.userDetail.partner_function;
    const obj = {
      SalesOrg: userDetail.sales_organization,
      Dist_Channel: userDetail.distribution_channel,
      Division: userDetail.division,
      SoldTo: userDetail.bp_customer_number,
      Products: {
        Material: product.skuNo,
      },
    };
    product.loading = true;
    product.error = false;
    this.storeProductService.getSalesPrice(obj).subscribe({
      next: (value) => {
        product.loading = false;
        if (value?.data?.ConditionRateValue) {
          product.price = parseFloat(value.data.ConditionRateValue);
        }
        product.total = product.price * product.qty;
      },
      error: (err) => {
        product.error = true;
        product.loading = false;
        this._snackBar.open("Error while fetching product details.", {
          type: "Error",
        });
      },
    });
  }

  selectedItem($event: any, product: any) {
    if ($event?.item?.product_id) {
      product.skuNo = $event?.item?.product_id;
      this.searchProduct(product);
    }
  }

  search: any = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(500),
      distinctUntilChanged(),
      switchMap((term: any) => {
        return this.storeProductService
          .search({
            perPage: 5,
            searchBy: "status,product_id",
            search: `ACTIVE,${term}`,
          })
          .pipe(map((response) => response.data))
          .pipe(
            catchError(() => {
              return of([]);
            })
          );
      })
    );

  formatter = (x: { product_id: string }) => x.product_id;

  calculateTotal(product: any) {
    product.total = product.price * product.qty;
  }

  isDisabled() {
    return !!this.products.find(
      (product) => product.loading || product.error || !product.qty
    );
  }

  addToCart() {
    const data = {
      to_Item: this.products.map((product: any) => ({
        Material: product?.skuNo?.product_id,
        RequestedQuantity: product.qty.toString(),
        to_PricingElement: [],
      })),
    };
    this.saving = true;
    this.cartService.mergeToCart(data).subscribe({
      next: (value) => {
        this.saving = false;
        this.cartService.getCartByID();
        this._snackBar.open("Cart updated successfully.");
      },
      error: (err) => {
        this.saving = false;
        this._snackBar.open("Error while processing your request.", {
          type: "Error",
        });
      },
    });
  }
}
