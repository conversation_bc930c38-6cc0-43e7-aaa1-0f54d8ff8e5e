import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { Product } from "../../models/product.model";
import { ProductService } from "../product.service";
import { Subject, Observable, startWith, switchMap, map, shareReplay, forkJoin } from "rxjs";
import { AngularEditorConfig } from "@kolkov/angular-editor";
import { SimilarProductsService } from "../similar-products.service";
import { StoreProductService } from "src/app/routes/customer-store/services/store.product.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { Output } from "@angular/core";
import { EventEmitter } from "@angular/core";

export interface ProductImage {
  id?: number
  product_id?: string
  media_name?: string
  media_type?: string
  image?: string
}


@Component({
  selector: "app-product-details",
  templateUrl: "./product-details.component.html",
  styleUrls: ["./product-details.component.scss"]
})
export class ProductDetailsComponent implements OnInit, OnChanges {
  @Input() model: Product = {};
  @Input() relationshipTypes: any = { data: [] };
  @Output() onUpdate = new EventEmitter<any>();

  form = new FormGroup({});
  refreshing = false;
  cardOpen = true;
  submitted = false;
  aciveTab = "General";
  currentForm: any;
  public classification: any = null;
  editorConfig: AngularEditorConfig = {
    editable: true,
    spellcheck: true,
    height: '15rem',
    minHeight: '5rem',
    placeholder: 'Enter text here...',
    translate: 'no',
    defaultParagraphSeparator: 'p',
    defaultFontName: 'Inter',
    toolbarHiddenButtons: [
      ['insertImage'],
      ['insertVideo']
    ],
    fonts: [
      { class: 'inter', name: 'Inter' },
    ],
  };


  subject = new Subject();
  images$!: Observable<any[]>;
  plants$!: Observable<any>;
  description$!: Observable<any>;
  categories: any = [];
  catalogs: any = [];

  constructor(
    public fb: FormBuilder,
    private _snackBar: AppToastService,
    private translate: TranslateService,
    private productService: ProductService,
    private storeProductService: StoreProductService,
    private similarProductService: SimilarProductsService
  ) { }

  // General Form
  GeneralForm = this.fb.group({
    product_id: "",
    name: [{ value: "" }],
    product_summary: [""],
    specification: [""],
    status: [""],
  });

  // Backend Form
  BackendForm = this.fb.group({
    product_id: [{ value: "", disabled: true }],
    product_desc: [""],
    price: [""],
    stock: [""],
    unit_measure: [""],
    sales_org_id: [""],
    sales_org_desc: [""],
    distri_channel_id: [""],
    distri_channel_desc: [""],
    division_id: [""],
    division_desc: [""],
    is_deleted: [false],
    product_type: [""],
    product_type_desc: [""],
    product_old_id: [""],
    product_old_id_desc: [""],
    product_group: [""],
    product_group_desc: [""],
    base_unit: [""],
    base_unit_desc: [""],
    base_iso_unit: [""],
    base_iso_unit_desc: [""],
    item_category_group: [""],
    item_category_group_desc: [""],
  });

  //price form
  priceForm = this.fb.group({
    product_id: [{ value: "", disabled: true }],
    currency_iso: [""],
    formatted_value: [""],
    max_quantity: [""],
    min_quantity: [""],
    price_type: [""],
    value: [""],
    max_price: [""],
    min_price: [""],
  });

  //Category form
  categoryForm: any = this.fb.group({
    product_id: "",
    product_categories_ids: ["", Validators.required],
    categoryHierarchy: [[]],
    product_catalogs_ids: [""]
  });

  allowedFileExtensions = ["jpg", "jpeg", "png"];

  mediaForm = this.fb.group({
    product_id: "",
    media_name: [""],
    doc_type: ["IMAGE", Validators.required],
    media_type: ["", Validators.required],
    image: ["", Validators.required],
    is_cover_image: [false]
  });

  plantForm = this.fb.group({
    id: "",
    product_id: "",
    plant: ["", Validators.required],
    base_unit: ["", Validators.required],
    base_iso_unit: ["", Validators.required],
  });

  similarProductsForm = this.fb.group({});
  classificationForm = this.fb.group({});
  similarProducts: any = [];
  loadingSimilarProducts = false;
  loadingProductClassifications = false;
  savingSimilarProducts = false;
  addSimilarProductDetails = {
    similar_product_id: '',
    relationship_type_id: '',
  };
  editSimilarProductDetails = {
    similar_product_id: '',
    relationship_type_id: '',
  };

  formMap: Record<string, any> = {
    General: this.GeneralForm,
    Backend: this.BackendForm,
    Pricing: this.priceForm,
    Category: this.categoryForm,
    Media: this.mediaForm,
    Plant: this.plantForm,
    ['Similar Products']: this.similarProductsForm,
    Classification: this.classificationForm
  };

  ngOnInit(): void {
    this.currentForm = this.GeneralForm;
    this.BackendForm.disable();
    this.priceForm.disable();
    this.plantForm.disable();
    this.getSimilarProducts();
    this.getProductClassification();
    this.onCategoryChange();
  }

  getProductClassification() {
    if (this.model?.product_id) {
      this.loadingProductClassifications = true;
      this.storeProductService
        .getProductClassification(this.model?.product_id)
        .subscribe({
          next: (res: any) => {
            this.loadingProductClassifications = false;
            const data = res?.data || [];
            this.classification = data
              .reduce((r: any, a: any) => {
                r[a.class_type_descr] = r[a.class_type_descr] || [];
                r[a.class_type_descr].push(a);
                return r;
              }, Object.create(null));
          },
          error: (e) => {
            this.loadingProductClassifications = false;
            this._snackBar.open("Error while fetching classification.", { type: 'Error' });
          },
        });
    }
  }

  getSimilarProducts() {
    if (this.model?.product_id) {
      this.loadingSimilarProducts = true;
      this.similarProductService.get(this.model?.product_id).subscribe({
        next: (value) => {
          this.loadingSimilarProducts = false;
          if (value.data?.length) {
            for (let i = 0; i < value.data.length; i++) {
              const element = value.data[i];
              element.relationship_type_id = element.relationship_type_id.toString();
            }
            this.similarProducts = value.data;
          } else {
            this.similarProducts = [];
          }
        },
        error: (err) => {
          this.loadingSimilarProducts = false;
          this._snackBar.open("Error while fetching similar products.", { type: 'Error' });
        },
      })
    }
  }

  addSimilarProduct() {
    const obj: any = {
      product_id: this.model.product_id,
      ...this.addSimilarProductDetails
    };
    this.savingSimilarProducts = true;
    this.similarProductService.save(obj).subscribe({
      next: (res: any) => {
        this.savingSimilarProducts = false;
        this.addSimilarProductDetails = {
          similar_product_id: '',
          relationship_type_id: '',
        };
        if (res.data) {
          res.data.relationship_type_id = res.data.relationship_type_id.toString();
          this.similarProducts.push(res.data);
        }
        this._snackBar.open("Record saved successfully.");
      },
      error: (err) => {
        this.savingSimilarProducts = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  editSimilarProduct(item: any) {
    this.editSimilarProductDetails = {
      similar_product_id: item.similar_product_id,
      relationship_type_id: item.relationship_type_id
    };
    item.editing = true;
  }

  updateSimilarProduct(item: any) {
    const obj: any = {
      product_id: this.model.product_id,
      ...this.editSimilarProductDetails
    };
    this.similarProductService.update(obj, item.id).subscribe({
      next: (res) => {
        item.editing = false;
        item.relationship_type_id = this.editSimilarProductDetails.relationship_type_id;
        item.similar_product_id = this.editSimilarProductDetails.similar_product_id;
        this._snackBar.open("Record updated successfully.");
      },
      error: (err) => {
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  removeSimilarProduct(item: any) {
    this.similarProductService.delete(item.id).subscribe({
      next: (res) => {
        this.getSimilarProducts();
        this._snackBar.open("Record deleted successfully.");
      },
      error: (err) => {
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    this.updateData();
  }

  updateData() {
    this.getSimilarProducts();
    this.getProductClassification();
    this.BackendForm.patchValue(this.model);
    this.GeneralForm.patchValue(this.model);
    this.priceForm.patchValue(this.model);
    this.categoryForm.patchValue(this.model);
    this.mediaForm.patchValue(this.model);
    this.images$ = this.subject.asObservable().pipe(
      startWith(0),
      switchMap(() =>
        this.productService.getMedia(this.model.product_id).pipe(
          map((x) => x.data),
        )
      ),
      shareReplay(1),
    );
    if (this.model.product_id) {
      this.plants$ = this.productService.getPlant(this.model.product_id);
      this.description$ = this.productService.getDescription(this.model.product_id);
      forkJoin({
        catagories: this.productService.getAllCategory(),
        productCategory: this.productService.getProductCategory(
          this.model.product_id
        ),
        catalogs: this.productService.getAllCatalog(),
        productCatalog: this.productService.getProductCatalog(
          this.model.product_id
        ),
      }).subscribe(
        ({ catagories, productCategory, catalogs, productCatalog }) => {
          this.categories = this.addCategoryHierarchy(catagories);
          const productCatagories: any = productCategory[0];
          this.setSelectedPCH(productCatagories?.id);
          if (productCategory && productCategory.length) {
            const product_categories_ids = productCategory[0]?.id || "";
            this.categoryForm.controls.product_categories_ids.patchValue(
              product_categories_ids
            );
          }
          this.catalogs = catalogs;
          if (productCatalog && productCatalog.length) {
            const product_catalogs_ids = productCatalog[0].id || "";
            this.categoryForm.controls.product_catalogs_ids.patchValue(
              product_catalogs_ids
            );
          }
        }
      );
    }
  }

  onCategoryChange() {
    this.categoryForm
      .get("product_categories_ids")
      .valueChanges
      .subscribe((value: any) => {
        this.setSelectedPCH(value);
      });
  }

  setSelectedPCH(category_id: any) {
    const pch = this.categories.find((o: any) => category_id == o.id);
    if (pch) {
      this.categoryForm.controls.categoryHierarchy.patchValue(pch.parent_category_hierarchy);
    } else {
      const arr: any = [];
      this.categoryForm.controls.categoryHierarchy.patchValue(arr);
    }
  }

  addCategoryHierarchy(categories: any) {
    const categoryMap = new Map();
    const resultArray: any = [];

    // Step 1: Create a map of categories using their IDs as keys
    categories.forEach((category: any) => {
      categoryMap.set(category.id, { ...category, parent_category_hierarchy: [] });
    });

    // Step 2: Traverse the categories and add their parent hierarchy
    categories.forEach((category: any) => {
      let parentId = category.parent_category_id;
      const currentCategory = categoryMap.get(category.id);

      while (parentId !== null) {
        const parentCategory = categoryMap.get(parentId);
        if (parentCategory) {
          currentCategory.parent_category_hierarchy.unshift(parentCategory.name);
          parentId = parentCategory.parent_category_id;
        } else {
          break;
        }
      }

      if (currentCategory.parent_category_hierarchy.indexOf(currentCategory.name) === -1) {
        currentCategory.parent_category_hierarchy.push(currentCategory.name);
      }

      resultArray.push(currentCategory);
    });

    return resultArray;
  }

  get f() {
    return this.currentForm.controls;
  }

  setActiveform(name: string) {
    this.aciveTab = name;
    this.currentForm = this.formMap[name];
  }

  submitForm() {
    this._markAsTouched(this.currentForm);
    if (this.currentForm.valid) {
      if (this.aciveTab === 'Media') {
        const formValue = this.currentForm.value;
        const obj = {
          product_id: formValue.product_id,
          media_name: formValue.media_name,
          dimension: formValue.media_type,
          media_type: formValue.doc_type,
          url: formValue.image,
          is_cover_image: formValue.is_cover_image
        };
        this.productService.submitMediaForm(obj).subscribe({
          next: () => {
            this._snackBar.open(this.translate.instant("form.submit.success"));
            this.mediaForm.patchValue({
              media_name: '',
              media_type: '',
              image: '',
              is_cover_image: false
            });
            this.mediaForm.markAsUntouched();
            this.subject.next(0);
          },
          error: () => {
            this._snackBar.open("Error while processing your request.", { type: 'Error' });
          },
        });
      } else if (this.aciveTab === 'Category') {
        const catFormVal = this.categoryForm.value;
        const categaoryPayload = {
          product_categories_ids: catFormVal.product_categories_ids,
        };
        const catalogPayload = {
          product_catalogs_ids: catFormVal.product_catalogs_ids,
        };
        forkJoin({
          saveProductCategory: this.productService.setProductCategory(
            catFormVal.product_id,
            categaoryPayload
          ),
          saveProductCatalog: this.productService.setProductCatalog(
            catFormVal.product_id,
            catalogPayload
          ),
        }).subscribe(({ saveProductCategory, saveProductCatalog }) => {
          this._snackBar.open(this.translate.instant("form.submit.success"));
        });
      } else if (this.aciveTab === 'General') {
        this.productService.submitGeneralForm(this.GeneralForm.value).subscribe({
          next: res => {
            this._snackBar.open(this.translate.instant("form.submit.success"));
          }
        });
      }
    }
  }

  toogleDetails() {
    this.cardOpen = !this.cardOpen;
  }


  removeImg(id: string) {
    this.productService.removeMedia(id).subscribe({
      next: () => {
        this._snackBar.open(this.translate.instant("form.imageRemoved"));
        this.subject.next(0);
      },
      error: () => {
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  changeDocType(e: any) {
    this.mediaForm.patchValue({
      media_name: '',
      media_type: '',
      image: '',
      is_cover_image: false
    });
    if (
      this.mediaForm.controls.doc_type.value !== "IMAGE" &&
      this.mediaForm.controls.doc_type.value !== "SPECIFICATION"
    ) {
      this.mediaForm.controls.media_type.clearValidators();
      this.mediaForm.controls.media_type.updateValueAndValidity();
      this.mediaForm.controls.media_name.setValidators(Validators.required);
      this.mediaForm.controls.media_name.updateValueAndValidity();
    } else {
      this.mediaForm.controls.media_type.setValidators(Validators.required);
      this.mediaForm.controls.media_type.updateValueAndValidity();
      this.mediaForm.controls.media_name.clearValidators();
      this.mediaForm.controls.media_name.updateValueAndValidity();
    }
  }

  toggleAccordian(data: any, event: any, index: number) {
    const element = event.target;
    element.classList.toggle("active");
    if (data[index].isActive) {
      data[index].isActive = false;
    } else {
      data[index].isActive = true;
    }

    const panel = element.parentElement.nextElementSibling;

    if (panel.style.maxHeight) {
      panel.style.maxHeight = null;
    } else {
      panel.style.maxHeight = panel.scrollHeight + "px";
    }
  }

  private _markAsTouched(group: FormGroup | FormArray) {
    group.markAsTouched({ onlySelf: true });

    Object.keys(group.controls).map((field) => {
      const control = group.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this._markAsTouched(control);
      }
    });
  }

  _removeValue(list: any, value: any) {
    list = list.split(",");
    list.splice(list.indexOf(value), 1);
    return list.join(",");
  }

  refresh() {
    if (!this.model?.product_id) {
      return;
    }
    this.refreshing = true;
    this.productService.getById(this.model.product_id).subscribe({
      next: (value) => {
        this.refreshing = false;
        this.model = value.data;
        this.onUpdate.emit(this.model);
        this.updateData();
      },
      error: (err) => {
        this.refreshing = false;
      },
    })
  }

  trustUrl(url: string = '') {
    return (url || '').replace(/\(/g, "\\(").replace(/\)/g, '\\)');
  }

}
