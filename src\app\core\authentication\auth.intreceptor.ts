import { Injectable } from "@angular/core";
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpErrorResponse,
  HttpResponse,
} from "@angular/common/http";
import { AuthService } from "./auth.service";
import { catchError, Observable, of, tap, throwError } from "rxjs";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler) {
    if (this.authService.isLoggedIn) {
      const authToken = this.authService.getToken();
      req = req.clone({
        setHeaders: {
          Authorization: "Bearer " + authToken,
        },
      });
    }
    return next
      .handle(req)
      .pipe(
        tap((event: any) => {
          if (event instanceof HttpResponse) {
            const newToken = event.headers.get("refreshtoken");
            const auth = this.authService.getAuth();
            if (newToken && auth) {
              auth.token = newToken;
              this.authService.setAuth(auth);
            }
          }
        })
      )
      .pipe(catchError((x) => this.handleAuthError(x)));
  }

  private handleAuthError(err: HttpErrorResponse): Observable<any> {
    if (err.status === 401 || err.status === 403) {
      this.authService.removeAuthToken();
      window.location.href = "#/auth/login";
      window.location.reload();

      return of(err.message);
    }
    return throwError(() => err);
  }
}
