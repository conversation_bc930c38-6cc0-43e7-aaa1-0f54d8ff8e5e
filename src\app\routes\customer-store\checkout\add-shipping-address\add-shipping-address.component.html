<div class="all-main-title-sec cart-details-main-title">
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>
        <a [routerLink]="['/store/checkout']">Secured Checkout</a>
      </li>
      <li>Create Shipping Address</li>
    </ul>
  </div>
  <h1>Secured Checkout</h1>
</div>
<form class="order-status-form" [formGroup]="form">
  <div class="form all-form-res">
    <div class="form-group o-num">
      <label>Name1 <span class="text-danger">*</span></label>
      <input type="input" class="form-control" placeholder="Enter Name1" formControlName="OrganizationBPName1" />
      <div *ngIf="submitted && f['OrganizationBPName1'].errors" class="invalid-feedback d-block">
        <span *ngIf="f['OrganizationBPName1']?.errors['required']">
          Name1 is required
        </span>
      </div>
    </div>
    <div class="form-group o-num">
      <label>Name2</label>
      <input type="input" class="form-control" placeholder="Enter Name2" formControlName="OrganizationBPName2" />
    </div>
    <div class="form-group">
      <label>Title</label>
      <select formControlName="AcademicTitle" class="form-control select-arrow-down">
        <option value="">Select title</option>
        <option *ngFor="let title of titles" [value]="title.code">
          {{ title.description }}
        </option>
      </select>
    </div>
    <ng-container formGroupName="to_BusinessPartnerAddress">
      <div class="form-group o-num">
        <label>Street Address <span class="text-danger">*</span></label>
        <input type="input" class="form-control" placeholder="Enter Address" formControlName="StreetName" />
        <div *ngIf="submitted && fToBPAddress['StreetName'].errors" class="invalid-feedback d-block">
          <span *ngIf="fToBPAddress['StreetName']?.errors['required']">
            Street Address is required
          </span>
        </div>
      </div>
      <div class="form-group">
        <label>Country / Region</label>
        <select class="form-control select-arrow-down" formControlName="Country">
          <option value="US">USA</option>
        </select>
      </div>
      <div class="form-group">
        <label>State / Province <span class="text-danger">*</span></label>
        <select formControlName="Region" class="form-control select-arrow-down" (change)="getCities($event)">
          <option value="">Select State</option>
          <option *ngFor="let state of states" [value]="state.abbreviation">
            {{ state.name }}
          </option>
        </select>
        <div *ngIf="submitted && fToBPAddress['Region'].errors" class="invalid-feedback d-block">
          <span *ngIf="fToBPAddress['Region']?.errors['required']">
            State / Province is required
          </span>
        </div>
      </div>
      <div class="form-group o-num">
        <label>Town / City <span class="text-danger">*</span></label>
        <select formControlName="CityName" class="form-control select-arrow-down">
          <option value="">Select Town / City</option>
          <option *ngFor="let city of cities" [value]="city.name">
            {{ city.name }}
          </option>
        </select>
        <div *ngIf="submitted && fToBPAddress['CityName'].errors" class="invalid-feedback d-block">
          <span *ngIf="fToBPAddress['CityName']?.errors['required']">
            Town / City is required
          </span>
        </div>
      </div>
      <div class="form-group o-num">
        <label>Zip / Postal code <span class="text-danger">*</span></label>
        <input type="input" class="form-control" placeholder="Enter Zip Code" formControlName="PostalCode" />
        <div *ngIf="submitted && fToBPAddress['PostalCode'].errors" class="invalid-feedback d-block">
          <span *ngIf="fToBPAddress['PostalCode']?.errors['required']">
            Zip / Postal code is required
          </span>
          <span *ngIf="fToBPAddress['PostalCode']?.errors['pattern']">
            Enter a valid zip code format.
          </span>
        </div>
      </div>
      <div class="form-group o-num" formGroupName="to_PhoneNumber">
        <label>Phone Number</label>
        <input type="input" class="form-control" placeholder="Enter Phone Number" formControlName="PhoneNumber" />
      </div>
      <div class="form-group o-num" formGroupName="to_EmailAddress">
        <label>Email Address</label>
        <input type="input" class="form-control" placeholder="Enter Email Address" formControlName="EmailAddress" />
        <div *ngIf="submitted && fbpTOEmail['EmailAddress'].errors" class="invalid-feedback d-block">
          <span *ngIf="fbpTOEmail['EmailAddress'].errors['email']">
            Email is invalid
          </span>
        </div>
      </div>
      <div class="form-btn-sec">
        <button type="button" class="order-s-btn" (click)="!saving && addShippingAddress()" [disabled]="saving">
          <i class="material-icons-outlined spin m-1" *ngIf="saving">sync</i>
          SAVE SHIPPING ADDRESS
        </button>
      </div>
    </ng-container>
  </div>
</form>