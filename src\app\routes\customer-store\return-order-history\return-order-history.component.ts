import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { Subject, takeUntil } from "rxjs";
import { Ng<PERSON><PERSON><PERSON>nda<PERSON>, NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import {
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from "@angular/forms";
import moment from "moment";

import { AuthService } from "src/app/core/authentication/auth.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { ReturnOrderService } from "./return-order.service";
import { Router } from "@angular/router";
import * as XLSX from 'xlsx';
import { ColDef } from "ag-grid-community";


@Component({
  selector: "app-return-order-history",
  templateUrl: "./return-order-history.component.html",
  styleUrls: ["./return-order-history.component.scss"],
})
export class ReturnOrderHistoryComponent implements OnInit, OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  public moment: any = moment;
  public sellerDetails: any = {};
  public filterForm: FormGroup;
  public loading: any = false;
  public submitted: any = false;
  public statuses: any = [];
  public returnOrders: any = [];
  public columnDefs: ColDef[] = [
    {
      field: "SD_DOC",
      headerName: 'Return Order #',
      headerComponentParams: { menuIcon: 'pin' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">pin</i>${data.data.SD_DOC}</span>`;
      },
      sortable: true,
      comparator: (valueA, valueB, nodeA, nodeB, isDescending) =>  parseInt(valueA) - parseInt(valueB),
      resizable: true
    },
    {
      field: "REF_SD_DOC",
      headerName: 'Ref. Sales Order #',
      headerComponentParams: { menuIcon: 'pin' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">pin</i>${data.data.REF_SD_DOC}</span>`;
      },
      comparator: (valueA, valueB, nodeA, nodeB, isDescending) =>  parseInt(valueA) - parseInt(valueB),
      sortable: true,
      resizable: true
    },
    {
      field: "DOC_DATE",
      headerName: 'Date Placed',
      sortable: true,
      headerComponentParams: { menuIcon: 'calendar_month' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">calendar_month</i>${moment(data.data.DOC_DATE).format("MM/DD/YYYY")}</span>`;
      },
      resizable: true
    },
    {
      field: "DOC_STATUS",
      headerName: "Status",
      headerComponentParams: { menuIcon: 'feed' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">feed</i>${this.getStatusName(data.data.DOC_STATUS)}</span>`;
      },
      resizable: true
    }
  ];

  constructor(
    private _snackBar: AppToastService,
    private calendarService: NgbCalendar,
    public router: Router,
    public authService: AuthService,
    public returnOrderService: ReturnOrderService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit(): void {
    this.createForm();
    this.getAllStatus();
  }

  createForm() {
    this.filterForm = new FormGroup(
      {
        DOCUMENT_DATE: new FormControl(null),
        DOCUMENT_DATE_TO: new FormControl(null),
        DOC_STATUS: new FormControl(""),
        SD_DOC: new FormControl(""),
        DOC_TYPE: new FormControl("CBAR"),
        SOLDTO: new FormControl(this.sellerDetails.customer_id),
        VKORG: new FormControl(this.sellerDetails.sales_organization),
        COUNT: new FormControl("100"),
      },
      [Validators.required, this.dateRangeValidator]
    );
  }

  clearFormField(name: string) {
    const obj: any = {};
    obj[name] = "";
    this.filterForm.patchValue(obj);
  }

  // convenience getter for easy access to form fields
  get f(): any {
    return this.filterForm?.controls;
  }

  private dateRangeValidator: ValidatorFn = (): {
    [key: string]: any;
  } | null => {
    let invalid = false;
    const from = this.filterForm && this.filterForm.get("DOCUMENT_DATE")?.value;
    const to =
      this.filterForm && this.filterForm.get("DOCUMENT_DATE_TO")?.value;
    if ((from && !to) || (!from && to)) {
      invalid = true;
    } else if (from && to) {
      invalid = new Date(from).valueOf() > new Date(to).valueOf();
    }
    return invalid ? { invalidRange: { from, to } } : null;
  };

  clear() {
    this.filterForm.patchValue({ DOCUMENT_DATE: null });
    this.filterForm.patchValue({ DOCUMENT_DATE_TO: null });
    this.filterForm.patchValue({ SD_DOC: "" });
    const status = this.statuses.find((val: any) => val.description === "All");
    this.filterForm.patchValue({ DOC_STATUS: status.code });
  }

  formatSearchDate(date: NgbDateStruct) {
    if (!date) return "";
    let newDate = new Date(date["year"], date["month"] - 1, date["day"]);
    return moment(newDate).format("YYYYMMDD");
  }

  today() {
    return this.calendarService.getToday();
  }

  getStatusName(code: string) {
    const status = this.statuses.find((o: any) => o.code === code);
    if (status) {
      return status.description;
    }
    return "";
  }

  getAllStatus() {
    this.returnOrderService
      .getAllStatus()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.statuses = res?.data || [];
          const statuses = this.statuses.map((val: any) => val.code).join(";");
          this.statuses.unshift({ code: statuses, description: "All" });
          this.filterForm.patchValue({ DOC_STATUS: statuses });
          this.getAllReturnOrder();
        },
        error: () => {
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  search() {
    this.getAllReturnOrder();
  }

  getAllReturnOrder() {
    this.submitted = true;
    if (this.filterForm.invalid) {
      return;
    }
    const payload: any = this.filterForm.value;
    payload.DOCUMENT_DATE = this.formatSearchDate(payload.DOCUMENT_DATE);
    payload.DOCUMENT_DATE_TO = this.formatSearchDate(payload.DOCUMENT_DATE_TO);
    this.loading = true;
    return this.returnOrderService
      .getAll(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.loading = false;
          this.submitted = false;
          this.returnOrders = res?.data?.RETURNORDERS || [];
        },
        error: () => {
          this.loading = false;
          this.submitted = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  exportToExcel() {
    const fileName = 'return-order-history.xlsx';
    const data = this.formatData(this.returnOrders)
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'test');

    XLSX.writeFile(wb, fileName);
  }

  formatData(returnOrders: any) {
    return returnOrders.map(({ REF_SD_DOC, DOC_STATUS, DOC_DATE, SD_DOC
    }: { REF_SD_DOC: string, DOC_STATUS: string, DOC_DATE: string, SD_DOC: string }) => ({ 'Return Order #': SD_DOC, 'Order #': REF_SD_DOC, 'Date Placed': moment(DOC_DATE).format("MM/DD/YYYY"), 'Return Status': this.getStatusName(DOC_STATUS) }));
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  goToRetrunOrder(order: any) {
    this.router.navigate([`/store/return-order/${order[0].SD_DOC}/${order[0].REF_SD_DOC}`]);
  }
}
