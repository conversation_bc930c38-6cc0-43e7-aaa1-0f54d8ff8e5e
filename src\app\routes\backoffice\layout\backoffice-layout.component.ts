import { Component } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import {
  DashboardLayoutConfiguration,
  SidePanelPosition,
  SidePanelState,
} from "../../../core";
import { NavigationLink } from "../../../shared";

@Component({
  selector: "backoffice-layout",
  templateUrl: "./backoffice-layout.component.html",
  styleUrls: ["./backoffice-layout.component.scss"],
})
export class BackOfficeLayoutComponent {
  public configuration: DashboardLayoutConfiguration;
  public links: NavigationLink[] = [];

  constructor(private translate: TranslateService) {
    this.configuration = new DashboardLayoutConfiguration(
      SidePanelPosition.LEFT,
      SidePanelState.OPEN
    );
    this.createLinks();
  }

  private createLinks() {
    this.translate
      .get([
        "admin.menu.home",
        "admin.menu.dashboard",
        "admin.menu.product",
        "admin.menu.customer",
        "admin.menu.contacts",
        "admin.menu.settings",
        "admin.menu.configs",
        "admin.menu.admin",
        "admin.menu.products"
      ])
      .subscribe((translations) => {
        const DASHBOARD = translations["admin.menu.dashboard"];
        const PRODUCT = translations["admin.menu.product"];
        const CUSTOMER = translations["admin.menu.customer"];
        const CONTACTS = translations["admin.menu.contacts"];
        const SETTINGS = translations["admin.menu.settings"];
        const CONFIGS = translations["admin.menu.configs"];
        const ADMIN = translations["admin.menu.admin"];
        const PRODUCTS = translations["admin.menu.products"];
        this.links = [
          new NavigationLink(DASHBOARD, ["dashboard"], "widgets"),
          new NavigationLink(
            PRODUCT,
            ["data", "product"],
            "shopping_basket",
            "P0028"
          ),
          new NavigationLink(
            CUSTOMER,
            ["data", "customer"],
            "group_add",
            "P0029"
          ),
          new NavigationLink(
            CONTACTS,
            ["data", "contacts"],
            "contacts",
            "P0030"
          ),
          new NavigationLink(
            SETTINGS,
            ["data", "settings"],
            "settings_suggest",
            "P0037"
          ),
          new NavigationLink(
            CONFIGS,
            ["data", "configs"],
            "display_settings",
            "P0031"
          ),
          new NavigationLink(
            ADMIN,
            ["data", "admin"],
            "manage_accounts",
            "P0032"
          ),
        ];
      });
  }
}
