<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">{{data?.role_name}} Permissions</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <div class="table-responsive">
        <ng-container *ngIf="permissions.length">
            <app-nested-table [data]="permissions" (toggle)="toggle($event)"
                (checkboxChange)="onCheckboxChange($event)"></app-nested-table>
        </ng-container>
        <ng-container *ngIf="!permissions.length">
            <h4 class="text-center">No permissions found.</h4>
        </ng-container>
    </div>
</div>
<div class="modal-footer">
    <button class="btn btn-light me-2" (click)="activeModal.dismiss()">Cancel</button>
    <button class="btn btn-primary" [disabled]="saving" (click)="!saving && savePermissions()">
        {{ saving ? 'Saving...' : 'Save' }}</button>
</div>