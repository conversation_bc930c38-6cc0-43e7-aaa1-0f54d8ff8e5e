import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { BehaviorSubject, map, lastValueFrom, catchError } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class AppFeatureService {
  public appFeatures: BehaviorSubject<any> = new BehaviorSubject<any>([]);

  constructor(private http: HttpClient) {}

  async getTurnOnFetures() {
    return await lastValueFrom(
      this.http
        .get<any>(`${ApiConstant.APP_FEATURES}/enable`)
        .pipe(
          map((res) => {
            if (res.status === "success") {
              const data = res?.data || [];
              this.appFeatures.next(data);
              return data;
            }
            return res;
          })
        )
        .pipe(
          catchError((error) => {
            this.appFeatures.next([]);
            return error;
          })
        )
    );
  }

  get enabledFetures(): any[] {
    return this.appFeatures?.value || [];
  }
}
