<div class="p-4">
    <h3 class="mb-2 d-flex justify-content-between">
        <span *ngIf="!data?.id">Schedule Auto Replenishment</span>
        <span *ngIf="data?.id">Update Auto Replenishment #{{data?.id}}</span>
        <a class="text-primary" href="javascript:void(0);">
            <span class="material-icons-outlined" (click)="activeModal.dismiss()">
                close
            </span>
        </a>
    </h3>
    <form [formGroup]="form">
        <div class="form-group">
            <label class="form-label">
                Start auto replenishment on <span class="text-danger">*</span>
            </label>
            <div class="input-group">
                <ng-container *ngIf="data?.id">
                    <input class="form-control" name="picker1" formControlName="start_date" ngbDatepicker
                        #d="ngbDatepicker" />
                    <button class="btn btn-outline-secondary dp-btn" (click)="d.toggle()" type="button">
                        <i class="material-icons-outlined">
                            calendar_month
                        </i>
                    </button>
                </ng-container>
                <ng-container *ngIf="!data?.id">
                    <input class="form-control" name="picker1" formControlName="start_date" ngbDatepicker
                        #d="ngbDatepicker" [minDate]="today()" />
                    <button class="btn btn-outline-secondary dp-btn" (click)="d.toggle()" type="button">
                        <i class="material-icons-outlined">
                            calendar_month
                        </i>
                    </button>
                </ng-container>

            </div>
            <div *ngIf="submitted && f['start_date'].errors" class="invalid-feedback d-block">
                <div *ngIf="f['start_date'].errors['required']">
                    Start Date is required
                </div>
            </div>
        </div>
        <div class="form-group mt-3">
            <label class="form-label">
                Frequency
            </label>
            <ng-container *ngFor="let st of scheduleTypes;let i = index;">
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="schedule_type" [attr.id]="'schedule_type'+i"
                        formControlName="schedule_type" [value]="st"
                        (change)="onScheduleTypeChange(f['schedule_type'].value)">
                    <label class="form-check-label" [attr.for]="'schedule_type'+i">
                        {{st}}
                    </label>
                </div>
            </ng-container>
        </div>
        <ng-container *ngIf="f['schedule_type'].value === 'WEEKLY'">
            <div class="form-group mt-3">
                <label class="mb-2">Every # of weeks:</label>
                <select formControlName="frequency" class="form-select mt-1">
                    <option *ngFor="let f of frequency" [value]="f+1">{{f + 1}}</option>
                </select>
            </div>
            <div class="form-group mt-3">
                <label class="mb-2">On the following days: <span class="text-danger">*</span></label>
                <ng-container *ngFor="let weekday of weekdays;let i = index;" formArrayName="weekdays_to_generate">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="weekday" [attr.id]="'weekday'+i"
                            [formControlName]="i">
                        <label class="form-check-label" [attr.for]="'weekday'+i">
                            {{weekday}}
                        </label>
                    </div>
                </ng-container>
                <div *ngIf="submitted && f['weekdays_to_generate'].errors" class="invalid-feedback d-block">
                    <div *ngIf="f['weekdays_to_generate'].errors['requireCheckboxToBeChecked']">
                        At least one weekday is required. Please select the checkbox.
                    </div>
                </div>
            </div>
        </ng-container>
        <div class="form-group mt-3" *ngIf="f['schedule_type'].value === 'MONTHLY'">
            <label class="mb-2">On this day of the month:</label>
            <select formControlName="day_of_month" class="form-select mt-1">
                <option *ngFor="let f of frequency" [value]="f+1">{{f + 1}}</option>
            </select>
        </div>
        <div class="form-group mt-3">
            <label class="form-label">
                End auto replenishment on <span class="text-danger">*</span>
            </label>
            <div class="input-group">
                <input class="form-control" name="picker2" formControlName="end_date" ngbDatepicker #d1="ngbDatepicker"
                    [minDate]="f['start_date'].value" />
                <button class="btn btn-outline-secondary dp-btn" (click)="d1.toggle()" type="button">
                    <i class="material-icons-outlined">
                        calendar_month
                    </i>
                </button>
            </div>
            <div *ngIf="submitted && f['end_date'].errors" class="invalid-feedback d-block">
                <div *ngIf="f['end_date'].errors['required']">
                    End Date is required
                </div>
            </div>
        </div>
        <div class="form-group mt-3" *ngIf="data?.id">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" formControlName="is_cancelled" id="is_cancelled"
                    (change)="onCancelledReplenishment()" />
                <label class="form-check-label" for="is_cancelled">
                    Do you want to cancel this auto replenishment?
                </label>
            </div>
        </div>
    </form>
    <div class="mt-3">
        <button class="btn" [disabled]="!form.valid" (click)="createSalesOrderSchedule()">
            <span *ngIf="!data?.id">SCHEDULE AUTO REPLENISHMENT</span>
            <span *ngIf="data?.id">UPDATE AUTO REPLENISHMENT</span>
        </button>
        <button class="btn btn-light mb-0" (click)="activeModal.dismiss()">Cancel</button>
    </div>
</div>