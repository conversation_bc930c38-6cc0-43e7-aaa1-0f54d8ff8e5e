<section class="order-status-sec">
  <div class="order-status-body">
    <div class="all-main-title-sec">
      <h1>Register Product</h1>
      <div class="all-bedcrumbs">
        <ul>
          <li>
            <a [routerLink]="['/store/dashboard']">
              <span class="material-icons-outlined">home</span> Home
            </a>
          </li>
          <li>
            <a [routerLink]="['/store/customer-services']">
              <span class="material-icons-outlined">support_agent</span>
              Customer Service
            </a>
          </li>
          <li>Register Product</li>
        </ul>
      </div>
    </div>
    <div class="order-contact-list">
      <div class="order-c-box">
        <div class="order-c-icon">
          <img src="/assets/images/seller-icon.png" alt="" title="" />
        </div>
        <div class="order-c-details">
          <h4>Customer #</h4>
          <small>{{ sellerDetails?.bp_customer_number }}</small>
        </div>
      </div>
      <div class="order-c-box">
        <div class="order-c-icon">
          <img src="/assets/images/phone-icon.png" alt="" title="" />
        </div>
        <div class="order-c-details">
          <h4>Customer Name</h4>
          <small>{{ sellerDetails.name }}</small>
        </div>
      </div>
      <div class="order-c-box address-box">
        <div class="order-c-icon">
          <img src="/assets/images/address-icon.png" alt="" title="" />
        </div>
        <div class="order-c-details">
          <h4>ADDRESS</h4>
          <small>{{ sellerDetails.address }}</small>
        </div>
      </div>
    </div>
    <div class="order-status-form all-form-res">
      <form [formGroup]="form">
        <div class="form reg-a-p-form">
          <div class="form-group">
            <label>
              <span class="material-icons-outlined">subject</span>
              Product ID #
              <span class="text-danger">*</span>
            </label>
            <input
              type="text"
              formControlName="product_id"
              class="form-control"
              placeholder="Product ID #"
              [ngClass]="{ 'is-invalid': submitted && f['product_id'].errors }"
            />
            <ng-container *ngIf="submitted && f['product_id'].errors">
              <div class="invalid-feedback">
                <span *ngIf="submitted && f['product_id']?.errors['required']">
                  Product ID is required
                </span>
              </div>
            </ng-container>
          </div>
          <div class="form-group">
            <label>
              <span class="material-icons-outlined">subject</span>
              Serial #
              <span class="text-danger">*</span>
            </label>
            <input
              type="text"
              formControlName="serial"
              class="form-control"
              placeholder="Enter Serial #"
              [ngClass]="{ 'is-invalid': submitted && f['serial'].errors }"
            />
            <ng-container *ngIf="submitted && f['serial'].errors">
              <div class="invalid-feedback">
                <span *ngIf="submitted && f['serial']?.errors['required']">
                  Serial is required
                </span>
              </div>
            </ng-container>
          </div>
          <div class="form-group">
            <label>
              <span class="material-icons-outlined">home</span>
              Place Of Purchase
            </label>
            <input
              type="text"
              formControlName="purchase_place"
              class="form-control"
              placeholder="Place Of Purchase"
            />
          </div>
          <div class="form-group">
            <label>
              <span class="material-icons-outlined">calendar_month</span>
              Purchase Date
              <span class="text-danger">*</span>
            </label>
            <div class="input-group" [ngClass]="{ 'is-invalid': submitted && f['purchase_date'].errors }">
              <input
                class="form-control"
                name="picker1"
                formControlName="purchase_date"
                placeholder="YYYY-MM-DD"
                ngbDatepicker
                #d="ngbDatepicker"
                (click)="d.toggle()"
                [maxDate]="today()"
              />
              <button
                class="btn btn-outline-secondary"
                (click)="d.toggle()"
                type="button"
              >
                <i class="material-icons-outlined"> calendar_month </i>
              </button>
            </div>
            <ng-container *ngIf="submitted && f['purchase_date'].errors">
              <div class="invalid-feedback">
                <span *ngIf="submitted && f['purchase_date']?.errors['required']">
                  Purchase date is required
                </span>
              </div>
            </ng-container>
          </div>
          <div class="form-group">
            <label>
              <span class="material-icons-outlined">cloud_upload</span>
              Proof of Purchase
            </label>
            <input
              #fileInput
              type="file"
              class="file-input"
              accept=".pdf,.doc,.docx,.png,.jpg,.jpeg"
              (change)="onFileChange($event)"
            />
            <button
              type="button"
              class="upload-file-link d-flex flex-column gap-3 align-items-between"
              (click)="fileInput.click()"
            >
              <span class="material-icons-outlined"> cloud_upload</span>
              <span>Choose File</span>
            </button>
          </div>
          <div class="form-group">
            <label>
              <span class="material-icons-outlined">sell</span>
              Purchase Price
            </label>
            <input
              type="number"
              formControlName="purchase_price"
              class="form-control"
              placeholder="Purchase Price"
            />
          </div>
          <div class="form-btn-sec">
            <button
              type="button"
              class="btn order-s-btn"
              [disabled]="saving"
              (click)="onSubmit()"
            >
              {{ saving ? "Submitting..." : "Submit" }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</section>
