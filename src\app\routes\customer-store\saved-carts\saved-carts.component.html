<div class="all-main-title-sec cart-details-main-title">
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>Saved Carts</li>
    </ul>
  </div>
  <h1>Saved Carts <span *ngIf="data.length"><span class="material-icons-outlined">shopping_bag</span>
      {{data.length}} Carts</span></h1>
</div>
<div class="save-c-table">
  <table class="table all-table">
    <thead>
      <tr>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">person_outline</span> Name</div>
        </th>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">badge</span> ID</div>
        </th>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">save</span> Date Saved</div>
        </th>
        <th width="300">
          <div class="save-c-table-box"><span class="material-icons-outlined">description</span> Description
          </div>
        </th>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">pin</span> Qty</div>
        </th>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">summarize</span> Total</div>
        </th>
        <th></th>
      </tr>
    </thead>
    <tbody *ngIf="loading">
      <tr class="loader">
        <td colspan="7">
          <div class="d-flex w-100 h-100 justify-content-center align-items-center">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        </td>
      </tr>
    </tbody>
    <tbody *ngIf="!loading">
      <tr *ngIf="!data.length">
        <td colspan="7">No records found.</td>
      </tr>
      <tr *ngFor="let item of data | SortDescByDate : 'created_at'" (click)="goToCart(item)">
        <td data-label="Name">{{item.name}}</td>
        <td data-label="ID">{{item.id}}</td>
        <td data-label="Date Saved">{{moment.utc(item?.created_at).local().format("MM/DD/YYYY HH:mm:ss A")}}</td>
        <td data-label="Description">{{item.description}}</td>
        <td data-label="Qty">{{item.payload?.length}}</td>
        <td data-label="Total">{{(item?.total || 0) | currency }}</td>
        <td>
          <div class="save-c-btns">
            <button type="button" class="btn restore-btn"
              (click)="$event.stopPropagation();restore(item)">Restore</button>
            <button type="button" class="btn" (click)="$event.stopPropagation();delete(item)"><span
                class="material-icons-outlined">delete_forever</span></button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div class="save-c-btn"><button type="button" class="btn btn-warning primary_color"
    [routerLink]="['/store/import-cart']" *checkPermission="'P0036'"> Import Saved Cart </button></div>