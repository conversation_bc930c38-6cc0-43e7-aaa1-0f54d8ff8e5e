import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { ApiConstant } from 'src/app/constants/api.constants';

@Injectable({
  providedIn: 'root'
})
export class OrderHistoryService {

  constructor(private http: HttpClient) { }

  getAll(data: any) {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(ApiConstant.ORDER_HISTORY, {
      params,
    });
  }

  getAllStatuses() {
    return this.http.get<any>(ApiConstant.GET_SALE_ORDER_STATUSES);
  }

  getAllOrderType() {
    return this.http.get<any>(ApiConstant.GET_ORDER_TYPES);
  }
}
