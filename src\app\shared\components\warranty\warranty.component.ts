import { Component, Input, OnInit } from '@angular/core';
import moment from 'moment';

interface WarrantyPeriod {
  name: string;
  startDate: Date;
  endDate: Date;
  stilRuning?: boolean;
}

@Component({
  selector: 'app-warranty',
  templateUrl: './warranty.component.html',
  styleUrls: ['./warranty.component.scss']
})
export class WarrantyComponent implements OnInit {
  @Input() warrantyPeriods: WarrantyPeriod[] = [];

  starting = new Date();
  startingYear = 0;
  startingMonth = 0;
  ending = new Date();
  endingMonth = this.ending.getMonth();
  moment = moment;
  endingYear = this.ending.getFullYear();
  slots = 0;

  ngOnInit() {
    for (let i = 0; i < this.warrantyPeriods.length; i++) {
      const element = this.warrantyPeriods[i];
      if (element.startDate < this.starting) {
        this.starting = element.startDate;
        this.startingMonth = this.starting.getMonth();
        this.startingYear = this.starting.getFullYear();
      }
      if (element.endDate >= this.ending) {
        this.ending = element.endDate;
        this.endingMonth = this.ending.getMonth();
        this.endingYear = this.ending.getFullYear();
      }
    }
    const today = new Date();
    const slots = moment(new Date(today.getFullYear(), today.getMonth())).diff(this.starting, 'months');
    this.slots = slots;
  }

  getWidth(warranty: WarrantyPeriod): string {
    const slots = moment(new Date(warranty.endDate.getFullYear(), warranty.endDate.getMonth())).diff(warranty.startDate, 'months');
    const left = moment(warranty.startDate).diff(this.starting, 'months');
    return `width: ${(slots * 100) / this.slots}%; left: ${(left * 100) / this.slots}%`;
  }
}
