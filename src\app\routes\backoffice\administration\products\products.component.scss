.file-input {
    display: none;
}

.red {
    color: red;
}

.import-saved-card-body {
    margin: 0;
    padding: 0;
    position: relative;
    display: flex;
    gap: 0 2%;

    .import-saved-cart-box {
        margin: 10px 0 30px 0;
        padding: 30px;
        background: var(--snjy-color-white);
        border-radius: 12px;
        border: 1px solid rgba(208, 215, 216, 0.3882352941);
        box-shadow: var(--snjy-box-shadow);
        display: block;
        min-height: 200px;
        flex: 1;

        h3 {
            margin: 0 0 15px 0;
            padding: 0;
            position: relative;
            font-size: var(--snjy-font-size-1-125);
            font-weight: var(--snjy-font-weight-medium);
            color: #00216c;
            line-height: 20px;
        }

        ul {
            margin: 0;
            padding: 0;
            list-style: none;
            position: relative;

            li {
                margin: 0 0 6px 0;
                padding: 0 0 0 22px;
                color: #687491;
                font-weight: var(--snjy-font-weight-medium);
                font-size: var(--snjy-font-size-0-8);
                position: relative;

                span {
                    margin-top: 5px;
                    padding: 0;
                    position: absolute;
                    width: fit-content;
                    height: fit-content;
                    font-size: 16px !important;
                    left: 0;
                    color: #0077d7;
                }

                a {
                    font-weight: var(--snjy-font-weight-bold);
                    text-decoration: underline;
                    color: #0086f9;
                }
            }
        }

        .upload-file-link {
            border-radius: 8px;
            padding: 0 40px;
            min-height: 50px;
            font-family: var(--snjy-font-family);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0 10px;
            font-size: var(--snjy-font-size-0-875);
            line-height: 16px;
            font-weight: var(--snjy-font-weight-medium);
            background-color: var(--snjy-font-color-primary) !important;
            color: var(--snjy-button-color-primary) !important;
            border: 1px solid var(--snjy-button-color-primary) !important;
        }
    }
}

.btn-primary {
    padding: 0 40px;
    min-height: 50px;
}