:host {
  min-width: 350px;
}

h3 {
  font-size: var(--snjy-font-size-1-25);
  font-weight: var(--snjy-font-weight-bold);
  color: var(--snjy-button-color-primary);
}

button,
button:disabled,
button:hover {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 0.8rem;
  padding: 0;
  min-height: 50px;
  font-family: var(--snjy-font-family);
  background-color: var(--snjy-button-color-primary);
  color: var(--snjy-color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0 5px;
  font-size: var(--snjy-font-size-0-875);
  line-height: 16px;
  font-weight: var(--snjy-font-weight-medium);
}

button:disabled {
  opacity: 0.8;
}

.btn-light:hover,
.btn-light {
  background-color: var(--snjy-font-color-primary);
  color: var(--snjy-button-color-primary);
  border: 1px solid var(--snjy-button-color-primary);
}

.checkout-id-summary {
  margin: 20px 0;
  flex: 2;

  .checkout-id-summary-body {
    margin: 0 0 20px 0;
    padding: 1rem;
    background: var(--snjy-color-white);
    border: 1px solid var(--snjy-border-color-secondary);
    border-radius: 10px;
    box-shadow: var(--snjy-box-shadow);
    position: relative;
    height: fit-content;

    h3 {
      margin: 0 0 3px 0;
      padding: 0;
      position: relative;
      font-size: var(--snjy-font-size-1-125);
      font-weight: var(--snjy-font-weight-bold);
      color: #00216c;
      line-height: 20px;
    }

    small {
      margin: 0 0 15px 0;
      padding: 0;
      font-size: var(--snjy-font-size-0-8);
      font-weight: var(--snjy-font-weight-semi-bold);
      color: #878787;
      display: block;
    }

    .item-box {
      margin: 0 0 10px 0;
      padding: 0 10px 0 0;
      display: flex;
      justify-content: flex-start;
      gap: 0 3%;
      position: relative;
      border-radius: 8px;
      border: 1px solid #e3e3e3;
      overflow: hidden;

      .item-box-img {
        margin: 0;
        padding: 0;
        position: relative;
        flex: 0 0 30%;
        height: 100px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      .item-box-content {
        margin: 0;
        padding: 0;
        position: relative;
        width: 100%;

        h4 {
          margin: 5px 0 0 0;
          padding: 0;
          position: relative;
          font-size: var(--snjy-font-size-0-875);
          font-weight: var(--snjy-font-weight-medium);
          color: var(--snjy-color-dark-secondary);
          line-height: 20px;
          display: block;
          max-width: 175px;
        }

        .item-box-list {
          display: flex;
          gap: 0 15px;
          margin: 2px 0 2px 0;
          flex-direction: column;
        }

        small {
          margin: 0;
          padding: 0;
          font-size: 10px;
          color: #687491;
          font-weight: var(--snjy-font-weight-medium);

          span {
            color: var(--snjy-button-color-primary);
          }
        }

        .quantity {
          margin: 0;
          padding: 0 0 0 4px;
          position: absolute;
          background: var(--snjy-color-white);
          display: inline-flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 4px;
          border: 1px solid #ffd383;
          font-size: 11px;
          font-weight: var(--snjy-font-weight-medium);
          overflow: hidden;
          gap: 0 8px;
          right: 0;
          bottom: 7px;
          color: var(--snjy-color-dark-secondary);

          span {
            margin: 0;
            padding: 0 3px;
            width: fit-content;
            height: 18px;
            background: #ffd383;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--snjy-color-dark-secondary);
          }
        }

        .item-price {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-8);
          font-weight: var(--snjy-font-weight-bold);
          color: #0086f9;
          line-height: 22px;
          text-align: right;
        }
      }
    }

    .checkout-summary-price {
      margin: 20px 0 0 0;

      ul {
        margin: 15px 0 15px 0;
        padding: 7px 18px;
        list-style: none;
        border-radius: 8px;
        background: #f0f5f6;

        li {
          margin: 10px 0;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 0 10px;
          color: #687491;
          font-weight: var(--snjy-font-weight-medium);
          font-size: var(--snjy-font-size-0-8);

          span {
            color: #0077d7;
          }
        }

        .total-price {
          margin: 5px 0 !important;
          font-size: 22px;
          color: var(--snjy-color-dark-secondary);

          span {
            font-size: var(--snjy-font-size-1-25);
            color: var(--snjy-color-dark-secondary);
            font-weight: var(--snjy-font-weight-bold);
          }
        }
      }
    }
  }
}