import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { CustomerComponent } from "./customer/customer.component";
import { ProductComponent } from "./product/product.component";

const routes: Routes = [
  {
    path: "customer",
    component: CustomerComponent,
    data: { permission: "P0029" },
  },
  {
    path: "product",
    component: ProductComponent,
    data: { permission: "P0028" },
  },
  {
    path: "contacts",
    loadChildren: () =>
      import("./contacts/contacts.module").then((m) => m.ContactsModule),
    data: { permission: "P0030" },
  },
  {
    path: "settings",
    loadChildren: () =>
      import("./settings/settings.module").then((m) => m.SettingsModule),
    data: { permission: "P0037" },
  },
  {
    path: "configs",
    loadChildren: () =>
      import("./config/config.module").then((m) => m.ConfigModule),
    data: { permission: "P0031" },
  },
  {
    path: "admin",
    loadChildren: () =>
      import("./administration/administration.module").then(
        (m) => m.AdministrationModule
      ),
    data: { permission: "P0032" },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BackOfficeRoutingModule {}
