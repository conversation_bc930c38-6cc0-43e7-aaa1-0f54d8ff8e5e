:host {
    padding: 25px;
}

.all-main-title-sec.cart-details-main-title {
    align-items: flex-start;
    justify-content: flex-start;
    text-align: left;
    padding: 0 0 25px 0;

    h1 {
        text-align: left;
        padding: 0;
        display: flex;
        align-items: center;
        gap: 0 10px;
        justify-content: space-between;

        >span {
            font-size: var(--snjy-font-size-0-75);
            color: var(--snjy-color-dark-secondary);
            top: 2px;
            position: relative;
            text-transform: uppercase;
            border: 1px solid #dfdfdf;
            padding: 5px 15px 5px 9px;
            border-radius: 6px;
            background: var(--snjy-color-white);
            font-weight: var(--snjy-font-weight-medium);
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 0 7px;

            .material-icons-outlined {
                width: fit-content;
                height: fit-content;
                font-size: var(--snjy-font-size-1-25);
                color: var(--snjy-button-color-primary);
            }
        }
    }

    .product-id {
        margin: 0;
        padding: 0;
        font-size: var(--snjy-font-size-0-9);
        font-weight: var(--snjy-font-weight-semi-bold);
        color: var(--snjy-color-text-ternary);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0 5px;
        line-height: 15px;

        span {
            margin: 0;
            padding: 0;
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-button-color-primary);
        }

        .material-icons-outlined {
            font-weight: var(--snjy-font-weight-normal);
            color: var(--snjy-color-text-ternary);
            font-size: var(--snjy-font-size-1-125);
            height: fit-content;
            width: fit-content;
        }
    }
}

.cart-id-sec {
    margin: 0;
    padding: 0;
    position: relative;

    .cart-id-body {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: space-between;
        gap: 0 3%;

        .cart-id-info {
            margin: 0;
            padding: 0;
            flex: 4;

            .cart-details {
                margin: 0 0 20px 0;
                padding: 24px;
                background: var(--snjy-color-white);
                border: 1px solid var(--snjy-border-color-secondary);
                border-radius: 10px;
                box-shadow: var(--snjy-box-shadow);

                .item-box {
                    margin: 0 0 18px 0;
                    padding: 16px;
                    background: #eff4f5;
                    display: flex;
                    justify-content: flex-start;
                    gap: 0 3%;
                    position: relative;
                    border-radius: 7px;

                    .item-box-img {
                        margin: 0;
                        padding: 0;
                        position: relative;
                        flex: 0 0 26%;
                        height: 144px;
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                        border-radius: 7px;
                        overflow: hidden;
                        border: 1px solid var(--snjy-button-color-primary);
                    }

                    .item-box-content {
                        margin: 0;
                        padding: 0;
                        position: relative;
                        width: 100%;

                        .close-btn {
                            background-color: var(--snjy-color-white);
                            color: #dd8b26;
                            border: 1px solid #c3c8cd;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            height: 26px;
                            width: 26px;
                            padding: 0;
                            position: absolute;
                            top: 0;
                            right: 0;
                            border-radius: 6px;

                            .material-icons-outlined {
                                font-size: var(--snjy-font-size-1);
                                width: fit-content;
                                height: fit-content;
                            }
                        }

                        h4 {
                            margin: 15px 0 0 0;
                            padding: 0;
                            position: relative;
                            font-size: var(--snjy-font-size-1);
                            font-weight: var(--snjy-font-weight-medium);
                            color: var(--snjy-color-dark-secondary);
                            line-height: 20px;
                            display: block;
                        }

                        small {
                            margin: 0;
                            padding: 0;
                            font-size: var(--snjy-font-size-0-75);
                            color: #687491;
                            font-weight: var(--snjy-font-weight-medium);
                        }

                        .item-box-bottom-content {
                            margin: 35px 0 0 0;
                            padding: 0;
                            position: relative;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;

                            .item-price {
                                margin: 0;
                                padding: 0;
                                position: relative;
                                font-size: var(--snjy-font-size-2);
                                font-weight: var(--snjy-font-weight-bold);
                                color: var(--snjy-color-dark-secondary);
                                line-height: 22px;
                                text-align: right;

                                span {
                                    margin: 0;
                                    padding: 0;
                                    position: relative;
                                    font-size: var(--snjy-font-size-0-75);
                                    font-weight: var(--snjy-font-weight-medium);
                                    color: #687491;
                                    display: block;
                                }
                            }

                        }
                    }
                }

            }
        }

        .cart-id-summary {
            margin: 0 0 20px 0;
            flex: 2;

            .cart-buttons-container {
                margin: -69px 0 19px;
                padding: 0;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 0 2%;

                .cart-btn {
                    font-size: 11px;
                    color: #4e4e4e;
                    top: 2px;
                    position: relative;
                    text-transform: uppercase;
                    border: 1px solid #dfdfdf;
                    padding: 8px 15px 8px 9px;
                    border-radius: 8px;
                    background: var(--snjy-color-white);
                    font-weight: var(--snjy-font-weight-medium);
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 0 7px;
                    box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);

                    .material-icons-outlined {
                        width: fit-content;
                        height: fit-content;
                        font-size: var(--snjy-font-size-1-25);
                        color: var(--snjy-button-color-primary);
                    }
                }
            }

            .cart-id-summary-body {
                margin: 0 0 20px 0;
                padding: 24px;
                background: var(--snjy-color-white);
                border: 1px solid var(--snjy-border-color-secondary);
                border-radius: 10px;
                box-shadow: var(--snjy-box-shadow);
                position: relative;
                height: fit-content;

                h3 {
                    margin: 0 0 3px 0;
                    padding: 0;
                    position: relative;
                    font-size: var(--snjy-font-size-1-125);
                    font-weight: var(--snjy-font-weight-bold);
                    color: #00216c;
                    line-height: 20px;
                }

                small {
                    margin: 10px 0 5px 0;
                    padding: 0;
                    font-size: var(--snjy-font-size-0-8);
                    font-weight: var(--snjy-font-weight-semi-bold);
                    color: #878787;
                    display: flex;
                    align-items: center;
                    gap: 0 6px;
                    position: absolute;
                    top: 14px;
                    right: 25px;

                    .material-icons-outlined {
                        margin: 0;
                        padding: 0;
                        font-size: var(--snjy-font-size-1);
                        width: fit-content;
                        height: fit-content;
                        color: var(--snjy-button-color-primary);
                    }
                }

                .cart-summary-price {
                    margin: 30px 0 0 0;

                    ul {
                        margin: 15px 0 15px 0;
                        padding: 7px 18px;
                        list-style: none;
                        border-radius: 8px;
                        background: #f0f5f6;

                        li {
                            margin: 10px 0;
                            padding: 0;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            gap: 0 10px;
                            color: #687491;
                            font-weight: var(--snjy-font-weight-medium);
                            font-size: var(--snjy-font-size-0-8);

                            span {
                                color: #0077d7;
                            }
                        }

                        .total-price {
                            margin: 5px 0 !important;
                            font-size: var(--snjy-font-size-1-25);
                            font-weight: var(--snjy-font-weight-bolder);
                            color: var(--snjy-color-dark-secondary);

                            span {
                                font-size: var(--snjy-font-size-1-25);
                                color: var(--snjy-color-dark-secondary);
                                font-weight: var(--snjy-font-weight-bold);
                            }
                        }

                    }

                    button {
                        width: 100%;
                        border-radius: 8px;
                        margin-bottom: 0.8rem;
                        padding: 0;
                        min-height: 50px;
                        font-family: var(--snjy-font-family);
                        background-color: var(--snjy-button-color-primary);
                        color: var(--snjy-color-white);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 0 5px;
                        font-size: var(--snjy-font-size-0-875);
                        line-height: 16px;
                        font-weight: var(--snjy-font-weight-medium);

                        &.btn-light {
                            background-color: var(--snjy-color-white);
                            color: var(--snjy-button-color-primary);
                            border: 1px solid var(--snjy-button-color-primary);
                        }
                    }

                    p.terms {
                        margin: 24px 0 0 0;
                        padding: 0 0 0 13px;
                        font-size: var(--snjy-font-size-0-8);
                        color: #687491;
                        font-weight: var(--snjy-font-weight-medium);
                        line-height: 20px;
                        a {
                            font-weight: bold;
                            color: var(--snjy-button-color-primary);
                        }
                    }
                }

            }

            .need-help-sec {
                margin: 0 0 20px 0;
                padding: 24px;
                background: var(--snjy-color-white);
                border: 1px solid rgba(208, 215, 216, 0.3882352941);
                border-radius: 10px;
                box-shadow: var(--snjy-box-shadow);
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 0 10px;

                .material-icons-outlined {
                    margin: 0;
                    padding: 0;
                    width: 50px !important;
                    height: 50px !important;
                    display: inline-flex !important;
                    align-items: center;
                    justify-content: center;
                    border-radius: 9px;
                    background: #ffc55c;
                }

                .need-help-details {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    font-size: var(--snjy-font-size-1);
                    font-weight: var(--snjy-font-weight-medium);
                    color: var(--snjy-color-dark-secondary);
                    line-height: 20px;
                    display: block;

                    span {
                        display: block;
                        font-size: var(--snjy-font-size-1-125);
                        margin: 5px 0 0 0;
                        color: var(--snjy-button-color-primary);
                        font-weight: var(--snjy-font-weight-bold);
                    }
                }

            }
        }
    }
}

.cart-empty {
    text-align: center;
    padding: 2rem;
}

button[disabled] {
    cursor: not-allowed;
}

@media only screen and (max-width: 1024px) {
    .cart-id-sec {
        .cart-id-body {
            flex-wrap: wrap;

            .cart-id-summary {
                .cart-buttons-container {
                    margin: 0 0 19px;
                    flex-wrap: wrap;
                    gap: 10px;

                    .cart-btn {
                        width: 48% !important;
                        font-size: 10px;
                        justify-content: flex-start;
                    }
                }

                .need-help-sec {
                    padding: 12px;
                }

                .cart-id-summary-body small {
                    margin: 10px 0 0 0;
                    gap: 0 6px;
                    position: relative;
                    top: 0;
                    right: 0;
                }
            }
        }
    }
}

@media only screen and (max-width: 900px) {
    .cart-id-sec {
        .cart-id-body {
            flex-wrap: wrap;

            .cart-id-info {
                width: 100%;
                flex: 100%;
                margin-top: 110px;
            }

            .cart-id-summary {
                flex: 100%;
            }

            .cart-buttons-container {
                position: absolute !important;
                top: 0;
                width: 100%;
            }

        }
    }
}

@media only screen and (max-width: 576px) {
    .cart-id-sec {
        .cart-id-body {
            .cart-id-info {
                .item-box {
                    flex-direction: column;

                    .item-box-img {
                        min-height: 180px;
                    }

                    .item-box-content {
                        position: unset !important;

                        .close-btn {
                            top: 15px !important;
                            right: 15px !important;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 480px) {
    .cart-id-sec {
        .cart-id-body {
            .cart-id-info {
                .item-box {
                    flex-direction: column;

                    .item-box-img {
                        min-height: 180px;
                    }

                    .item-box-content {
                        .item-box-bottom-content {
                            margin: 15px 0 0 0 !important;

                            .item-price {
                                font-size: var(--snjy-font-size-1-125) !important;
                            }
                        }
                    }
                }

                .cart-details {
                    padding: 0;
                    background: #FFF;
                    border: none !important;
                    box-shadow: none !important;
                }
            }
        }
    }
}