import { Component, OnInit, Input } from "@angular/core";
import {
  ColDef,
  ColumnMovedEvent,
  GridOptions,
  IGetRowsParams,
} from "ag-grid-community";
import { map } from "rxjs";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

import { GridColumn } from "src/app/shared/components/grid-column/grid-column.model";
import { ManageUserService } from "./manage-user-service/manage-user.service";
import { RegisterUserComponent } from "./register-user/register-user.component";
import { RolesType } from "src/app/constants/api.constants";

@Component({
  selector: "app-manage-users",
  templateUrl: "./manage-users.component.html",
  styleUrls: ["./manage-users.component.scss"],
})
export class ManageUsersComponent implements OnInit {
  @Input() userRoleType: any = `${RolesType.BACKOFFICE},${RolesType.CUST_SERVICE},${RolesType.SALES},${RolesType.VENDOR}`;
  public columnDefs: ColDef[] = [
    {
      field: "id",
      headerName: "ID",
      sortable: false,
      filter: false,
      hide: true,
    },
    {
      field: "first_name",
      headerName: "First Name",
      sortable: true,
      filter: true,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      sortable: true,
      filter: true,
    },
    {
      field: "display_roles",
      headerName: "Roles",
      sortable: true,
      filter: true,
    },
    {
      field: "email",
      headerName: "Email Id",
      sortable: true,
      filter: true,
    },
    {
      field: "contact_address",
      headerName: "Address",
      sortable: true,
      filter: true,
    },
  ];
  public defaultColDef: ColDef = {
    flex: 1,
    minWidth: 200,
    filter: false,
  };
  private gridApi!: any;
  private gridColumnApi!: any;
  public pageSize = 10;
  public searchText: string = "";
  public remoteGridBinding = this;
  public model: any;
  public rowClicked: Boolean = false;

  constructor(
    private manageUserService: ManageUserService,
    private dialog: NgbModal
  ) {}

  ngOnInit(): void {}

  gridOptions: GridOptions = {
    onColumnMoved: (event: ColumnMovedEvent) => this.onColumnMoved(event),
  };

  openDialog() {
    const dialogRef = this.dialog.open(RegisterUserComponent);
    dialogRef.componentInstance.userRoleType = this.userRoleType;
    dialogRef.result.then((result) => {
      this.onSearchChange("");
    });
  }

  onColumnMoved(params: any) {
    const columnDragState = params.columnApi.getColumnState();
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map((e: { colDef: any }) => {
        return e.colDef;
      });
    const newColDef: ColDef[] = colIds.map((item: any, i: string | number) =>
      Object.assign({}, item, columnDragState[i])
    );
    this.columnDefs = newColDef;
  }

  onGridReady(params: { api: any; columnApi: any }) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
  }

  getData(params: IGetRowsParams) {
    this.gridApi.showLoadingOverlay();
    return this.manageUserService
      .getUsers({
        perPage: this.pageSize,
        search: this.searchText,
        pageNo: params.endRow / this.pageSize,
        userRoleType: this.userRoleType,
      })
      .pipe(
        map((value: any) => {
          this.gridApi.hideOverlay();
          return {
            data: value.data,
            totalRecords: !value.data.length ? 0 : value.total,
          };
        })
      );
  }

  onSearchChange(text: string) {
    this.searchText = text;
    this.gridApi.paginationGoToPage(0);
    setTimeout(() => {
      this.gridApi.purgeInfiniteCache();
    }, 100);
  }

  onCellClicked(row: any) {
    this.model = row.data;
    this.rowClicked = true;
  }

  onColumnChange(columns: any[]) {
    const { displayed, hidden } = this.getDetaildColumns(columns);
    this.gridColumnApi.setColumnsVisible(displayed, true);
    this.gridColumnApi.setColumnsVisible(hidden, false);
    this.gridApi.sizeColumnsToFit();
  }

  onColumnPositionChange(position: number[]) {
    this.gridColumnApi.moveColumnByIndex(position[0], position[1]);
    this.gridApi.sizeColumnsToFit();
  }

  getDetaildColumns(columns: GridColumn[]) {
    const obj: any = {
      displayed: [],
      hidden: [],
    };
    for (let i = 0; i < columns.length; i++) {
      const element: GridColumn = columns[i];
      (element.show ? obj.displayed : obj.hidden).push(element.field);
    }
    return obj;
  }

  refreshRowData(data: any) {
    const rowNode = this.gridApi.getRowNode(data.id.toString())!;
    rowNode && rowNode.setData(data);
  }

  getRowId(params: any) {
    return params.data.id.toString();
  }
}
