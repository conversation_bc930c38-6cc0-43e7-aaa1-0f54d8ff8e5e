import { Component, OnInit } from "@angular/core";
import { AppToastService } from "src/app/shared/services/toast.service";
import { ActivatedRoute, Router } from "@angular/router";
import moment from "moment";

import { OrderDetailsService } from "./order-details.service";
import { AppConstant } from "src/app/constants/api.constants";
import { OrderHistoryService } from "../order-history/order-history.service";
import { CustomerService } from "../../backoffice/customer/customer.service";
import { CartService } from "../services/cart.service";

@Component({
  selector: "app-order-details",
  templateUrl: "./order-details.component.html",
  styleUrls: ["./order-details.component.scss"],
})
export class OrderDetailsComponent implements OnInit {
  public loading = false;
  public orderDetails: any = {};
  public subTotal = 0;
  public salesTax = 0;
  public total = 0;
  public shipping = 0;
  public moment = moment;
  public statuses: any = {};
  public customer: any = null;
  public shipToParty: any = null;
  public cart: any = null;
  public isReordering: any = false;
  public orderType: any = null;

  constructor(
    public router: Router,
    private service: OrderDetailsService,
    private orderHistoryService: OrderHistoryService,
    private _snackBar: AppToastService,
    private activatedRoute: ActivatedRoute,
    private customerService: CustomerService,
    private cartService: CartService
  ) {
    this.cartService.getCart().subscribe((cart) => {
      this.cart = cart;
    });
  }

  ngOnInit() {
    this.getAllStatus();
    this.activatedRoute.paramMap.subscribe((params) => {
      const id = params.get("id");
      this.orderType = params.get("type");
      if (id && this.orderType) {
        this.getOrderDetails(id, this.orderType);
      }
    });
  }

  getPartnerFunction(soldToParty: string, shipToParty: string) {
    this.customerService.getPartnerFunction(soldToParty).subscribe({
      next: (value: any) => {
        this.customer = value.find(
          (o: any) =>
            o.customer_id === soldToParty && o.partner_function === "SP"
        );
        this.shipToParty = value.find(
          (o: any) =>
            o.bp_customer_number === shipToParty && o.partner_function === "SH"
        );
      },
      error: (err) => {
        this._snackBar.open("Error while processing get ship to request.", { type: 'Error' });
      },
    });
  }

  getAllStatus() {
    this.loading = true;
    return this.orderHistoryService.getAllStatuses().subscribe({
      next: (value) => {
        if (value?.data?.length) {
          value.data.reduce(
            (
              acc: { [x: string]: any },
              value: { code: string | number; description: any }
            ) => {
              acc[value.code] = value.description;
              return acc;
            },
            this.statuses
          );
        }
      },
      error: () => {
        this.loading = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  getOrderDetails(orderId: string, orderType: string) {
    this.loading = true;
    this.service.get(orderId, orderType).subscribe({
      next: (value: any) => {
        if (value.data?.SALESORDER) {
          this.orderDetails = value.data.SALESORDER;
          this.getPartnerFunction(
            this.orderDetails?.SOLDTO?.SOLDTOPARTY,
            this.orderDetails?.SHIPTO?.SHIPTOPARTY
          );
          this.setOtherDetails(this.orderDetails);
        } else {
          this._snackBar.open("No data received.");
        }
        this.loading = false;
      },
      error: (err) => {
        this.loading = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  setOtherDetails(data: any) {
    if (!data.ORDER_LINE_DETAIL?.length) return;
    for (let j = 0; j < data.ORDER_LINE_DETAIL.length; j++) {
      const item = data.ORDER_LINE_DETAIL[j];
      this.setImage(item);
    }
  }

  setImage(item: any) {
    item.imageUrl = AppConstant.PRODUCT_IMAGE_FALLBACK;
    this.service.getImages(item.MATERIAL).subscribe({
      next: (value: any) => {
        if (value?.data?.length) {
          const images = value.data.filter(
            (item: any) => item.dimension == "1200X1200"
          );
          if (images.length) {
            item.imageUrl = images[0].url;
          }
        }
      },
    });
  }

  upsert(array: any, element: any) {
    const i = array.findIndex(
      (_element: any) => _element.Material === element.Material
    );
    if (i > -1) {
      const totalRQ =
        parseInt(array[i]?.RequestedQuantity) +
        parseInt(element.RequestedQuantity);
      array[i].RequestedQuantity = totalRQ.toString();
    } else {
      array.push(element);
    }
  }

  async reorder() {
    const OLD = this.orderDetails?.ORDER_LINE_DETAIL || [];
    OLD.forEach((element: any) => {
      const obj: any = {};
      obj.Material = element.MATERIAL;
      obj.RequestedQuantity = element.REQ_QTY;
      obj.to_PricingElement = [];
      this.upsert(this.cart.to_Item, obj);
    });
    this.isReordering = true;
    await this.cartService.salesOrderSimulation();
    this.isReordering = false;
    this.router.navigate([`/store/cart`]);
  }
}
