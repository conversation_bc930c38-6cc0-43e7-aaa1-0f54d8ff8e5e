<div class="wrapper d-flex flex-column align-items-end px-2 py-0">
    <div class="d-flex justify-content-between bck-contact">
        <button class="btn btn-primary" (click)="openDialog()">
            <i class="material-icons-outlined">call</i>
            Register {{userRoleType==='STOREFRONT'? 'Contact' : 'User'}}
        </button>
        <div class="d-flex">
            <input type="text" [ngModel]="searchText" class="form-control me-2" placeholder="Search..."
                (ngModelChange)="onSearchChange($event)">
            <app-grid-column [columns]="columnDefs" buttonText="Columns" selectableChecked="show"
                (columnChange)="onColumnChange($event)" (columnPositionChange)="onColumnPositionChange($event)">
            </app-grid-column>
        </div>
    </div>
    <div class="w-100 my-2">
        <ag-grid-angular [getRowId]="getRowId" style="width: 100%; height: 400px;"
            class="ag-theme-alpine bck-table-body" [columnDefs]="columnDefs" [defaultColDef]="defaultColDef"
            (cellClicked)="onCellClicked($event)" [pagination]="true" [rowModelType]="'infinite'"
            [cacheBlockSize]="pageSize" [remoteGridBinding]="remoteGridBinding" [paginationPageSize]="pageSize"
            (remoteGridReady)="onGridReady($event)" [gridOptions]="gridOptions" [paginationPageSizeSelector]="[10, 20, 50, 100]"></ag-grid-angular>
    </div>
</div>
<app-user-detail [model]="model" *ngIf="rowClicked" (onUpdate)="refreshRowData($event)"
    class="bck-table-details contact-tab-links" [userRoleType]="userRoleType" />