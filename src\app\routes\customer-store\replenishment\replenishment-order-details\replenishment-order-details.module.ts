import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { SharedModule } from "src/app/shared/shared.module";
import { ReplenishmentOrderDetailsComponent } from "./replenishment-order-details.component";

@NgModule({
  declarations: [ReplenishmentOrderDetailsComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: ReplenishmentOrderDetailsComponent }]),
  ],
})
export class ReplenishmentOrderDetailsModule { }
