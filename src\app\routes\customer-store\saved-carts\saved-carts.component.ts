import { Component, OnInit } from '@angular/core';
import { SavedCartsService } from './saved-carts.service';
import moment from 'moment';
import { RestoreCartComponent } from './restore-cart/restore-cart.component';
import { CartService } from '../services/cart.service';
import { Router } from '@angular/router';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-saved-carts',
  templateUrl: './saved-carts.component.html',
  styleUrls: ['./saved-carts.component.scss']
})
export class SavedCartsComponent implements OnInit {

  data: any = [];
  loading = false;
  moment = moment;

  constructor(
    private service: SavedCartsService,
    public router: Router,
    private cartService: CartService,
    private _snackBar: AppToastService,
    public dialog: NgbModal
  ) { }

  ngOnInit(): void {
    this.fetchData();
  }

  fetchData(): void {
    this.loading = true;

    this.service.get().subscribe({
      next: (value: any) => {
        this.loading = false
        this.data = value.data.map((d: any) => {
          if (d.payload && d.payload.length) {
            d.total = d.payload.reduce((a: any, b: any) => {
              return a + (parseFloat(b?.ConditionAmount) || 0);
            }, 0);
          }
          return d;
        });
      },
      error: (err) => {
        this.loading = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      }
    })
  }

  delete(data: any) {
    this.service.delete(data.id).subscribe({
      next: (value) => {
        this.fetchData();
        this._snackBar.open('Cart deleted.');
      },
      error: (err) => {
        this.loading = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      }
    });
  }

  restore(data: any) {
    const dialogRef = this.dialog.open(RestoreCartComponent);
    dialogRef.componentInstance.data = data;
    dialogRef.result.then((result: any) => {
      this.fetchData();
      this.cartService.setSOSReq(result?.data?.so_simulation_req_data);
      this.cartService.salesOrderSimulation();
    });
  }

  goToCart(cart: any) {
    this.router.navigate(["/store/saved-cart-details", cart.id]);
  }

}
