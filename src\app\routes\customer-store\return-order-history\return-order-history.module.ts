import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { NgbDatepickerModule } from "@ng-bootstrap/ng-bootstrap";

import { ReturnOrderHistoryComponent } from "./return-order-history.component";
import { SharedModule } from "src/app/shared/shared.module";

@NgModule({
  declarations: [ReturnOrderHistoryComponent],
  imports: [
    CommonModule,
    SharedModule,
    NgbDatepickerModule,
    RouterModule.forChild([
      { path: "", component: ReturnOrderHistoryComponent },
    ]),
  ],
})
export class ReturnOrderHistoryModule {}
