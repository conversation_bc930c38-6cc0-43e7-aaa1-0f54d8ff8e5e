<div class="bck-table-details config-tab-links">
    <asar-tabs (activeTab)="onTabChange($event)">
        <asar-tab [tabTitle]="tab.title" *ngFor="let tab of tabs">
            <ng-container *ngIf="tab.subTabs?.length">
                <asar-tabs (activeTab)="onTabChange($event, tab)" class="sub-tab">
                    <asar-tab [tabTitle]="subTab.title" *ngFor="let subTab of tab.subTabs">
                        <ng-container *ngTemplateOutlet="tabcontent;context:{tab: subTab, loading}"></ng-container>
                    </asar-tab>
                </asar-tabs>
            </ng-container>
            <ng-container *ngIf="!tab.subTabs?.length">
                <ng-container *ngTemplateOutlet="tabcontent;context:{tab, loading}"></ng-container>
            </ng-container>
        </asar-tab>
    </asar-tabs>
</div>


<ng-template #tabcontent let-loading="loading" let-tab="tab">
    <ng-container &ngIf="!loading">
        <div class="table-responsive" [class.hasDetails]="tab.id == 'catalogs' || tab.id == 'categories'">
            <table class="table table-striped table-hover">
                <thead *ngIf="tab.type !== 'multiValue'">
                    <tr>
                        <th *ngFor="let column of tab.columns">{{column.name}}</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngIf="tab.type === 'multiValue'">
                        <tr *ngFor="let column of tab.columns">
                            <td>
                                {{column.name}}
                            </td>
                            <td>
                                <input type="text" [(ngModel)]="column.itemValue" [placeholder]="column.name"
                                    class="form-control" [maxlength]="column.maxlength || 100"/>
                            </td>
                            <td>
                                <button class="btn btn-primary ms-2" (click)="saveSettings(tab, column)"
                                    [disabled]="isDisabled(tab)">
                                    <i class="material-icons-outlined">save</i>
                                </button>
                            </td>
                        </tr>
                    </ng-container>
                    <tr *ngIf="tab.type !== 'multiValue' && tab?.creation">
                        <td *ngFor="let column of tab.columns">
                            <ng-container [ngSwitch]="column.type">
                                <ng-container *ngSwitchCase="'radio'">
                                    <div class="form-check form-check-inline" *ngFor="let option of column.options">
                                        <input class="form-check-input" type="radio" [value]="option.value"
                                            [name]="column.name" [(ngModel)]="column.itemValue">
                                        <label class="form-check-label">{{option.name}}</label>
                                    </div>
                                </ng-container>
                                <ng-container *ngSwitchCase="'dd'">
                                    <ng-container *ngIf="column.multiple">
                                        <select class="form-select" [(ngModel)]="column.itemValue" multiple>
                                            <option>Please Select</option>
                                            <option *ngFor="let option of column.options;trackBy: trackByFn1" [value]="option.value">
                                                {{option.name}}
                                            </option>
                                        </select>
                                    </ng-container>
                                    <ng-container *ngIf="!column.multiple">
                                        <select class="form-select" [(ngModel)]="column.itemValue">
                                            <option>Please Select</option>
                                            <option *ngFor="let option of column.options;trackBy: trackByFn1" [value]="option.value">
                                                {{option.name}}
                                            </option>
                                        </select>
                                    </ng-container>
                                </ng-container>
                                <ng-container *ngSwitchCase="'number'">
                                    <input type="number" [(ngModel)]="column.itemValue" [required]="column.required"
                                        [placeholder]="column.name" class="form-control" [min]="1"
                                        [disabled]="loading" />
                                </ng-container>
                                <ng-container *ngSwitchCase="'checkbox'">
                                    <input type="checkbox" [(ngModel)]="column.itemValue" [required]="column.required"
                                        class="form-control checkbox-input m-1 shadow-none" [disabled]="loading" />
                                </ng-container>
                                <input *ngSwitchDefault type="text" [(ngModel)]="column.itemValue"
                                    [maxlength]="column.length" [placeholder]="column.name" class="form-control" />
                            </ng-container>
                        </td>
                        <td>
                            <button class="btn btn-primary ms-2"
                                (click)="tab.type === 'SingleValue' ? saveSettings(tab) : addRow(tab)"
                                [disabled]="isDisabled(tab)">
                                <i class="material-icons-outlined">{{ tab.type === 'SingleValue' ? 'save' : 'add' }}</i>
                            </button>
                        </td>
                    </tr>
                    <ng-container *ngIf="tab.type !== 'SingleValue' && tab.type !== 'multiValue'">
                        <ng-container *ngIf="!tab.data.length && !tab.hasErrors">
                            <tr>
                                <td [attr.colspan]="tab.columns.length + 1">No records found.</td>
                            </tr>
                        </ng-container>
                        <ng-container *ngIf="tab.hasErrors">
                            <tr>
                                <td [attr.colspan]="tab.columns.length + 1">Error while processing your request.</td>
                            </tr>
                        </ng-container>
                        <ng-container *ngIf="tab.data.length">
                            <tr *ngFor="let item of tab.data;let i = index; trackBy: trackByFn" (click)="selectRow(item, tab);">
                                <td *ngFor="let column of tab.columns">
                                    <ng-container [ngSwitch]="column.type" *ngIf="item.editing">
                                        <ng-container *ngSwitchCase="'radio'">
                                            <div class="form-check form-check-inline"
                                                *ngFor="let option of column.options">
                                                <input class="form-check-input" type="radio" [value]="option.value"
                                                    [name]="column.name + i" [(ngModel)]="item[column.newValue]">
                                                <label class="form-check-label">{{option.name}}</label>
                                            </div>
                                        </ng-container>
                                        <ng-container *ngSwitchCase="'dd'">
                                            <ng-container *ngIf="column.multiple">
                                                <select class="form-select" [(ngModel)]="item[column.newValue]"
                                                    multiple>
                                                    <option>Please Select</option>
                                                    <ng-container *ngFor="let option of column.options;trackBy: trackByFn1">
                                                        <option *ngIf="option.value != item.id" [value]="option.value">
                                                            {{option.name}}
                                                        </option>
                                                    </ng-container>
                                                </select>
                                            </ng-container>
                                            <ng-container *ngIf="!column.multiple">
                                                <select class="form-select" [(ngModel)]="item[column.newValue]">
                                                    <option>Please Select</option>
                                                    <ng-container *ngFor="let option of column.options;trackBy: trackByFn1">
                                                        <option *ngIf="option.value != item.id" [value]="option.value">
                                                            {{option.name}}
                                                        </option>
                                                    </ng-container>
                                                </select>
                                            </ng-container>
                                        </ng-container>
                                        <ng-container *ngSwitchCase="'checkbox'">
                                            <input type="checkbox" [(ngModel)]="item[column.newValue]"
                                                [required]="column.required" class="form-control checkbox-input" />
                                        </ng-container>
                                        <ng-container *ngSwitchDefault>
                                            <ng-container *ngIf="column?.readonly">
                                                <span>{{item[column.value]}}</span>
                                            </ng-container>
                                            <ng-container *ngIf="!column?.readonly">
                                                <input type="text" [(ngModel)]="item[column.newValue]"
                                                    class="form-control" />
                                            </ng-container>
                                        </ng-container>
                                    </ng-container>
                                    <ng-container [ngSwitch]="column.type" *ngIf="!item.editing">
                                        <ng-container *ngSwitchCase="'dd'">
                                            <ng-container *ngIf="column.multiple">
                                                <select class="form-select" [(ngModel)]="item[column.value]" multiple
                                                    disabled>
                                                    <option></option>
                                                    <ng-container *ngFor="let option of column.options;trackBy: trackByFn1">
                                                        <option [value]="option.value">
                                                            {{option.name}}
                                                        </option>
                                                    </ng-container>
                                                </select>
                                            </ng-container>
                                            <ng-container *ngIf="!column.multiple">
                                                <select class="form-select" [(ngModel)]="item[column.value]" disabled>
                                                    <option></option>
                                                    <ng-container *ngFor="let option of column.options;trackBy: trackByFn1">
                                                        <option [value]="option.value">
                                                            {{option.name}}
                                                        </option>
                                                    </ng-container>
                                                </select>
                                            </ng-container>
                                        </ng-container>
                                        <ng-container *ngSwitchCase="'checkbox'">
                                            <input type="checkbox" [(ngModel)]="item[column.value]"
                                                [required]="column.required" class="form-control checkbox-input"
                                                disabled />
                                        </ng-container>
                                        <ng-container *ngSwitchDefault>
                                            <span>{{item[column.value]}}</span>
                                        </ng-container>
                                    </ng-container>
                                </td>
                                <td width="150">
                                    <div class="d-flex">
                                        <ng-container *ngIf="tab.id != 'catalogs' && tab.id != 'categories'">
                                            <button class="btn btn-primary ms-2" (click)="edit(item, tab)"
                                                *ngIf="tab?.updation && !item.editing">
                                                <i class="material-icons-outlined">edit</i>
                                            </button>
                                            <button class="btn btn-primary ms-2" (click)="update(item, tab)"
                                                *ngIf="tab?.updation && item.editing">
                                                <i class="material-icons-outlined">done</i>
                                            </button>
                                            <button class="btn btn-primary ms-2" (click)="cancelEditing(item, tab)"
                                                *ngIf="tab?.updation && item.editing">
                                                <i class="material-icons-outlined">close</i>
                                            </button>
                                        </ng-container>
                                        <button class="btn btn-primary ms-2"
                                            (click)="$event.stopPropagation();remove(item, tab)"
                                            *ngIf="tab?.deletion && !item.editing">
                                            <i class="material-icons-outlined">delete</i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </ng-container>
                    </ng-container>
                </tbody>
            </table>
        </div>
        <ng-container [ngSwitch]="tab.id">
            <ng-container *ngSwitchCase="'catalogs'">
                <app-catalog [model]="model" [categories]="categories" *ngIf="model"
                    (dataUpdated)="dataUpdated($event)"></app-catalog>
            </ng-container>
            <ng-container *ngSwitchCase="'categories'">
                <app-categories [model]="model" [categories]="categories" 
                    [catalogs]="catalogs" *ngIf="model"
                    (dataUpdated)="dataUpdated($event)"></app-categories>
            </ng-container>
        </ng-container>
    </ng-container>
    <div *ngIf="loading">Loading...</div>
</ng-template>