import { Component } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON>, Validators } from "@angular/forms";
import { AuthService } from "../../../core/authentication/auth.service";
import { Router } from "@angular/router";
import { catchError, of } from "rxjs";
import { ForgotPasswordComponent } from "../forgot-password/forgot-password.component";
import { RolesType } from "src/app/constants/api.constants";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { environment } from "src/environments/environment";

@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
})
export class LoginComponent {
  public API_ENDPOINT: any = environment.apiEndpoint;
  public isSubmitting = false;
  public loginForm = this.fb.nonNullable.group({
    email: ["", [Validators.required, Validators.email]],
    password: ["", [Validators.required]],
    rememberMe: [false],
  });
  public errMsg: any = null;

  constructor(
    private fb: FormBuilder,
    private auth: AuthService,
    public dialog: NgbModal,
    public router: Router
  ) {}

  get email() {
    return this.loginForm.get("email")!;
  }

  get password() {
    return this.loginForm.get("password")!;
  }

  get rememberMe() {
    return this.loginForm.get("rememberMe")!;
  }

  login() {
    this.isSubmitting = true;
    this.auth
      .login(this.email.value, this.auth.encryptString(this.password.value))
      .pipe(catchError((err) => of(err.error)))
      .subscribe((res: any) => {
        this.isSubmitting = false;
        if (res === "success") {
          this.checkRoleType();
        } else if (res?.status === "error") {
          this.errMsg = res.message;
        }
      });
  }

  private checkRoleType() {
    switch (this.auth.role) {
      case RolesType.STOREFRONT:
        this.router.navigate(["store"]);
        break;
      case RolesType.CUST_SERVICE:
        this.router.navigate(["store/customer-services"]);
        break;
      case RolesType.SALES:
        this.router.navigate(["/store/sales"]);
        break;
      case RolesType.VENDOR:
        this.router.navigate(["/store/vendor"]);
        break;
      default:
        this.router.navigate(["backoffice"]);
    }
  }

  reset() {
    this.loginForm.reset();
    this.errMsg = null;
  }

  forgotPassword() {
    this.dialog.open(ForgotPasswordComponent);
  }
}
