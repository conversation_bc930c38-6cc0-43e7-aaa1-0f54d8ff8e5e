<div class="all-main-title-sec">
  <h1>Invoices</h1>
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>Invoices</li>
    </ul>
  </div>
</div>
<app-loader *ngIf="loadingPdf"></app-loader>
<div class="invoice-sec">
  <div class="invoice-body">
    <div class="invoice-contact-list">
      <div class="invoice-c-box">
        <div class="invoice-c-icon"><img src="/assets/images/seller-icon.png" alt="" title="" /></div>
        <div class="invoice-c-details">
          <h4>Customer #</h4>
          <small>{{sellerDetails.bp_customer_number}}</small>
        </div>
      </div>
      <div class="invoice-c-box">
        <div class="invoice-c-icon"><img src="/assets/images/phone-icon.png" alt="" title="" /></div>
        <div class="invoice-c-details">
          <h4>Customer Name</h4>
          <small>{{sellerDetails.name}}</small>
        </div>
      </div>
      <div class="invoice-c-box address-box">
        <div class="invoice-c-icon"><img src="/assets/images/address-icon.png" alt="" title="" /></div>
        <div class="invoice-c-details">
          <h4>ADDRESS</h4>
          <small>{{sellerDetails.address}}</small>
        </div>
      </div>
    </div>
    <div class="inv-form-sec all-form-res">
      <div class="form">
        <div class="form-group">
          <label><span class="material-icons-outlined">calendar_month</span> Date From</label>
          <div class="input-group">
            <input class="form-control" name="picker1" [(ngModel)]="searchParams.fromDate" ngbDatepicker
              #d="ngbDatepicker" [maxDate]="today()" />
            <button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
              <i class="material-icons-outlined">
                calendar_month
              </i>
            </button>
          </div>
        </div>
        <div class="form-group">
          <label><span class="material-icons-outlined">calendar_month</span> Date To</label>
          <div class="input-group">
            <input class="form-control" name="picker2" [(ngModel)]="searchParams.toDate" ngbDatepicker
              #d1="ngbDatepicker" [minDate]="searchParams.fromDate" [maxDate]="today()" />
            <button class="btn btn-outline-secondary" (click)="d1.toggle()" type="button">
              <i class="material-icons-outlined">
                calendar_month
              </i>
            </button>
          </div>
        </div>
        <div class="form-group">
          <label><span class="material-icons-outlined">feed</span> Invoice Types</label>
          <select class="form-control select-arrow-down" [(ngModel)]="searchParams.type">
            <option *ngFor="let type of types" [value]="type.code">
              {{ type.description }}
            </option>
          </select>
        </div>
        <div class="form-group">
          <label><span class="material-icons-outlined">feed</span> Invoice Status</label>
          <select class="form-control select-arrow-down" [(ngModel)]="searchParams.status">
            <option *ngFor="let status of statuses" [value]="status.code">
              {{ status.description }}
            </option>
          </select>
        </div>

        <div class="form-btn-sec d-flex justify-content-center gap-1">
          <button type="button" class="mx-4 order-s-btn" (click)="clear()">Clear</button>
          <button type="button" class="mx-4 order-s-btn" (click)="search()" [disabled]="loading">{{loading ?
            'Searching...' : 'Search'}}</button>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="invoice-table">
  <app-grid [columns]="columnDefs" [data]="invoices" *ngIf="!loading && invoices.length"></app-grid>
  <div class="w-100" *ngIf="loading || !invoices.length">{{ loading ? 'Loading...' : 'No records found.' }}
  </div>
</div>