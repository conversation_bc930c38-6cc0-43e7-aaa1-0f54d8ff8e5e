import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { AuthService } from 'src/app/core/authentication/auth.service';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { ReturnOrderService } from '../return-order.service';
import { ProductReturnService } from '../../product-return/product-return.service';

@Component({
  selector: 'app-return-order-details',
  templateUrl: './return-order-details.component.html',
  styleUrls: ['./return-order-details.component.scss']
})
export class ReturnOrderDetailsComponent {
  private ngUnsubscribe = new Subject<void>();
  public moment = moment;
  public loading = false;
  public returnOrderId: any = null;
  public refDocId: any = null;
  public returnOrderDetail: any = null;
  public sellerDetails: any = {};
  public statuses: any = [];
  public returnReason: any = [];

  constructor(
    private _snackBar: AppToastService,
    private activatedRoute: ActivatedRoute,
    public productReturnService: ProductReturnService,
    public service: ReturnOrderService,
    public authService: AuthService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit() {
    this.getReasonReturn();
    this.activatedRoute.paramMap.subscribe((params) => {
      const id = params.get("returnOrderId");
      const refDocId = params.get("refDocId");
      if (id && refDocId) {
        this.returnOrderId = id;
        this.refDocId = refDocId;
        this.loading = true;
        this.getOrderDetails();
      }
    });
  }

  getReasonReturn() {
    this.productReturnService
      .getAllReturnReason()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.returnReason = res?.data || [];
        },
        error: (err) => {
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  getOrderDetails() {
    const payload: any = {
      SD_DOC: this.returnOrderId,
      DOC_TYPE: "CBAR",
      REF_SD_DOC: this.refDocId
    };
    const status$ = this.service.getAllStatus();
    const details$ = this.service.getRetrunOrderDetails(payload);
    forkJoin([status$, details$])
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (result: any) => {
          this.statuses = result[0]?.data || [];
          this.returnOrderDetail = result[1]?.data?.RETURNORDER || null;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  getStatusName(code: string) {
    const status = this.statuses.find((o: any) => o.code === code);
    if (status) {
      return status.description;
    }
    return "";
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
