# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code. 
# Add steps that build, run tests, deploy, and more:  
# https://aka.ms/yaml

resources:
  containers:
  - container: cfcli
    image: 'ajayrdockerpika/node16cfcli:v1'
    options: --user 0:0 --privileged

trigger:
- master

variables:
- group: CF_SecretKeys

stages:
- stage: deploy
  displayName: Deployment to BTP
  jobs:
    - job: deploy
      pool:
        vmImage: 'ubuntu-latest'
      container: cfcli
      steps:
        - script: cd '$(System.DefaultWorkingDirectory)'
        - script: npm install --legacy-peer-deps
        - script: npm run build-staging
        - script: cd '$(System.DefaultWorkingDirectory)/dist/ppfe'
        - script: cf login -a $(API_ENDPOINT) -u $(USER) -p $(PASSWORD) -o "ASAR AMERICA INC._portaldev-foundry" -s $(SPACE_DEV)
        - script: cf push   