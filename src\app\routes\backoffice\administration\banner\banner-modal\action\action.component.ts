import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";

@Component({
  selector: "app-action",
  templateUrl: "./action.component.html",
  styleUrls: ["./action.component.scss"],
})
export class ActionComponent implements ICellRendererAngularComp {
  private params: any;
  public label: string;
  public rowData: any[] = [];

  agInit(params: any): void {
    this.params = params;
    this.label = this.params.label || null;
    this.getRowData();
  }

  refresh(params?: any): boolean {
    this.params = params;
    this.getRowData();
    return true;
  }

  getRowData() {
    this.rowData = [];
    this.params.api.forEachNode((rowNode: any) => {
      this.rowData.push(rowNode.data);
    });
  }

  onClick($event: any, action: string) {
    if (this.params.onClick instanceof Function) {
      // put anything into params u want pass into parents component
      const params = {
        action,
        event: $event,
        rowData: this.params.node.data,
        // ...something
      };
      this.params.onClick(params);
    }
  }
}
