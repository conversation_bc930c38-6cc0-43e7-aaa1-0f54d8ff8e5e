import { Directive, ElementRef, HostListener, Input } from "@angular/core";
import { AppConstant } from "src/app/constants/api.constants";

@Directive({
    selector: 'img[appImgFallback]'
})
export class ImgFallbackDirective {
    @Input() appImgFallback!: string;

    constructor(private ref: ElementRef) {}

    @HostListener('error')
    loadFallbackImage() { 
        const element: HTMLImageElement = <HTMLImageElement> this.ref.nativeElement;
        element.src = this.appImgFallback || AppConstant.PRODUCT_IMAGE_FALLBACK
    } 
}