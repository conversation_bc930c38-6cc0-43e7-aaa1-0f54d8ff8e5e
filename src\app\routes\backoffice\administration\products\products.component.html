<div class="import-saved-card-body">
  <div class="import-saved-cart-box d-flex flex-column">
    <h3>
      The excel file should list the product media details in the following
      format:
    </h3>
    <ul>
      <li>
        <span class="material-icons-outlined">east</span>Documents (Doc type) that can be uploaded through this
        template: Image, PDF, Video, Specification
      </li>
      <li>
        <span class="material-icons-outlined">east</span> Dimension is mandatory for Image and Specification media
        upload
      </li>
      <li>
        <span class="material-icons-outlined">east</span> Document Name is mandatory for Video and PDF upload
      </li>
      <li>
        <span class="material-icons-outlined">east</span> URL is mandatory for all document types
      </li>
      <li>
        <span class="material-icons-outlined">east</span> Template:
        <a href="https://snjya.s3.us-east-2.amazonaws.com/Files/Product+Media+Upload+Template.xlsx">Download</a>
      </li>
    </ul>
  </div>
  <div class="import-saved-cart-box d-flex flex-column">
    <h3>Select a file to upload. The file must be in XLS/XLSX format.</h3>
    <input type="file" class="file-input" accept=".xls,.xlsx" (change)="onFileSelected($event)" #fileInput />
    <div class="d-flex gap-3 align-items-center justify-content-center flex-grow-1">
      <button type="button" (click)="fileInput.click()" class="upload-file-link">
        <span class="material-icons-outlined"> cloud_upload </span>
        {{ fileName || "Choose File" }}
        <span class="material-icons-outlined red" *ngIf="fileName" (click)="removeFile($event)">
          cancel
        </span>
      </button>
      <button class="btn btn-primary" [disabled]="!fileName" (click)="upload()">
        {{ saving ? "Uploading..." : "Upload" }}
      </button>
    </div>
  </div>
</div>

<table class="table table-striped table-hover mt-3" *ngIf="products?.length">
  <thead>
    <tr>
      <th>File Name</th>
      <th>Progress</th>
      <th>Total</th>
      <th>Success</th>
      <th>Failed</th>
      <th>Status</th>
      <th>Summary</th>
      <th>Remove</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let product of products">
      <td>{{ product.file_original_name }}</td>
      <td>{{ product.progress }}%</td>
      <td>{{ product.total }}</td>
      <td>{{ product.success }}</td>
      <td>{{ product.failed }}</td>
      <td>{{ product.status }}</td>
      <td>
        <a class="text-danger" href="javascript:void(0);" *ngIf="product.status === 'DONE'">
          <i class="material-icons-outlined" (click)="exportFileSummary(product)" *ngIf="!product.loading">
            cloud_download
          </i>
          <div class="spinner-border spinner-border-sm" role="status" *ngIf="product.loading">
            <span class="visually-hidden">Loading...</span>
          </div>
        </a>
      </td>
      <td>
        <a class="text-danger" href="javascript:void(0);">
          <i class="material-icons-outlined" (click)="removeMediaUploadStatus(product.id)">
            delete
          </i>
        </a>
      </td>
    </tr>
  </tbody>
</table>