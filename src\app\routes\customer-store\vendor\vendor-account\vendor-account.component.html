<div class="all-main-title-sec product-details-main-title">
  <h1>Account ID: {{ partner_function?.customer_id }}</h1>
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>
        <a [routerLink]="['/store/vendor']">
          <span class="material-icons-outlined">warehouse</span> Vendor
        </a>
      </li>
      <li>Account</li>
    </ul>
  </div>
</div>

<div class="q-account-buttons-container">
  <button type="button" class="q-account-btn">
    <span class="material-icons-outlined">save</span>
    Save
  </button>
  <button type="button" class="q-account-btn">
    <span class="material-icons-outlined">edit</span>
    Edit
  </button>
  <button type="button" class="q-account-btn">
    <span class="material-icons-outlined">cancel</span>
    Cancel
  </button>
  <button type="button" class="q-account-btn">
    <span class="material-icons-outlined">add</span>
    New PO
  </button>
</div>

<div class="account-id-sec">
  <div class="account-id-body all-details-page">
    <div class="account-id-info">
      <div class="account-details">
        <h3>Vendor Info</h3>
        <ul>
          <li>
            <span class="material-icons-outlined">warehouse</span>
            Vendor<span>Volcom</span>
          </li>
          <li>
            <span class="material-icons-outlined">person</span>
            Contact<span>James Thompson</span>
          </li>
          <li>
            <span class="material-icons-outlined">phone</span>
            Phone<span>(*************</span>
          </li>
          <li>
            <span class="material-icons-outlined">phone</span>
            Alt Phone<span>(*************</span>
          </li>
          <li>
            <span class="material-icons-outlined">fax</span>
            Fax<span></span>
          </li>
          <li>
            <span class="material-icons-outlined">email</span>
            Email<span><EMAIL></span>
          </li>
          <li>
            <span class="material-icons-outlined">language</span>
            Website<span>merchant.volcom.com</span>
          </li>
          <li>
            <span class="material-icons-outlined">home</span>
            Address<span>489 N 51st</span>
          </li>
          <li>
            <span class="material-icons-outlined">location_city</span>
            City<span>North Salt Lake</span>
          </li>
          <li>
            <span class="material-icons-outlined">domain</span>
            State<span>Utah</span>
          </li>
          <li>
            <span class="material-icons-outlined">location_on</span>
            Zip<span>84512</span>
          </li>
          <li>
            <span class="material-icons-outlined">map</span>
            Country<span>USA</span>
          </li>
        </ul>
      </div>
      <div class="account-details">
        <h3>Account details</h3>
        <ul>
          <li>
            <span class="material-icons-outlined">pin</span>
            Account # <span>{{ partner_function?.customer_id }}</span>
          </li>
          <li>
            <span class="material-icons-outlined">pest_control</span>
            Terms<span>Net 30</span>
          </li>
          <li>
            <span class="material-icons-outlined">sell</span>
            Discount %<span>Platinum Club: 5%</span>
          </li>
          <li>
            <span class="material-icons-outlined">sell</span>
            Discount Paid By<span>Reduced Invoicing</span>
          </li>
          <li>
            <span class="material-icons-outlined">local_shipping</span>
            Ship Via<span>UPS</span>
          </li>
          <li>
            <span class="material-icons-outlined">schedule</span>
            Store Hours<span>8:00 AM - 6:00 PM M-F</span>
          </li>
          <li>
            <span class="material-icons-outlined">schedule</span>
            Time Zone<span>MST</span>
          </li>
          <li>
            <span class="material-icons-outlined">description</span>
            Notes<span>Free shipping on orders over $500</span>
          </li>
        </ul>
      </div>
      <div class="account-details">
        <h3>History</h3>
        <div class="row">
          <div class="col">
            <div class="my-2"><strong>Purchase Orders</strong></div>
            <div class="account d-flex flex-column align-items-end">
              <app-grid
                [columns]="poColumnDefs"
                [data]="purchaseOrders"
                *ngIf="purchaseOrders.length"
              ></app-grid>
              <div class="w-100" *ngIf="!purchaseOrders.length">
                {{ "No records found." }}
              </div>
            </div>
          </div>
          <div class="col">
            <div class="my-2"><strong>Inbound Orders</strong></div>
            <div class="account d-flex flex-column align-items-end">
              <app-grid
                [columns]="roColumnDefs"
                [data]="receivingOrders"
                *ngIf="receivingOrders.length"
              ></app-grid>
              <div class="w-100" *ngIf="!receivingOrders.length">
                {{ "No records found." }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
