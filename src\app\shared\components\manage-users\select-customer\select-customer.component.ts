import {
  Component,
  EventEmitter,
  HostListener,
  Output,
  ViewChild,
} from "@angular/core";
import {
  ColDef,
  ColumnMovedEvent,
  GridOptions,
  IDatasource,
  IGetRowsParams,
  RowHeightParams,
} from "ag-grid-community";
import { TranslateService } from "@ngx-translate/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Subject, takeUntil } from "rxjs";

import { AppToastService } from "../../../services/toast.service";
import { ManageUserService } from "../manage-user-service/manage-user.service";
import { GridColumn } from "../../grid-column/grid-column.model";
import { InputRadioBtnComponent } from "../renderer/input-radio-btn/input-radio-btn.component";
import { AuthService } from "src/app/core/authentication/auth.service";
import { CartService } from "src/app/routes/customer-store/services/cart.service";

@Component({
  selector: "app-select-customer",
  templateUrl: "./select-customer.component.html",
  styleUrls: ["./select-customer.component.scss"],
})
export class SelectCustomerComponent {
  private ngUnsubscribe = new Subject<void>();
  public saving = false;
  public selectedCustomerId: any = null;
  public columnDefs: ColDef[] = [
    {
      headerName: "",
      cellRenderer: InputRadioBtnComponent,
      cellRendererParams: {
        onChange: this.selectUserCustomer.bind(this),
        label: "",
      },
      maxWidth: 46,
    },
    {
      field: "customer_id",
      headerName: this.getTranslate("backoffice.contact.customerID"),
      sortable: true,
      maxWidth: 150,
    },
    // {
    //   field: "bp_uuid",
    //   headerName: this.getTranslate("backoffice.contact.accountGUID"),
    //   sortable: true,
    // },
    {
      field: "customer_name",
      headerName: this.getTranslate("backoffice.contact.customerName"),
      sortable: true,
    },
    {
      field: "cpf_address",
      headerName: this.getTranslate("backoffice.contact.address"),
      sortable: true,
    },
    {
      field: "phone",
      headerName: this.getTranslate("backoffice.contact.phone"),
      sortable: true,
      maxWidth: 180,
    },
  ];

  public defaultColDef: ColDef = {
    flex: 1,
    minWidth: 250,
  };
  public rowClass = 'ag-row';

  public rowData!: any[];
  private gridApi!: any;
  private gridColumnApi!: any;
  public isLoading = true;
  public query = {
    perPage: 10,
    pageNo: 1,
  };
  public searchText: string = "";
  public searchBy: string = "customer_name";
  public pageSize = 10;
  public gridOptions: GridOptions = {
    rowHeight: 62,
    suppressMenuHide: true,
    pagination: true,
    cacheBlockSize: 10,
    paginationPageSize: 10,
    rowModelType: "infinite",
    onColumnMoved: (event: ColumnMovedEvent) => this._onColumnMoved(event),
  };

  public dataSource: IDatasource = {
    getRows: (params: IGetRowsParams) => {
      this.query.pageNo = params.endRow / this.query.perPage;
      this.gridApi.showLoadingOverlay();
      this.manageUserService
        .getLoggedInUserCustomers({
          perPage: this.pageSize,
          search: this.searchText,
          searchBy: this.searchBy,
          pageNo: params.endRow / this.pageSize,
        })
        .subscribe({
          next: ({ data, total }: any) => {
            params.successCallback(data, total);
            this.gridApi.hideOverlay();
          },
          error: (err: any) => {
            this.gridApi.hideOverlay();
            this._snackBar.open(
              err.error?.message ||
              "Error while processing get user's customer request.",
              { type: "Error" }
            );
          },
        });
    },
  };

  constructor(
    public activeModal: NgbActiveModal,
    private translate: TranslateService,
    private _snackBar: AppToastService,
    private manageUserService: ManageUserService,
    private auth: AuthService,
    private cart: CartService
  ) { }

  ngOnInit(): void { }

  selectUserCustomer(e: any) {
    // Deselect other rows
    this.gridApi.forEachNode((node: any) => {
      if (node.data.customer_id !== e?.selectedCustomerId) {
        node.data.is_selected = false;
      } else {
        node.data.is_selected = true;
      }
    });
    this.selectedCustomerId = e.selectedCustomerId;
  }

  @HostListener("window:resize", ["$event"])
  onResize(event: any) {
    this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params: { api: any; columnApi: any }) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
    this.gridApi.setDatasource(this.dataSource);
  }

  onSearchChange(text: string) {
    this.searchText = text;
    this.updateTable();
  }

  onSearchByChange(option: string) {
    this.searchBy = option;
    this.updateTable();
  }

  updateTable() {
    this.gridApi.paginationGoToPage(0);
    setTimeout(() => {
      this.gridApi.purgeInfiniteCache();
    }, 100);
  }

  getTranslate(key: string) {
    return this.translate.instant(key);
  }

  @ViewChild("columnMenu") columnMenu: any;
  @Output() columnChange = new EventEmitter<GridColumn[]>();
  _onColumnChange(columns: any[]) {
    this.columnChange.emit(columns);

    const displayedColumns = Object.assign(
      [],
      this.getDisplayedColumnFields(columns)
    );
    const allColumns = Object.assign(
      [],
      this.getAllDisplayedColumnFields(columns)
    );

    this.gridColumnApi.setColumnsVisible(displayedColumns, false);
    this.gridColumnApi.setColumnsVisible(allColumns, true);
    this.gridApi.sizeColumnsToFit();
  }

  getDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => !item.show)
      .map((item: GridColumn) => item.field)
      .filter((item) => item);
    return fields;
  }

  getAllDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => item.show)
      .map((item: GridColumn) => item.field)
      .filter((item) => item);
    return fields;
  }

  _columnPositionChange(position: number[]) {
    this.gridColumnApi.moveColumnByIndex(position[0], position[1]);
    this.gridApi.sizeColumnsToFit();
  }

  _onColumnMoved(params: any) {
    const columnDragState = params.columnApi.getColumnState();
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map((e: { colDef: any }) => {
        return e.colDef;
      });
    const newColDef: ColDef[] = colIds.map((item: any, i: string | number) =>
      Object.assign({}, item, columnDragState[i])
    );
    this.columnDefs = newColDef;
  }

  refreshRowData(data: any) {
    const rowNode = this.gridApi.getRowNode(data.customer_id)!;
    rowNode && rowNode.setData(data);
  }

  getRowId(params: any) {
    return params.data.customer_id;
  }

  onReset() {
    this.selectedCustomerId = null;
    this.gridApi.refreshCells({ force: true });
  }

  onSubmit() {
    if (!this.selectedCustomerId) {
      this._snackBar.open("Please select customer.", {
        type: "Warning",
      });
      return;
    }
    this.saving = true;
    this.manageUserService
      .updateSelectedCustomer(this.selectedCustomerId, null)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: async (res: any) => {
          this.saving = false;
          const user = res?.data || null;
          this.auth.updateAuth(user);
          await this.cart.getCartByID();
          this.activeModal.close();
          location.reload();
        },
        error: () => {
          this.saving = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
