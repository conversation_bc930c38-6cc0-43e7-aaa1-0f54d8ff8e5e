<ng-container *ngIf="!loading">
  <div class="all-main-title-sec cart-details-main-title">
    <div class="all-bedcrumbs">
      <ul>
        <li>
          <a [routerLink]="['/store/dashboard']">
            <span class="material-icons-outlined">home</span> Home
          </a>
        </li>
        <li>
          <a [routerLink]="['/store/order-history']">
            <span class="material-icons-outlined">list_alt</span> Orders
          </a>
        </li>
        <li>
          <a [routerLink]="['/store/order', orderID, orderType]">
            <span class="material-icons-outlined">info</span> Order Detail
          </a>
        </li>
        <li>Product Return</li>
      </ul>
    </div>
    <h1>Product Return</h1>
  </div>

  <form class="order-details" [formGroup]="form">
    <ng-container *ngIf="step === 1">
      <h3>Select the items you would like to return</h3>
      <ng-container formGroupName="to_Item">
        <div class="item-box all-common-item" formArrayName="results" *ngFor="let r of results.controls; let i = index">
          <ng-container formGroupName="{{ i }}">
            <input type="checkbox" class="form-control checkbox-input" formControlName="Selected" />
            <ng-container *ngIf="
                r.get('Product')?.value?.MATERIAL
                  | getProductImage
                  | async as url
              ">
              <div class="item-box-img" [ngStyle]="{
                  'background-image': 'url(' + url + ')'
                }"></div>
            </ng-container>
            <div class="item-box-content">
              <h4>{{ r.get("Product")?.value?.SHORT_TEXT }}</h4>
              <div class="item-box-list">
                <small>
                  <span>Material No:</span>
                  {{ r.get("Product")?.value?.MATERIAL }}
                </small>
                <small>
                  <span>Item Price:</span>
                  {{ r.get("Product")?.value?.formatted_base_price_each }}
                </small>
                <small>
                  <span>Order Quantity: </span>
                  {{ r.get("Product")?.value?.REQ_QTY }}
                </small>
              </div>
              <div class="item-box-bottom-content">
                <div class="return-reason w-100">
                  <div class="return-r-dropdown">
                    <label>
                      Return Reason <span class="text-danger">*</span>
                    </label>
                    <select class="form-control select-arrow-down" formControlName="ReturnReason">
                      <option value="">Select Return Reason</option>
                      <option *ngFor="let reason of returnReason" [value]="reason.code">
                        {{ reason.description }}
                      </option>
                    </select>
                    <div class="invalid-feedback d-block" *ngIf="submitted && r.get('ReturnReason').errors">
                      <span *ngIf="r.get('ReturnReason')?.errors['required']">
                        Return reason is required.
                      </span>
                    </div>
                  </div>
                  <div class="d-flex flex-column return-refund-type">
                    <label>
                      Return Type <span class="text-danger">*</span>
                    </label>
                    <div class="initiate-return-list">
                      <label>
                        <input [id]="'return-refund-'+ i" type="radio" class="form-control" value="0"
                          formControlName="ReturnsRefundType" />
                        <span>Return & Refund</span>
                      </label>
                      <label>
                        <input [id]="'replace-'+ i" type="radio" class="form-control" value="1"
                          formControlName="ReturnsRefundType" />
                        <span>Replace Item </span>
                      </label>
                    </div>
                    <div class="invalid-feedback d-block" *ngIf="submitted && r.get('ReturnsRefundType').errors">
                      <span *ngIf="r.get('ReturnsRefundType')?.errors.required">
                        Returns refund type is required.
                      </span>
                    </div>
                  </div>
                  <div class="w-25">
                    <div class="d-flex justify-content-end">
                      <div class="quantity">
                        <span class="px-2">
                          Return Quantity:
                          <span class="text-danger">*</span>
                        </span>
                        <span class="req-qty">
                          <input type="text" placeholder="Type..." formControlName="RequestedQuantity" /></span>
                      </div>
                    </div>
                    <div class="invalid-feedback d-flex justify-content-end"
                      *ngIf="submitted && r.get('RequestedQuantity').errors">
                      <span *ngIf="r.get('RequestedQuantity')?.errors['required']">
                        Requested quantity reason is required.
                      </span>
                      <span *ngIf="
                          r.get('RequestedQuantity')?.errors['min'] ||
                          r.get('RequestedQuantity')?.errors['max'] ||
                          r.get('RequestedQuantity')?.errors['pattern']
                        ">
                        Enter a valid return quantity.
                      </span>
                    </div>
                  </div>
                </div>
                <div class="item-price">
                  {{ r.get("Product")?.value?.formatted_base_price }}
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </ng-container>
      <div class="return-r-btn d-flex justify-content-between align-items-center">
        <div class="text-danger">
          <span *ngIf="submitted && results?.errors?.hasError">
            Please select at least one product.
          </span>
          <span *ngIf="submitted && !results?.errors?.hasError && form.invalid">
            There are errors in the form submission. Please review the form.
          </span>
        </div>
        <button class="btn" (click)="next()">Next</button>
      </div>
    </ng-container>
    <ng-container *ngIf="step === 2">
      <h3>Review the selected return products:</h3>
      <ng-container formGroupName="to_Item">
        <ng-container formArrayName="results" *ngFor="let r of results.controls; let i = index">
          <div class="item-box all-common-item px-4" formGroupName="{{ i }}" *ngIf="r.get('Selected')?.value">
            <ng-container *ngIf="
                r.get('Product')?.value?.MATERIAL
                  | getProductImage
                  | async as url
              ">
              <div class="item-box-img" [ngStyle]="{
                  'background-image': 'url(' + url + ')'
                }"></div>
            </ng-container>
            <div class="item-box-content">
              <h4>{{ r.get("Product")?.value?.SHORT_TEXT }}</h4>
              <div class="item-box-list">
                <small>
                  <span>Material No:</span>
                  {{ r.get("Product")?.value?.MATERIAL }}
                </small>
                <small>
                  <span>Item Price:</span>
                  {{ r.get("Product")?.value?.formatted_base_price_each }}
                </small>
                <small>
                  <span>Order Quantity: </span>
                  {{ r.get("Product")?.value?.REQ_QTY }}
                </small>
              </div>
              <div class="item-box-bottom-content">
                <div class="return-reason w-100">
                  <div class="return-r-dropdown">
                    <label>Return Reason</label>
                    <select class="form-control select-arrow-down" disabled>
                      <option value="">Select Return Reason</option>
                      <option *ngFor="let reason of returnReason" [value]="reason.code" [selected]="
                          r.get('ReturnReason')?.value === reason.code
                        ">
                        {{ reason.description }}
                      </option>
                    </select>
                  </div>
                  <div class="d-flex flex-column return-refund-type">
                    <label>
                      Return Type <span class="text-danger">*</span>
                    </label>
                    <div class="initiate-return-list">
                      <label>
                        <input [id]="'return-refund-'+ i" type="radio" class="form-control" value="0"
                          [checked]="r.get('ReturnsRefundType')?.value === '0'" disabled />
                        <span>Return & Refund</span>
                      </label>
                      <label>
                        <input [id]="'replace-'+ i" type="radio" class="form-control" value="1"
                          [checked]="r.get('ReturnsRefundType')?.value === '1'" disabled />
                        <span>Replace Item </span>
                      </label>
                    </div>
                  </div>
                  <div class="w-25">
                    <div class="d-flex justify-content-end">
                      <div class="quantity">
                        <span class="px-2"> Return Quantity: </span>
                        <span class="req-qty">
                          {{ r.get("RequestedQuantity").value }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="item-price">
                  {{ r.get("Product")?.value?.formatted_base_price }}
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </ng-container>
      <div class="return-r-btn d-flex justify-content-end align-items-center">
        <div class="d-flex gap-3">
          <button class="btn" (click)="previous()">Previous</button>
          <button class="btn" (click)="!isInitiateReturn && initiateReturn()" [disabled]="isInitiateReturn">
            Initiate Return
          </button>
        </div>
      </div>
    </ng-container>
  </form>
</ng-container>
<app-loader *ngIf="loading"></app-loader>