import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class SalesOrderService {
  constructor(private http: HttpClient) {}

  salesOrderSimulation(sosReq: any, cartID?: any): any {
    let params = new HttpParams();
    if (cartID) {
      params = params.append("cartID", cartID);
    }
    return this.http.post<any>(ApiConstant.SALES_ORDER_SIMULATION, sosReq, {
      params,
    });
  }

  salesOrderCreation(socReq: any, cartID: any): any {
    const params = new HttpParams().appendAll({ cartID });
    return this.http.post<any>(ApiConstant.SALES_ORDER_CREATION, socReq, {
      params,
    });
  }

  addShippingAddress(body: any): any {
    return this.http.post<any>(ApiConstant.ADD_SHIPPING_ADDRESS, body);
  }
}
