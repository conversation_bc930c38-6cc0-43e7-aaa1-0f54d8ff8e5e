import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { SharedModule } from "src/app/shared/shared.module";
import { AccountComponent } from "./account.component";

@NgModule({
  declarations: [AccountComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: AccountComponent }]),
  ],
})
export class AccountModule {}
