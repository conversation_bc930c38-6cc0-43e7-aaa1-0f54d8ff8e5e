<div class="row">
    <div class="col-12">
        <ul ngbNav #nav="ngbNav" [(activeId)]="activeId" class="nav-tabs" (navChange)="navChange($event)">
            <ng-container *ngFor="let placement of placements;let i = index;">
                <li [ngbNavItem]="i">
                    <button ngbNavLink>{{placement?.name}}</button>
                    <ng-template ngbNavContent>
                        <ng-container *ngIf="activeId === i">
                            <div class="accordion accordion-flush" *ngIf="custGroup.length">
                                <div class="accordion-item" *ngFor="let cg of custGroup;let i = index;">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed"
                                            (click)="toggleAccordian(custGroup,$event, i)" type="button">
                                            {{ cg?.name }}
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="panel">
                                        <div class="accordion-body">
                                            <app-banner [isActive]="cg.isActive" [custGroup]="cg.customer_group"
                                                [custAccountGroup]="cg.customer_account_group"
                                                [placement]="placement.value"></app-banner>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </ng-template>
                </li>
            </ng-container>
        </ul>
        <div [ngbNavOutlet]="nav" class="mt-2"></div>
    </div>
</div>