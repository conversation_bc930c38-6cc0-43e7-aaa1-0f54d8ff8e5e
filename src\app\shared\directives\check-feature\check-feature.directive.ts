import { Directive, Input, TemplateRef, ViewContainerRef } from "@angular/core";
import { AppFeatureService } from "src/app/routes/customer-store/services/app-feature/app-feature.service";

@Directive({
  selector: "[checkAppFeature]",
})
export class CheckFeatureDirective {
  private hasView = false;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private appFeatureService: AppFeatureService
  ) {}

  @Input() set checkAppFeature(feature: string) {
    let features = this.appFeatureService.enabledFetures;
    if (feature && !this.hasView) {
      if (features.includes(feature)) {
        this.viewContainer.createEmbeddedView(this.templateRef);
        this.hasView = true;
      } else {
        this.viewContainer.clear();
        this.hasView = false;
      }
    } else if (!feature && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    }
  }
}
