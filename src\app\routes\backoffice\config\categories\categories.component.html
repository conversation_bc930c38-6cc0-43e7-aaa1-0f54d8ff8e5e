<div class="bck-table-details mt-2">
    <div class="d-flex justify-content-between">
        <span (click)="toogleDetails()" class="expand-title d-flex align-items-center">
            <i class="material-icons-outlined">{{cardOpen ? 'expand_more': 'chevron_right'}}</i>&nbsp;{{
            'form.details' | translate }}
        </span>
        <ng-container *ngIf="cardOpen">
            <button class="btn btn-primary" (click)="submitForm()" *ngIf="aciveTab != 'History' || submitted">
                {{ 'form.action.submit' | translate }}
            </button>
        </ng-container>
    </div>
    <div *ngIf="cardOpen">
        <asar-tabs (activeTab)="setActiveform($event)">
            <asar-tab [tabTitle]="'General'">
                <form [formGroup]="generalForm">
                    <div class="row">
                        <div class="col-md-6 form-group">
                            <label>Name</label>
                            <input class="form-control" type="text" formControlName="name" />
                            <span class="text-error" *ngIf="f.name?.touched && f.name.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                        <div class="col-md-6 form-group">
                            <label>Parent Category</label>
                            <select class="form-select" formControlName="parent_category_id">
                                <option value=""></option>
                                <ng-container *ngFor="let option of categories">
                                    <option [value]="option.id" *ngIf="model.id != option.id">
                                        {{option.name}}
                                    </option>
                                </ng-container>
                            </select>
                            <span class="text-error"
                                *ngIf="f.parent_category_id?.touched && f.parent_category_id.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                    </div>
                    <div class="row" *ngIf="f?.parentCategoryHierarchy?.value.length">
                        <div class="col">
                            <strong>Category hierarchy:</strong>
                            <nav aria-label="breadcrumb mt-2">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item" *ngFor="let pch of f?.parentCategoryHierarchy?.value">
                                        <span>{{pch}}</span>
                                    </li>
                                </ol>
                              </nav>
                        </div>
                    </div>
                </form>
            </asar-tab>
            <asar-tab [tabTitle]="'Catalogs'">
                <form [formGroup]="catalogsForm">
                    <div class="row">
                        <div class="col-12 form-group">
                            <label>Catalogs</label>
                            <div class="d-flex align-items-start">
                                <select class="form-select h-100" formControlName="selectedCatalogs" multiple="true">
                                    <ng-container *ngFor="let option of catalogs">
                                        <option [value]="option.id">
                                            {{option.name}}
                                        </option>
                                    </ng-container>
                                </select>
                                <button class="btn btn-primary mx-2" (click)="addCatalog()">
                                    <i class="material-icons-outlined">add</i>
                                </button>
                            </div>
                            <label>Selected Catalogs</label>
                            <table class="table table-striped table-hover" *ngIf="f.catalogs?.value">
                                <tbody>
                                    <tr *ngFor="let catalog of f.catalogs?.value">
                                        <td>{{getCatalogName(catalog)}}</td>
                                        <td>
                                            <button class="btn btn-primary float-end" (click)="removeCatalog(catalog)">
                                                <i class="material-icons-outlined">delete</i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <span class="text-error" *ngIf="f.catalogs?.touched && f.catalogs.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                    </div>
                </form>
            </asar-tab>
            <asar-tab [tabTitle]="'History'">
                <div class="row">
                    <div class="col-md-6 form-group">
                        <label> Created On</label>
                        <p *ngIf="model.created_at">{{moment(model.created_at, 'YYYY-MM-DD
                            HH:mm:SS').format('MM/DD/YYYY HH:mm:SS')}}</p>
                    </div>
                    <div class="col-md-6 form-group">
                        <label> Created By</label>
                        <p>{{model.created_by}}</p>
                    </div>
                    <div class="col-md-6 form-group">
                        <label> Updated On</label>
                        <p *ngIf="model.updated_at">{{moment(model.updated_at, 'YYYY-MM-DD
                            HH:mm:SS').format('MM/DD/YYYY HH:mm:SS')}}</p>
                    </div>
                    <div class="col-md-6 form-group">
                        <label> Updated By</label>
                        <p>{{model.updated_by}}</p>
                    </div>
                </div>
            </asar-tab>
        </asar-tabs>
    </div>
</div>