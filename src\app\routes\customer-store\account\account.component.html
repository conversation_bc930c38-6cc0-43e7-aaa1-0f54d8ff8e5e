<div class="all-main-title-sec product-details-main-title">
    <h1>Account ID: {{partner_function?.customer_id}}</h1>
    <div class="all-bedcrumbs">
        <ul>
            <li>
                <a [routerLink]="['/store/dashboard']">
                    <span class="material-icons-outlined">home</span> Home
                </a>
            </li>
            <li>
                <a [routerLink]="['/store/customer-services']">
                    <span class="material-icons-outlined">support_agent</span> Customer Service
                </a>
            </li>
            <li>Account</li>
        </ul>
    </div>
</div>

<div class="account-id-sec">
    <div class="account-id-body all-details-page">
        <div class="account-id-info">
            <div class="account-details">
                <h3>Account Info</h3>
                <ul>
                    <li>
                        <span class="material-icons-outlined">pin</span>
                        Account # <span>{{partner_function?.customer_id}}</span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">person_outline</span>
                        Account Name<span>{{partner_function?.name}}</span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">home</span>
                        Address<span>{{partner_function?.address}}</span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">factory</span>
                        Industry<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">confirmation_number</span>
                        Employee Count<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">check</span>
                        Status<span>Active</span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">person</span>
                        HCM Account Owner<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">support</span>
                        Account Support<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">person</span>
                        Client Engagement Manager<span></span>
                    </li>
                </ul>
            </div>
            <div class="account-details">
                <h3>Account Team</h3>
                <ul>
                    <li>
                        <span class="material-icons-outlined">person</span>
                        HCM Account Owner<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">support</span>
                        Account Support<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">person</span>
                        Client Engagement Manager<span></span>
                    </li>
                </ul>
            </div>
            <div class="account-details">
                <h3>Instance Details</h3>
                <ul>
                    <li>
                        <span class="material-icons-outlined">source</span>
                        Development Company ID<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">store</span>
                        Test Company ID<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">apartment</span>
                        Production Company ID<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">api</span>
                        Development Server<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">bug_report</span>
                        Test Server<span></span>
                    </li>
                    <li>
                        <span class="material-icons-outlined">dns</span>
                        Production Server<span></span>
                    </li>
                </ul>
            </div>
            <div class="account-details">
                <h3>Contacts</h3>
                <div class="account d-flex flex-column align-items-end">
                    <app-grid [columns]="columnDefs" [data]="contacts"
                        *ngIf="!loading && contacts.length"></app-grid>
                    <div class="w-100" *ngIf="loading || !contacts.length">{{ loading ? 'Loading...' : 'No records
                        found.' }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>