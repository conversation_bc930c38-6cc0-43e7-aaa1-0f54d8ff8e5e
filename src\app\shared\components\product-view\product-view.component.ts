import {
  Component,
  Input,
  OnInit,
} from "@angular/core";
import { Observable, catchError, delay, map, of } from "rxjs";
import { UntypedFormGroup, UntypedFormControl } from "@angular/forms";
import { CartService } from "src/app/routes/customer-store/services/cart.service";
import { ProductService } from "src/app/routes/backoffice/product/product.service";
import { AuthService } from "src/app/core/authentication/auth.service";
import { StoreProductService } from "src/app/routes/customer-store/services/store.product.service";

@Component({
  selector: "app-product-view",
  templateUrl: "./product-view.component.html",
  styleUrls: ["./product-view.component.scss"],
})
export class ProductViewComponent implements OnInit {
  public isProductIntoView: boolean = false;

  @Input() detail: any;
  public maxQuantity: number = 999;
  public imgUrl$!: Observable<any>;
  public addToCartForm = new UntypedFormGroup({
    quantity: new UntypedFormControl(1, { updateOn: "blur" }),
  });
  public salesPrice$!: Observable<any>;
  public salesPrice: any = 0;

  constructor(
    private authService: AuthService,
    private cartService: CartService,
    private productService: ProductService,
    private storeProductService: StoreProductService
  ) { }

  ngOnInit(): void {
    this.getProductPrice();
    this.getImage();
  }

  addToCart($event: any, product: any) {
    $event.stopPropagation();
    let addToCart = {
      Material: product.product_id,
      RequestedQuantity: this.addToCartForm?.value?.quantity || 1,
    };
    this.cartService.setMaterial(product.product_id);
    this.cartService.addToCart(addToCart);
  }

  getImage() {
    this.imgUrl$ = this.productService
      .getMedia(this.detail.product_id)
      .pipe(catchError((err) => of(err.error)))
      .pipe(
        map((res) => {
          const coverImage = res?.data?.find(
            (d: { is_cover_image: string }) => d.is_cover_image
          );
          if (coverImage) {
            return coverImage.url;
          } else {
            const imageData = res?.data?.find(
              (d: { dimension: string }) => d.dimension === "1200X1200"
            );
            if (imageData) {
              return imageData.url;
            } else {
              return "/assets/images/demo-product.png";
            }
          }
        })
      );
  }

  getProductPrice() {
    const userDetail = this.authService.userDetail.partner_function;
    const payload: any = {
      SalesOrg: userDetail.sales_organization,
      Dist_Channel: userDetail.distribution_channel,
      Division: userDetail.division,
      SoldTo: userDetail.bp_customer_number,
      Products: { Material: this.detail.product_id },
    };
    this.salesPrice$ = this.storeProductService
      .getSalesPrice(payload)
      .pipe(catchError((err) => of(err.error)))
      .pipe(
        delay(0),
        map((res) => {
          if (res?.status === "success") {
            this.salesPrice = parseFloat(res?.data?.ConditionRateValue) || 0;
            return this.salesPrice.toString();
          }
          this.salesPrice = 0;
          return this.salesPrice.toString();
        })
      );
  }

  trustUrl(url: string = '') {
    return (url || '').replace(/\(/g, "\\(").replace(/\)/g, '\\)');
  }

}
