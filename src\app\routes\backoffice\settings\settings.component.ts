import { Component, OnInit } from "@angular/core";
import {
  FormGroup,
  Validators,
  FormBuilder,
  AbstractControl,
} from "@angular/forms";
import { SettingsService } from "./settings.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-settings",
  templateUrl: "./settings.component.html",
  styleUrls: ["./settings.component.scss"],
})
export class SettingsComponent implements OnInit {
  form: FormGroup = this.formBuilder.group({
    id: ["", []],
    timezone: ["", [Validators.required]],
    currency: ["", [Validators.required]],
    country: ["", [Validators.required]],
    company_name: ["", []],
    company_logo: ["", []],
  });
  submitted = false;
  saving = false;
  timezones: Array<any> = [];
  currencies: Array<string> = [];
  countries: Array<string> = [];

  constructor(
    private formBuilder: FormBuilder,
    private service: SettingsService,
    private _snackBar: AppToastService
  ) {
    this.timezones = this.service.getTimezones();
    this.currencies = this.service.getCurrencies();
    this.countries = this.service.getCountries();
  }

  ngOnInit(): void {
    this.getSettings();
  }

  getSettings() {
    this.service.getSettings().subscribe({
      next: (data) => {
        this.form.patchValue(data);
      },
      error: (e) => {
        this._snackBar.open("Error while processing seetings request.", { type: 'Error' });
      },
    });
  }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    this.service.saveSettings(this.form.value).subscribe({
      complete: () => {
        this.onReset();
        this.saving = false;
        this._snackBar.open("Settings saved successfully!");
      },
      error: () => {
        this.saving = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  onReset(): void {
    this.submitted = false;
  }
}
