import { Component } from "@angular/core";
import { Router } from "@angular/router";
import { Subject, takeUntil } from "rxjs";
import moment from "moment";

import { AppToastService } from "src/app/shared/services/toast.service";
import { CartService } from "../services/cart.service";
import { SalesOrderService } from "../services/sales-order/sales-order.service";
import { CustomerService } from "../../backoffice/customer/customer.service";

@Component({
  selector: "app-sales-order-scheduler-confirmation",
  templateUrl: "./sales-order-scheduler-confirmation.component.html",
  styleUrls: ["./sales-order-scheduler-confirmation.component.scss"],
})
export class SalesOrderSchedulerConfirmationComponent {
  private ngUnsubscribe = new Subject<void>();
  public moment = moment;
  public salesOrderScheduler: any = null;
  public order: any = null;
  public customer: any = null;
  public loading: any = false;

  constructor(
    public router: Router,
    private _snackBar: AppToastService,
    private cartService: CartService,
    private salesOrderService: SalesOrderService,
    private customerService: CustomerService
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.cartService
      .getCreatedOrderScheduler()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((sos) => {
        if (!sos) {
          this.router.navigate([`store/catalogues`]);
        } else {
          this.salesOrderScheduler = sos;
          this.getSalesOrderSimulation();
        }
      });
  }

  getSalesOrderSimulation() {
    const sosReq = { ...this.salesOrderScheduler.req_data };
    sosReq.to_Partner = [];
    sosReq.to_Pricing = {};
    delete sosReq.User;
    delete sosReq.to_Text;
    delete sosReq.RequestedDeliveryDate;
    this.salesOrderService
      .salesOrderSimulation(sosReq)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.order =
            res?.data?.SALESORDERSIMULATE?.A_SalesOrderSimulationType || [];
          this.getCustomer(this.order?.SoldToParty);
        },
        error: (err: any) => {
          this.loading = false;
          this._snackBar.open("Error while processing get customer request.", {
            type: "Error",
          });
        },
      });
  }

  getCustomer(customerID: string) {
    this.customerService
      .getCustomerByID(customerID)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (value: any) => {
          this.loading = false;
          this.customer = value;
        },
        error: (err: any) => {
          this.loading = false;
          this._snackBar.open("Error while processing get customer request.", {
            type: "Error",
          });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
