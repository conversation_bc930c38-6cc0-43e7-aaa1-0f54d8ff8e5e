import { Component } from "@angular/core";
import { Subject, takeUntil } from "rxjs";
import { FormArray, FormBuilder, FormGroup } from "@angular/forms";

import { AppToastService } from "src/app/shared/services/toast.service";
import { AppFeatureService } from "./app-feature.service";

@Component({
  selector: "app-features",
  templateUrl: "./app-features.component.html",
  styleUrls: ["./app-features.component.scss"],
})
export class AppFeaturesComponent {
  private ngUnsubscribe = new Subject<void>();
  public appFeatures: any[] = [];
  public form: FormGroup;
  public saving: any = false;

  constructor(
    private fb: FormBuilder,
    private appFeatureService: AppFeatureService,
    private _snackBar: AppToastService
  ) {}

  ngOnInit(): void {
    this.createForm();
    this.getAppFeatures();
  }

  createForm() {
    this.form = this.fb.group({
      list: this.fb.array([]),
    });
  }

  get f(): any {
    return this.form.controls;
  }

  get features(): any {
    return this.form.get("list") as FormArray;
  }

  insertFeature(feature: any): FormGroup {
    return this.fb.group({
      id: [feature?.id],
      code: [feature?.code],
      name: [feature?.name],
      is_enabled: [feature?.is_enabled],
      toggle: [true],
      parent_feature_id: [feature?.parent_feature_id],
      list: this.fb.array([]),
    });
  }

  makeFeatureList(formArr: any, features: any) {
    formArr.clear();
    features.forEach((feature: any, index: any) => {
      formArr.push(this.insertFeature(feature));
      if (feature?.child) {
        const childFormArr: any = formArr.at(index).get("list");
        this.makeFeatureList(childFormArr, [...feature?.child]);
      }
    });
  }

  getAppFeatures() {
    this.appFeatureService
      .getAppFeatures()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.appFeatures = res?.data || [];
          this.makeFeatureList(this.features, [...this.appFeatures]);
        },
        error: () => {
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  toggle(feature: any) {
    const toggle: any = feature.get("toggle").value;
    feature.patchValue({ toggle: !toggle });
  }

  onParentCBChange(feature: any) {
    const is_enabled: any = feature.get("is_enabled").value;
    const list = feature.get("list").controls;
    list.forEach((ctrl: any) => {
      ctrl.patchValue({ is_enabled });
    });
  }

  onChildCBChange(parentFeature: any) {
    const list = parentFeature.get("list").controls;
    const is_enabled: any = list.some((ctrl: any) => ctrl.value.is_enabled);
    parentFeature.patchValue({ is_enabled });
  }

  getFeatureIds(data: any) {
    const ids: any = [];
    data.forEach((item: any) => {
      if (item.is_enabled) {
        ids.push(item.id);
      }
      if (item.list.length > 0) {
        const nestedIds = this.getFeatureIds(item.list);
        ids.push(...nestedIds);
      }
    });
    return ids;
  }

  saveFeatures() {
    if (this.form?.status === "INVALID") {
      return;
    }
    const val = this.form?.value;
    const ids = this.getFeatureIds(val.list || []);
    this.saving = true;
    this.appFeatureService
      .turnOnAppFeature(ids)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.saving = false;
          this._snackBar.open("APP Features saved successfully.");
        },
        error: () => {
          this.saving = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
