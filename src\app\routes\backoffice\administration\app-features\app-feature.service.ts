import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Observable, map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class AppFeatureService {
  constructor(private http: HttpClient) {}

  getAppFeatures(): Observable<any[]> {
    return this.http.get<any[]>(`${ApiConstant.APP_FEATURES}`).pipe(
      map((res: any) => {
        const data: any = res?.data || [];
        const features: any[] = [];
        data.forEach((item: any) => {
          const newItem = { ...item };
          const childItems = data.filter(
            (child: any) => child.parent_feature_id === item.id
          );
          if (childItems.length > 0) {
            newItem.child = childItems;
          }
          if (!item.parent_feature_id) {
            features.push(newItem);
          }
        });
        res.data = features;
        return res;
      })
    );
  }

  getAppFeatureById(appFeatureId: number): Observable<any> {
    return this.http.get<any>(`${ApiConstant.APP_FEATURES}/${appFeatureId}`);
  }

  createAppFeature(appFeature: any): Observable<any> {
    return this.http.post<any>(`${ApiConstant.APP_FEATURES}`, appFeature);
  }

  updateAppFeature(
    appFeatureId: number,
    updatedAppFeature: any
  ): Observable<any> {
    return this.http.put<any>(
      `${ApiConstant.APP_FEATURES}/${appFeatureId}`,
      updatedAppFeature
    );
  }

  turnOnAppFeature(ids: any): Observable<any> {
    return this.http.put<any>(`${ApiConstant.APP_FEATURES}/enable`, { ids });
  }

  deleteAppFeature(appFeatureId: number): Observable<any> {
    return this.http.delete<any>(`${ApiConstant.APP_FEATURES}/${appFeatureId}`);
  }
}
