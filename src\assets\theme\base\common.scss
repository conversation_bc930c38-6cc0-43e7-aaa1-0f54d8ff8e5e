*,
html,
body,
ul,
p,
h1,
h2,
h3,
h4,
h5,
h6,
blockquote {
    margin: 0;
    padding: 0;
    font-family: var(--snjy-font-family);
}

html {
    font-size: 16px;
    height: 100%;
}

body {
    background: var(--snjy-color-main-background-secondary);
    height: 100%;
}

a {
    color: var(--snjy-font-color-primary);
    font-weight: var(--snjy-font-weight-normal);
    text-decoration: none;
    outline: none;
}

.text-error {
    color: var(--snjy-color-danger);
    font-size: var(--snjy-font-size-1);
}

h2 {
    color: var(--snjy-color-text-primary) !important;
}

.panel {
    padding: 0 18px;
    background-color: var(--snjy-font-color-primary);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
}

.accordion-button {
    background-color: var(--snjy-accordian-button-color);
}

.accordion-header {
    margin-bottom: 0;
    border-bottom: 2px solid var(--snjy-font-color-primary);
}

.select-arrow-down {
    background-image: url(../../images/arrow_down.svg) !important;
    background-size: 25px !important;
    background-position-x: 96% !important;
    background-position-y: center !important;
    background-repeat: no-repeat !important;
}

.pro-details .cart-quantity,
.item-box-bottom-content .cart-quantity,
.order-counter .cart-quantity {
    max-width: 135px !important;
    justify-content: space-between !important;
    width: 135px !important;
    margin: 0 !important;

    button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-height: 36px;
    }

    span:not(.material-icons-outlined) {
        font-family: var(--snjy-font-family) !important;
        font-weight: var(--snjy-font-weight-medium) !important;
    }
}

.order-counter .counter-container {
    justify-content: center !important;
}

.table tbody tr td:first-child {
    color: var(--snjy-table-first-column-color) !important;
}

.back-office-container {
    .btn {
        display: flex;
        gap: .5rem;
        justify-content: center;
        align-items: center;
        text-transform: uppercase;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-bold);
    }

    .btn-primary {
        background-color: var(--snjy-button-color-primary);
        opacity: .9;

        &:active,
        &:hover {
            opacity: 1;
            background-color: var(--snjy-button-color-primary);
        }
    }

    .btn-light {
        color: var(--snjy-button-color-light);
        border: 1px solid var(--snjy-button-color-light-border);
    }
}

.expand-title {
    font-size: var(--snjy-font-size-1-125);
    text-transform: uppercase;
    font-weight: var(--snjy-font-weight-medium);
    color: var(--snjy-button-color-primary);
    cursor: pointer;
}

.modal-dialog {
    width: 100%;
}

.modal-fullscreen {
    width: 95vw;
    height: 95vh;
    margin: auto;
    margin-top: 2.5vh;
}

.modal-title {
    color: var(--snjy-color-text-primary) !important;
}

@media only screen and (max-width:480px) {

    .pro-details .cart-quantity,
    .item-box-bottom-content .cart-quantity,
    .order-counter .cart-quantity {
        max-width: 110px !important;
        width: 110px !important;
    }

    .ag-paging-panel {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
        margin: 1rem 0;
    }

    .modal-dialog {
        max-width: 90vw;
    }
}

.product-zoom-modal {

    .ngxImageZoomContainer {
        width: fit-content !important;
        height: auto !important;
    }

    .ngxImageZoomThumbnail {
        width: auto !important;
        height: auto !important;
        max-width: 80vw !important;
        max-height: 90vh;
    }

    .modal-body {
        justify-content: center;
        align-items: center;
        display: flex;
    }

    .btn-close {
        top: 10px;
        right: 10px;
    }
}

button {
    text-transform: uppercase;
}