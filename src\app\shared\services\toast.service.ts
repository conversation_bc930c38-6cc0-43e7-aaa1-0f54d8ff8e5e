import { Injectable, TemplateRef } from "@angular/core";

type Type = 'Success' | 'Error' | 'Warning';

interface Options {
    type: Type;
}

@Injectable({ providedIn: 'root' })
export class AppToastService {
    toasts: any[] = [];

    open(textOrTpl: string | TemplateRef<any>, options: Options = { type: 'Success' }) {
        let classname = 'bg-success text-light';
        if (options.type === 'Error') {
            classname = 'bg-danger text-light';
        }
        if (options.type === 'Warning') {
            classname = 'bg-warning text-dark';
        }
        this.toasts.push({ textOrTpl, ...options, classname });
    }

    remove(toast: any) {
        this.toasts = this.toasts.filter((t) => t !== toast);
    }

    clear() {
        this.toasts.splice(0, this.toasts.length);
    }
}