import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ApiConstant } from 'src/app/constants/api.constants';

@Injectable({
  providedIn: 'root'
})
export class OrderDetailsService {

  constructor(private http: HttpClient) { }

  get(orderId: string, orderType: string) {
    return this.http.get(`${ApiConstant.ORDER_DETAILS}/${orderId}/${orderType}`);
  }

  getScheduledOrderDetail(orderId: string) {
    return this.http.get(`${ApiConstant.SCHEDULED_ORDER_DETAILS}/${orderId}`);
  }

  getImages(productId: string) {
    return this.http.get(`${ApiConstant.IMAGES}?productID=${productId}`);
  }
}
