<ngb-carousel [showNavigationIndicators]="false" [showNavigationArrows]="banners.length > 1">
    <ng-container *ngFor="let banner of banners">
        <ng-container *ngIf="banner?.type === 'IMAGE'">
            <ng-template ngbSlide>
                <div class="picsum-img-wrapper">
                    <img class="img-fluid w-100 banner-img" [src]="banner?.url" />
                </div>
                <div class="carousel-caption banner-caption" *ngIf="banner?.caption" [innerHTML]="banner.caption"></div>
            </ng-template>
        </ng-container>
        <ng-container *ngIf="banner?.type === 'VIDEO'">
            <ng-template ngbSlide>
                <div class="dashboard-video-sec">
                    <video width="100%" height="100%" autoplay loop controls>
                        <source [src]="banner?.url" type="video/mp4">
                    </video>
                </div>
                <div class="carousel-caption banner-caption" *ngIf="banner?.caption" [innerHTML]="banner.caption"></div>
            </ng-template>
        </ng-container>
    </ng-container>
    <ng-container *ngIf="!banners.length">
        <ng-template ngbSlide>
            <div class="dashboard-video-sec">
                <video width="100%" height="100%" autoplay loop controls>
                    <source src="/assets/images/city-building.mp4" type="video/mp4">
                </video>
            </div>
            <div class="carousel-caption banner-caption" *ngIf="settings">
                <h3 class="t-shadow">Welcome to</h3>
                <h1 class="t-shadow">{{settings?.company_name || 'SNJYA'}}</h1>
            </div>
        </ng-template>
    </ng-container>
</ngb-carousel>