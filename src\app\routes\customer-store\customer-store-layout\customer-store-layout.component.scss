:host {
  background-color: var(--snjy-color-main-background);
  display: flex;
  height: 100%;
  flex-direction: column;
}

/*-----------------HEADER SEC--------------*/
header {
  margin: 0;
  padding: 10px 0;
  position: sticky !important;
  border-bottom: 1px solid #e7edee;
  box-shadow: 0 4px 8px rgba(42, 52, 59, 0.05);
  background: var(--snjy-color-white);

  .header-body {
    margin: 0 auto;
    padding: 0 15px;
    max-width: 98%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .logo-sec {
      margin: 0;
      padding: 0;
      position: relative;
      max-width: 146px;
      width: 100%;

      a {
        display: block;
      }
    }

    .user-login-sec {
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 0 5%;
      min-width: 300px;

      .selected-cust {
        color: #929292;
        font-weight: var(--snjy-font-weight-bold);
        font-size: 11px;
        min-width: 100px;

        .text-truncate {
          max-width: 100%;
        }
      }

      .h-notification-part {
        margin: 0;
        padding: 5px 10px 0 0;
        position: relative;

        .material-icons-outlined {
          font-variation-settings: "FILL" 1, "wght" 400, "GRAD" 0;
          color: #929292;
          font-size: 30px;
          height: fit-content;
          width: fit-content;
        }

        span.notification-count {
          margin: 0;
          padding: 0px 3px;
          background: #e55c5c;
          position: absolute;
          top: 0px;
          right: -4px;
          line-height: 16px;
          font-size: 11px;
          font-weight: var(--snjy-font-weight-semi-bold);
          color: var(--snjy-color-white);
          min-width: 26px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid;
          border-radius: 5px;
        }
      }

      .user-login {
        margin: 0;
        padding: 0 20px 0 0;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0 10px;
        cursor: pointer;

        .header-toggle-menu {
          top: 5px !important;

          button {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 1rem;
          }
        }

        .dropdown-toggle:after {
          position: absolute;
          content: '\e5cf';
          font-family: 'Material icons Outlined';
          top: 0;
          right: 0;
          bottom: 0;
          margin: auto 0;
          font-size: var(--snjy-font-size-1-125);
          color: var(--snjy-button-color-primary);
          font-weight: var(--snjy-font-weight-medium);
          line-height: 35px;
          border: none;
        }

        .user-img {
          margin: 0;
          padding: 0;
          position: relative;
          width: 34px;
          height: 34px;
          border-radius: 5px;
          background-size: cover;
          background-repeat: no-repeat;
          background-position: center;
        }

        .user-name {

          h4 {
            margin: 0;
            padding: 0;
            position: relative;
            font-size: var(--snjy-font-size-0-875);
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-button-color-primary);
            line-height: 10px;
            white-space: nowrap;
          }

          small {
            margin: 0;
            padding: 0;
            font-size: 11px;
            font-weight: var(--snjy-font-weight-bold);
            color: var(--snjy-color-dark-secondary);
            opacity: 0.5;
          }
        }
      }
    }
  }
}

/*-----------------HEADER SEC--------------*/
.dropdown-toggle {
  outline: 0;
}

.nav-flush .nav-link {
  border-radius: 0;
}

.btn-toggle {
  display: inline-flex;
  align-items: center !important;
  padding: 0.25rem 0.5rem;
  font-weight: var(--snjy-font-weight-medium);
  background-color: #e9e9e9;
  border: 0;
  position: absolute;
  top: 0;
  right: 5px;
  bottom: 0;
  margin: auto;
  height: 30px;
  width: 30px;

  &::before {
    width: 14px;
    line-height: 0;
    content: '';
    transition: transform 0.35s ease;
    transform: rotate(90deg);
    height: 14px;
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='rgba%280,0,0,.5%29' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 14l6-6-6-6'/%3e%3c/svg%3e") center center no-repeat;
    background-size: cover;
  }

}

main {
  display: flex;
  flex-wrap: nowrap;
  height: 100vh;
  height: -webkit-fill-available;
  max-height: 100vh;
  overflow-x: auto;
  overflow-y: hidden;
}

.content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: auto;
}

.hidden {
  display: none;
}

.sidebar {
  width: 61px;
  transition: all 0.3s ease-in-out;
  padding: 2px;
  border-right: 1px solid #e7edee;
  box-shadow: 1px 0px 2px rgba(42, 52, 59, 0.04);
  position: relative;
  z-index: 1021;
  background: #FFF !important;

  .sidebar-collapse-btn {
    margin: 28px 0 28px 7px;
    padding: 0;
    position: relative;
    min-height: 40px !important;
    width: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: #e3e8e9;
    border-radius: 7px;
    color: #0077d7;
    cursor: pointer;
    transition: all 0.3s ease-in-out;

    &:hover {
      background: #0077d7;
      color: var(--snjy-color-white);
    }

    span {
      font-size: var(--snjy-font-size-1-25);
    }
  }
}

ul.nav.nav-pills {
  gap: 8px 0;
  padding: 0 7px;
  max-height: calc(100% - 100px);
  overflow-x: hidden;
  overflow-y: auto;
  flex-wrap: nowrap;

  li {
    position: relative;
  }

  .n-item-btn {
    margin: 0;
    padding: 10px 9px;
    position: relative;
    display: inline-flex;
    align-items: center;
    width: 40px;
    height: 40px;
    background: #f5f5f5;
    border-radius: 10px;
    border: 1px solid rgba(229, 229, 229, 0);
    gap: 0 10px;
    white-space: nowrap;
    transition: all 0.3s ease-in-out;
    cursor: pointer;

    span {
      margin: 0;
      padding: 0;
      color: #687491;
      font-weight: var(--snjy-font-weight-medium);
      font-size: var(--snjy-font-size-0-8);
      line-height: 10px;
    }
  }
}

.font-icon {
  font-size: 20px;
}

.img-icon {
  margin: 0;
  filter: brightness(0) saturate(90%) invert(38%) sepia(20%) saturate(553%) hue-rotate(214deg) brightness(64%) contrast(60%);
  width: 20px;

  &.active {
    filter: none;
  }
}

.sidebar {
  &.expanded {
    width: 280px;

    ul .nav-item .n-item-btn {
      width: 100% !important;
      justify-content: flex-start !important;
      background: var(--snjy-font-color-primary) !important;
      border: 1px solid rgb(243 243 243) !important;
    }
  }

  ul .nav-item .n-item-btn.active {
    background: #0077d7 !important;
    width: 100%;

    span {
      color: var(--snjy-color-white) !important;
    }
  }
}

ul.btn-toggle-nav {
  padding: 15px 27px 5px 27px;

  li {
    margin: 0 0 5px 0;
  }
}

.child-link {
  width: 100%;
}

.btn-toggle-nav a {
  display: inline-flex;
  text-decoration: none;
  align-items: center;
  gap: 0 5px;

  img {
    width: 20px !important;
  }

  span {
    margin: 0;
    padding: 0;
    color: #0077d7;
    font-weight: var(--snjy-font-weight-medium);
    font-size: var(--snjy-font-size-0-8);
    line-height: 10px;
  }
}

.logo {
  display: block;
  height: 85px;
  position: relative;

  img {
    height: 75px;
  }
}

footer.footer-sec {
  margin: auto 0 0 0;
  padding: 15px 0;
  position: relative;
  bottom: 0;
  width: 100%;
  background: var(--snjy-color-white);
  border-top: 1px solid #e7edee;
  box-shadow: 0 -4px 8px rgba(42, 52, 59, 0.05);
  z-index: 999;

  .footer-body {
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    max-width: 1120px;

    ul {
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0 8%;
      list-style: none;

      li a {
        margin: 0;
        padding: 0;
        font-size: var(--snjy-font-size-0-8);
        text-transform: uppercase;
        font-weight: var(--snjy-font-weight-medium);
        color: var(--snjy-button-color-primary);
        line-height: 13px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0 5px;

        &:hover {
          color: var(--snjy-color-text-ternary);
        }

        .material-icons-outlined {
          color: var(--snjy-color-text-ternary);
          font-size: 21px;
          width: fit-content !important;
          height: fit-content !important;
          display: inline-flex !important;
          max-width: 20px;
        }
      }
    }
  }
}

@media only screen and (max-width: 1024px) {
  header .header-body .logo-sec {
    margin: 0 0 0 50px;
  }

  .sidebar {
    position: fixed;
    left: -61px;
    height: 100vh;

    .sidebar-collapse-btn {
      margin: 31px 0 11px 70px;
    }
  }

  .sidebar.expanded {
    width: 280px;
    left: 0;

    .sidebar-collapse-btn {
      margin: 31px 0 11px 5px;
    }
  }
}


@media only screen and (max-width: 768px) {
  .sidebar {
    .sidebar-collapse-btn {
      margin: 21px 0 21px 70px;
    }
  }

  .sidebar.expanded {
    .sidebar-collapse-btn {
      margin: 21px 0 21px 5px;
    }
  }

  header {
    .header-body {
      .logo-sec {
        max-width: 120px;
      }

      .user-login-sec {
        gap: 0 5%;
        min-width: fit-content;

        .h-notification-part {
          .material-icons-outlined {
            font-size: 24px;
          }

          span.notification-count {
            line-height: 14px;
            font-size: 11px;
            min-width: 22px;
            border-radius: 4px;
          }
        }

        .user-name {
          display: none;
        }
      }
    }
  }

  footer.footer-sec .footer-body ul {
    gap: 5px;
    list-style: none;
    flex-direction: column;
  }

  footer.footer-sec .footer-body ul li a {
    position: relative;
    width: 100%;
    height: 20px;
  }

  footer.footer-sec .footer-body ul li a .material-icons-outlined {
    display: none !important;
  }
}

@media only screen and (max-width: 480px) {
  header {
    .header-body {
      .logo-sec {
        max-width: 90px;
        margin: 0 0 0 40px;
      }
    }
  }

  .sidebar {
    .sidebar-collapse-btn {
      margin: 14px 0 7px 65px;
      min-height: 34px !important;
      width: 34px;
      border-radius: 4px;
    }
  }

  .sidebar.expanded {
    .sidebar-collapse-btn {
      margin: 14px 0 7px 5px;
    }
  }
}