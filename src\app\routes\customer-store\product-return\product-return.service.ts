import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class ProductReturnService {
  constructor(private http: HttpClient) {}

  create(data: any) {
    return this.http.post(`${ApiConstant.RETURN_ORDER}/create`, data);
  }

  getAllReturnReason() {
    return this.http.get<any>(ApiConstant.RETURN_REASON);
  }
}
