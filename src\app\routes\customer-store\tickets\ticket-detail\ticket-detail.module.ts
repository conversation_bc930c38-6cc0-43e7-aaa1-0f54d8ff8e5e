import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { TicketDetailComponent } from "./ticket-detail.component";
import { AddMessageComponent } from './add-message/add-message.component';
import { SharedModule } from "src/app/shared/shared.module";

@NgModule({
  declarations: [TicketDetailComponent, AddMessageComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: TicketDetailComponent }]),
  ],
})
export class TicketDetailModule { }
