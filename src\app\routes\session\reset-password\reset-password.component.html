<h4 class="mb-4">Reset Password</h4>
<form [formGroup]="form">
    <div class="form-group mb-4 required">
        <label class="mb-2">Password</label>
        <input type="password" formControlName="password" class="form-control mt-1 mb-2"
            [ngClass]="{ 'is-invalid': submitted && f['password'].errors }" />
        <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
            <div *ngIf="f['password'].errors['required']">
                This field is required</div>
            <div *ngIf="f['password'].errors['minlength']">
                Must be at least 8 characters</div>
            <div *ngIf="f['password'].errors['hasNumber']">
                Must contain at least one number</div>
            <div *ngIf="f['password'].errors['hasCapitalCase']">
                Must contain at least one Letter in Capital Case</div>
            <div *ngIf="f['password'].errors['hasSmallCase']">
                Must contain at least one Letter in Small Case</div>
            <div *ngIf="f['password'].errors['hasSpecialCharacters']">
                Must contain at least one Special Character</div>
        </div>
    </div>
    <div class="form-group mb-4 required">
        <label class="mb-2">Confirm Password</label>
        <input type="password" formControlName="passwordConfirm" class="form-control mt-1 mb-2"
            [ngClass]="{ 'is-invalid': submitted && f['passwordConfirm'].errors }" />
        <div *ngIf="submitted && f['passwordConfirm'].errors" class="invalid-feedback">
            <div *ngIf="f['passwordConfirm'].errors['required']">
                This field is required</div>
            <div *ngIf="f['passwordConfirm'].errors['confirmedValidator']">
                Passwords must match
            </div>
        </div>
    </div>
    <div class="d-flex justify-content-between">
        <button type="submit" class="btn btn-light" [disabled]="saving" (click)="onSubmit()">Reset Password</button>
        <button type="submit" class="btn btn-primary" routerLink="/store">Cancel</button>
    </div>
</form>