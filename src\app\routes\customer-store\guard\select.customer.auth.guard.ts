import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  CanActivate,
  CanActivateChild,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from "@angular/router";
import { NgbModal, NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";

import { AuthService } from "../../../core/authentication/auth.service";
import { SelectCustomerComponent } from "src/app/shared/components/manage-users/select-customer/select-customer.component";
import { RolesType } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class SelectCustomerAuthGuard implements CanActivate, CanActivateChild {
  private isDialogOpen: boolean = false;
  private modalOption: NgbModalOptions = {};
  constructor(
    private dialog: NgbModal,
    private router: Router,
    private auth: AuthService
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    return this.authenticate(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean | UrlTree {
    return this.authenticate(state.url);
  }

  private authenticate(url: string): boolean | UrlTree {
    if (this.auth.isLoggedIn && !this.auth.isCustomerSelected) {
      if (!this.isDialogOpen) {
        this.isDialogOpen = true;
        this.openDialog();
      }
      if (this.auth.role === RolesType.SALES) {
        if (!url.startsWith("/store/sales")) {
          return this.router.parseUrl("/store/sales");
        }
      } else if (this.auth.role === RolesType.VENDOR) {
        if (!url.startsWith("/store/vendor")) {
          return this.router.parseUrl("/store/vendor");
        }
      } else if (this.auth.role === RolesType.CUST_SERVICE) {
        if (!url.startsWith("/store/customer-services")) {
          return this.router.parseUrl("/store/customer-services");
        }
      } else {
        if (!url.startsWith("/store/dashboard")) {
          return this.router.parseUrl("/store/dashboard");
        }
      }
    }
    return true;
  }

  openDialog() {
    this.modalOption.backdrop = "static";
    this.modalOption.keyboard = false;
    this.modalOption.size = "xl";
    const dialogRef = this.dialog.open(
      SelectCustomerComponent,
      this.modalOption
    );
    dialogRef.result
      .then(() => {
        this.isDialogOpen = false;
      })
      .catch(() => {
        this.isDialogOpen = false;
      });
  }
}
