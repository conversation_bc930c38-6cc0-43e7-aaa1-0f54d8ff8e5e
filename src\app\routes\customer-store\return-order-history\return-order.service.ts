import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";

import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class ReturnOrderService {
  constructor(private http: HttpClient) { }

  getAll(data: any) {
    return this.http.post<any>(`${ApiConstant.RETURN_ORDER}/list`, data);
  }

  getAllRefundProgress() {
    return this.http.get<any>(ApiConstant.REFUND_PROGRESS);
  }

  getAllStatus() {
    return this.http.get<any>(ApiConstant.RETURN_STATUS);
  }

  getRetrunOrderDetails(data: any) {
    return this.http.post<any>(ApiConstant.RETURN_ORDER + '/detail', data);
  }
}
