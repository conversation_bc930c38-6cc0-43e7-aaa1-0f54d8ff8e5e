<ng-container *ngIf="product?.status==='ACTIVE'">
  <div class="all-main-title-sec product-details-main-title">
    <div class="all-bedcrumbs">
      <ul>
        <li>
          <a [routerLink]="['/store/dashboard']">
            <span class="material-icons-outlined">home</span> Home
          </a>
        </li>
        <li><a [routerLink]="['/store/catalogues']">Products</a></li>
        <li>{{ product?.name ? product?.name : (product.pd_description? product.pd_description.replaceAll('"','') : "")
          }}</li>
      </ul>
    </div>
  </div>

  <div class="desc-container">
    <div class="images" *ngIf="!loadingImage">
      <ng-container *ngIf="!productImages['1200X1200']">
        <img src="/assets/images/demo-product.png" class="img-responsive img-thumbnail w-100" />
      </ng-container>
      <ng-container *ngIf="productImages['1200X1200']">
        <div class="img-container" *ngIf="productImages['1200X1200'][activeSlides && activeSlides.current]">
          <lib-ngx-image-zoom [thumbImage]="productImages['1200X1200'][activeSlides.current].url"
            [fullImage]="productImages['1200X1200'][activeSlides.current].url"></lib-ngx-image-zoom>
          <span class="material-icons-outlined expand"
            (click)="expand(productImages['1200X1200'][activeSlides.current].url, content)">open_in_full</span>
        </div>
        <div>
          <span class="material-icons-outlined left" (click)="selectImage(activeSlides.previous)">chevron_left</span>
          <div class="carosol">
            <img *ngFor="let productImage of productImages['1200X1200']; let i = index"
              [class.active]="i === activeSlides.current" [src]="productImage.url" (click)="selectImage(i)" />
          </div>
          <span class="material-icons-outlined has-right right"
            (click)="selectImage(activeSlides.next)">chevron_right</span>
        </div>
      </ng-container>
    </div>
    <div class="desc">
      <div class="product-title-part">
        <div class="product-name">
          <span class="available-quantity" *ngIf="stock != 0 && !loadingStock"
            [style.background-color]="stock < lowStockQty && '#ff8000'">{{ stock }} Available</span>
          <span class="available-quantity" *ngIf="stock == 0 && !loadingStock" [style.background-color]="'red'">Out of
            Stock</span>
          {{ product?.name ? product?.name: (product?.product_desc || '').replaceAll('"','') }}
        </div>
        <div class="pro-like-share">
          <ul>
            <li><span class="material-icons-outlined">favorite_border</span> <span>Favorite</span></li>
            <li><span class="material-icons-outlined">share</span> <span>Share</span></li>
          </ul>
        </div>
      </div>
      <ul>
        <!-- <li><span class="material-icons-outlined">ballot</span> {{ product.product_desc }}</li>-->
        <li class="sku-id"><span class="material-icons-outlined">subtitles</span> SKU ID: <span>{{
            product.product_id }}</span></li>
      </ul>

      <div class="accordion accordion-flush" id="accordionFlushExample">
        <div class="accordion-item">
          <h2 class="accordion-header" id="flush-headingOne">
            <button class="accordion-button collapsed" type="button" (click)="toggleAccordian(accPanel,$event, 1)">
              <span class="material-icons-outlined">inventory_2</span> Product Details
            </button>
          </h2>
          <div id="flush-collapseOne" class="panel panel-details">
            <div class="accordion-body">
              <angular-editor [(ngModel)]="product.product_summary" [config]="editorConfig"></angular-editor>
              <br>
            </div>
          </div>
        </div>
        <div class="accordion-item">
          <h2 class="accordion-header" id="flush-headingTwo">
            <button class="accordion-button collapsed" type="button" (click)="toggleAccordian(accPanel,$event, 1)">
              <span class="material-icons-outlined">display_settings</span> Specification
            </button>
          </h2>
          <div id="flush-collapseTwo" class="panel panel-details">
            <div class="accordion-body">
              <angular-editor *ngIf="product.specification" [(ngModel)]="product.specification"
                [config]="editorConfig"></angular-editor>
              <div class="specification-container">
                <ng-container *ngFor="let item of classification | keyvalue">
                  <div class="card border-light w-100 shadow-sm">
                    <div class="card-header"><b>Class:</b> {{item.key}}</div>
                    <div class="card-body">
                      <ng-container *ngFor="let val of $any(item).value; let j = index">
                        <p class="card-title text-capitalize fw-bold">{{val?.class_charc_type_descr}}</p>
                        <p class="card-subtitle mb-2 text-muted" *ngIf="val?.formatted_charc_value">
                          {{val?.formatted_charc_value}}
                        </p>
                        <p class="card-subtitle mb-2 text-muted" *ngIf="!val?.formatted_charc_value">
                          &nbsp;
                        </p>
                      </ng-container>
                    </div>
                  </div>
                </ng-container>
              </div>
              <ng-container *ngFor="let productResource of productResources['SPECIFICATION']; let i = index">
                <div class="row">
                  <div class="col-12">
                    <img class="w-100" [src]="productResource.url" />
                  </div>
                </div>
              </ng-container>
              <br />
            </div>
          </div>
        </div>
        <div class="accordion-item">
          <h2 class="accordion-header" id="flush-headingThree">
            <button class="accordion-button collapsed" type="button" (click)="toggleAccordian(accPanel,$event, 1)">
              <span class="material-icons-outlined">view_array</span> Product Resources
            </button>
          </h2>
          <div id="flush-collapseThree" class="panel panel-details" aria-labelledby="flush-headingThree"
            data-bs-parent="#accordionFlushExample">
            <div class="accordion-body pdp-resources">
              <ul>
                <li *ngFor="let productResource of productResources['PDF']; let i = index">
                  <a [href]="productResource.url" target="_blank"
                    class="d-flex flex-column align-items-center text-center"> <i class="material-icons-outlined">
                      picture_as_pdf
                    </i>
                    <span>{{productResource.media_name}}</span> </a>
                </li>
                <li *ngFor="let productResource of productResources['VIDEO']; let i = index">
                  <a [href]="productResource.url" target="_blank"
                    class="d-flex flex-column align-items-center text-center"> <i
                      class="material-icons-outlined">play_circle</i>
                    <span>{{productResource.media_name}}</span> </a>
                </li>
                <li *ngFor="let productResource of productResources['SHEET']; let i = index">
                  <a [href]="productResource.url" target="_blank"
                    class="d-flex flex-column align-items-center text-center"> <i class="far fa-file-alt fa-2x"></i>
                    <span>Product Info Sheet</span> </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="add-to-cart-container">
      <p *ngIf="loadingPrice">Loading price...</p>
      <ng-container *ngIf="!loadingPrice">
        <div class="offer-part" style="background-image: url('/assets/images/price-bg.jpg');">
          <div class="offer-price">
            <h4>Starts</h4>
            <small>As low as</small>
          </div>
          <div class="offr-date">{{ sub_total | currency }}</div>
        </div>
      </ng-container>

      <div class="add-to-cart-body">
        <div class="order-counter">
          <h4 class="mb-0">Min. Order Qty: 1</h4>
          <small>Item Price: {{product_price | currency }}</small>
          <div class="mt-4"></div>
          <item-counter [max]="maxQuantity" [control]="$any(addToCartForm.get('quantity'))"
            *checkPermission="'P0019'"></item-counter>
        </div>
        <button type="button" class="btn" (click)="addToCart(product)" *checkPermission="'P0019'"><span
            class="material-icons-outlined">shopping_cart</span> Add to cart</button>
        <button type="button" class="btn btn-light" [routerLink]="['/store/catalogues']"><span
            class="material-icons-outlined">reply</span> Back to listing </button>
      </div>
    </div>
  </div>
  <ng-container *ngIf="similarProducts.length">
    <hr>
    <div class="d-flex justify-content-between align-items-center">
      <span class="simliar-text">More Item to Consider</span>
      <span class="d-flex view-more-text" [routerLink]="['/store/catalogues']">View More
        <span class="material-icons-outlined">arrow_forward</span>
      </span>
    </div>
    <div class="similar-products">
      <app-product-view *ngFor="let product of similarProducts" [detail]="product"></app-product-view>
    </div>
  </ng-container>
</ng-container>
<app-loader *ngIf="loading"></app-loader>
<ng-container *ngIf="!loading && (!product || (product?.status==='INACTIVE'))">
  <div class="not-found d-flex align-items-center justify-content-center vh-50">
    <div class="text-center row">
      <div class="col-md-12 mt-5">
        <p class="fs-3"> <span class="text-danger">Opps!</span> Product Unavailable or Inactive.</p>
        <p class="lead col-7 m-auto my-3">
          Please note that the product you are looking for is currently unavailable or inactive. We apologize for any
          inconvenience caused.
        </p>
        <button type="button" class="btn btn-light w-25 m-auto" [routerLink]="['/store/catalogues']"> <span
            class="material-icons-outlined">local_mall</span> Continue Shopping </button>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #content let-modal>
  <div class="modal-body">
    <button type="button" class="btn-close position-absolute" aria-label="Close"
      (click)="modal.dismiss('Cross click')"></button>
    <lib-ngx-image-zoom [thumbImage]="productImages['1200X1200'][activeSlides.current].url"
      [fullImage]="productImages['1200X1200'][activeSlides.current].url"></lib-ngx-image-zoom>
  </div>
</ng-template>