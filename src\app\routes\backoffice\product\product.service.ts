import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ApiConstant } from 'src/app/constants/api.constants';

export interface ContactListParam {
  perPage?: number;
  pageNo?: number;
  search?: string;
  [param: string]: any;
}

@Injectable({
  providedIn: 'root'
})
export class ProductService {


  constructor(private http: HttpClient) { }


  getAll(data: any) {
    const params = new HttpParams().appendAll({ ...data, ...{ search: data.search, searchBy: data.searchBy } });
    return this.http.get<any>(ApiConstant.GET_ALL_PRODUCTS, {
      params,
    });
  }

  getById(id: string) {
    return this.http.get<any>(ApiConstant.GET_ALL_PRODUCTS + '/' + id);
  }

  getPlant(id: any) {
    return this.http.get<any>(`${ApiConstant.PRODUCT_PLANT}?productID=${id}`, {}).pipe(map(res => res.data));;
  }

  getDescription(id: any) {
    return this.http.get<any>(`${ApiConstant.PRODUCT_DESCRIPTION}?productID=${id}`, {}).pipe(map(res => res.data));;
  }

  getAllCategory() {
    return this.http.get<any>(`${ApiConstant.GET_CATEGORIES}`, {}).pipe(map(res => res.data));;
  }

  getProductCategory(id: any) {
    return this.http.get<any>(`${ApiConstant.PRODUCT_CATEGORIES.replace('{product_id}', id)}`, {}).pipe(map(res => res.data));;
  }

  setProductCategory(id: any, data: any) {
    return this.http.post<any>(`${ApiConstant.PRODUCT_CATEGORIES.replace('{product_id}', id)}`, data).pipe(map(res => res.data));;
  }

  getAllCatalog() {
    return this.http.get<any>(`${ApiConstant.GET_CATALOGS}`, {}).pipe(map(res => res.data));;
  }

  getProductCatalog(id: any) {
    return this.http.get<any>(`${ApiConstant.PRODUCT_CATALOGS.replace('{product_id}', id)}`, {}).pipe(map(res => res.data));;
  }

  setProductCatalog(id: any, data: any) {
    return this.http.post<any>(`${ApiConstant.PRODUCT_CATALOGS.replace('{product_id}', id)}`, data).pipe(map(res => res.data));;
  }

  getMedia(id: any) {
    return this.http.get<any>(`${ApiConstant.PRODUCT_IMAGE}?productID=${id}`, {});
  }

  removeMedia(id: any) {
    return this.http.delete<any>(`${ApiConstant.PRODUCT_IMAGE}/${id}`, {});
  }

  submitMediaForm(data: any) {
    return this.http.post<any>(ApiConstant.PRODUCT_IMAGE, data);
  }

  submitGeneralForm(data: any) {
    return this.http.post<any>(ApiConstant.GET_ALL_PRODUCTS, data);
  }

  getRelationshipTypes() {
    return this.http.get<any>(ApiConstant.RELATIONSHIP_TYPES);
  }

}
