import { Component, ElementRef, ViewChild } from '@angular/core';
import { IHeaderAngularComp } from 'ag-grid-angular'
import { IHeaderParams } from 'ag-grid-community';

export interface ICustomHeaderParams {
    menuIcon: string;
}

@Component({
    selector: 'app-custom-header',
    templateUrl: './grid-header.component.html',
    styleUrls: ['./grid-header.component.scss']
})
export class CustomHeader implements IHeaderAngularComp {
    public params!: IHeaderParams & ICustomHeaderParams;
    public descSort = false;
    public ascSort = false;
    @ViewChild('menuButton', { read: ElementRef }) public menuButton!: ElementRef;

    agInit(params: IHeaderParams & ICustomHeaderParams): void {
        this.params = params;
        params.column.addEventListener('sortChanged', this.onSortChanged.bind(this));
        this.onSortChanged();
    }

    onSortChanged() {
        this.ascSort = this.descSort = false;
        if (this.params.column.isSortAscending()) {
            this.ascSort = true;
        } else if (this.params.column.isSortDescending()) {
            this.descSort = true;
        }
    }

    onMenuClicked() {
        this.params.showColumnMenu(this.menuButton.nativeElement);
    }

    sort() {
        if(!this.params.enableSorting){
            return
        }
        if (this.params.column.isSortAscending()) {
            this.params.setSort('desc');
        } else if (this.params.column.isSortNone()) {
            this.params.setSort('asc');
        } else if (this.params.column.isSortDescending()) {
            this.params.setSort('asc');
        }
    }

    refresh(params: IHeaderParams) {
        return false;
    }
}