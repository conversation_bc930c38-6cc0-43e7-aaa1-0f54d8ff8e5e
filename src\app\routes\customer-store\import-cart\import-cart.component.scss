:host {
	padding: 25px;
	overflow: auto;
}

.all-main-title-sec.cart-details-main-title {
	align-items: flex-start;
	justify-content: flex-start;
	text-align: left;
	padding: 0 0 25px 0;

	h1 {
		text-align: left;
		padding: 0;
		display: flex;
		align-items: center;
		gap: 0 10px;
		justify-content: space-between;
	}

	small {
		margin: 0;
		padding: 0;
		font-size: 15px !important;
		color: var(--snjy-color-dark-secondary);
	}
}

.import-saved-card-body {
	margin: 0;
	padding: 0;
	position: relative;
	display: flex;
	gap: 0 2%;

	.import-saved-cart-box {
		margin: 10px 0 30px 0;
		padding: 30px;
		background: var(--snjy-color-white);
		border-radius: 12px;
		border: 1px solid rgba(208, 215, 216, 0.3882352941);
		box-shadow: var(--snjy-box-shadow);
		display: block;
		min-height: 200px;
		flex: 1;

		h3 {
			margin: 0 0 15px 0;
			padding: 0;
			position: relative;
			font-size: var(--snjy-font-size-1-125);
			font-weight: var(--snjy-font-weight-medium);
			color: #00216c;
			line-height: 20px;
		}

		ul {
			margin: 0;
			padding: 0;
			list-style: none;
			position: relative;

			li {
				margin: 0 0 6px 0;
				padding: 0 0 0 22px;
				color: #687491;
				font-weight: var(--snjy-font-weight-medium);
				font-size: var(--snjy-font-size-0-8);
				position: relative;

				span {
					margin: 0;
					padding: 0;
					position: absolute;
					width: fit-content;
					height: fit-content;
					font-size: 16px !important;
					left: 0;
					color: #0077d7;
				}

				a {
					font-weight: var(--snjy-font-weight-bold);
					text-decoration: underline;
					color: #0086f9;
				}
			}
		}

		.upload-file-link {
			margin: 40px auto 0 auto;
			border-radius: 8px;
			margin-bottom: 0.8rem;
			padding: 0 40px;
			min-height: 50px;
			font-family: var(--snjy-font-family);
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 0 10px;
			font-size: var(--snjy-font-size-0-875);
			line-height: 16px;
			font-weight: var(--snjy-font-weight-medium);
			background-color: var(--snjy-font-color-primary) !important;
			color: var(--snjy-button-color-primary) !important;
			border: 1px solid var(--snjy-button-color-primary) !important;
		}
	}
}

.save-c-btn {
	margin: 15px 0 0 0;

	button {
		border-radius: 10px;
		display: flex;
		font-size: var(--snjy-font-size-0-8);
		height: 46px;
		align-items: center;
		justify-content: center;
		min-width: 240px;
		padding-bottom: 0;
		padding-top: 0;
		cursor: pointer;
		color: var(--snjy-color-white);
		background: var(--snjy-button-gradient);
		position: relative;
		margin: 0 0 0 auto;
		text-transform: uppercase;
		font-weight: var(--snjy-font-weight-medium);
		transition: all0 0.3s ease-in-out;
		border: none;
	}

}

.file-input {
	display: none;
}

.red {
	color: red;
}