import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class CustomerGroupService {
  constructor(private http: HttpClient) {}

  getSalesAreas() {
    return this.http
      .get<any>(`${ApiConstant.CUSTOMER_SALES_AREA}/group`)
      .pipe(map((res) => res.data));
  }
}
