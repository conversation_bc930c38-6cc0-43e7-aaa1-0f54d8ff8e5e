import {
  Component,
  EventEmitter,
  HostListener,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import {
  ColDef,
  ColumnMovedEvent,
  GridOptions,
  IDatasource,
  GridApi,
  IGetRowsParams,
} from "ag-grid-community";
import { map, tap } from "rxjs";
import { GridColumn } from "src/app/shared/components/grid-column/grid-column.model";
import { Product } from "../models/product.model";
import { ProductService } from "./product.service";


export interface RepoSearchList {
  incomplete_results: boolean;
  data: any[];
  total: number;
}

@Component({
  selector: "app-product",
  templateUrl: "./product.component.html",
  styleUrls: ["./product.component.scss"],
})
export class ProductComponent implements OnInit {
  public columnDefs: ColDef[] = [
    {
      field: "product_id",
      headerName: this.getTranslate("backoffice.product.productId"),
      sortable: true,
    },
    {
      field: "pd_description",
      headerName: this.getTranslate("backoffice.product.desc"),
      sortable: true,
    },
    {
      field: "item_category_group",
      headerName: this.getTranslate("backoffice.product.category"),
      sortable: true,
    },
    {
      field: "status",
      headerName: this.getTranslate("backoffice.product.status"),
      sortable: true,
    },
    // {
    //   field: "catalog_name",
    //   headerName: this.getTranslate("backoffice.product.catalog"),
    //   sortable: true,
    //   filter: true,
    // },
    {
      field: "base_unit",
      headerName: this.getTranslate("backoffice.product.baseUnit"),
      sortable: true,
    },
  ];
  public defaultColDef: ColDef = {
    flex: 1,
    minWidth: 150,
    filter: false,
  };

  public rowData!: any[];
  private gridApi!: any;
  private gridColumnApi!: any;
  isLoading = true;
  query = {
    perPage: 10,
    pageNo: 1,
  };
  pageSize = 10;
  rowClicked: Boolean = false;
  model: Product = {};
  searchText: string = '';
  searchBy: string = 'product_id';

  gridOptions: GridOptions = {
    suppressMenuHide: true,
    pagination: true,
    cacheBlockSize: 10,
    paginationPageSize: 10,
    rowModelType: "infinite",
    onColumnMoved: (event: ColumnMovedEvent) => this._onColumnMoved(event),
  };
  relationshipTypes = [];

  constructor(
    private productService: ProductService,
    private translate: TranslateService
  ) { }

  ngOnInit(): void {
    this.getRelationShipTypes();
  }

  getRelationShipTypes() {
    this.productService.getRelationshipTypes().subscribe({
      next: (value) => {
        this.relationshipTypes = value;
      },
    });
  }

  @HostListener("window:resize", ["$event"])
  onResize(event: any) {
    this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params: any) {
    params.api.showLoadingOverlay();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
    this.gridApi.setDatasource(this.dataSource);
  }

  dataSource: IDatasource = {
    getRows: (params: IGetRowsParams) => {
      this.query.pageNo = params.endRow / this.query.perPage;
      this.productService
        .getAll({
          perPage: this.pageSize,
          search: this.searchText,
          searchBy: this.searchBy,
          pageNo: params.endRow / this.pageSize
        })
        .pipe(
          tap(({ data, total }) => {
            return params.successCallback(data, total);
          }),
          tap(() => this.gridApi.hideOverlay())
        )
        .subscribe();
    },
  };

  getTranslate(key: string) {
    return this.translate.instant(key);
  }

  handleCellClicked(row: any) {
    this.model = row.data;
    this.rowClicked = true;
  }

  @ViewChild("columnMenu") columnMenu: any;
  @Output() columnChange = new EventEmitter<GridColumn[]>();
  _onColumnChange(columns: any[]) {
    this.columnChange.emit(columns);

    const displayedColumns = Object.assign(
      [],
      this.getDisplayedColumnFields(columns)
    );
    const allColumns = Object.assign(
      [],
      this.getAllDisplayedColumnFields(columns)
    );

    this.gridColumnApi.setColumnsVisible(displayedColumns, false);
    this.gridColumnApi.setColumnsVisible(allColumns, true);
    this.gridApi.sizeColumnsToFit();
  }
  getDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => !item.show)
      .map((item: GridColumn) => item.field);
    return fields;
  }

  onSearchChange(text: string) {
    this.searchText = text;
    this.updateTableAfterSearch()
  }

  onSearchByChange(option: string) {
    this.searchBy = option;
    this.updateTableAfterSearch()
  }

  updateTableAfterSearch() {
    this.gridApi.paginationGoToPage(0);
    setTimeout(() => {
      this.gridApi.purgeInfiniteCache();
    }, 100);
  }

  getAllDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => item.show)
      .map((item: GridColumn) => item.field);
    return fields;
  }

  _columnPositionChange(position: number[]) {
    this.gridColumnApi.moveColumnByIndex(position[0], position[1]);
    this.gridApi.sizeColumnsToFit();
  }

  _onColumnMoved(params: any) {
    const columnDragState = params.columnApi.getColumnState();
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map((e: { colDef: any }) => {
        return e.colDef;
      });
    const newColDef: ColDef[] = colIds.map((item: any, i: string | number) =>
      Object.assign({}, item, columnDragState[i])
    );
    this.columnDefs = newColDef;
  }

  refreshRowData(data: any) {
    const rowNode = this.gridApi.getRowNode(data.product_id)!;
    rowNode && rowNode.setData(data);
  }

  getRowId(params: any) {
    return params.data.product_id;
  };
}
