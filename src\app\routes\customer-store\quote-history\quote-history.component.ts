import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { Router } from "@angular/router";
import { Subject, takeUntil } from "rxjs";
import {
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from "@angular/forms";
import moment from "moment";

import { AuthService } from "src/app/core/authentication/auth.service";
import { QuoteHistoryService } from "./quote-history.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { SettingsService } from "../../backoffice/settings/settings.service";
import { NgbCalendar, NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import * as XLSX from 'xlsx';
import { ColDef } from "ag-grid-community";

@Component({
  selector: "app-quote-history",
  templateUrl: "./quote-history.component.html",
  styleUrls: ["./quote-history.component.scss"],
})
export class QuoteHistoryComponent implements On<PERSON><PERSON>t, On<PERSON><PERSON>roy {
  private ngUnsubscribe = new Subject<void>();
  public moment: any = moment;
  public statuses: any = [];
  public quotes: any = [];
  public sellerDetails: any = {};
  public loading = false;
  public submitted = false;
  public filterForm: FormGroup;

  public columnDefs: ColDef[] = [
    {
      field: "SD_DOC",
      headerName: 'Quote #',
      headerComponentParams: { menuIcon: 'pin' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">pin</i>${data.data.SD_DOC}</span>`;
      },
      comparator: (valueA, valueB, nodeA, nodeB, isDescending) =>  parseInt(valueA) - parseInt(valueB),
      sortable: true,
      resizable: true
    },
    {
      field: "DOC_NAME",
      headerName: 'Name',
      headerComponentParams: { menuIcon: 'badge' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">badge</i>${data.data.DOC_NAME}</span>`;
      },
      sortable: true,
      resizable: true
    },
    {
      field: "DOC_DATE",
      headerName: 'Date Placed',
      sortable: true,
      headerComponentParams: { menuIcon: 'calendar_month' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">calendar_month</i>${this.formatDate(data.data.DOC_DATE)}</span>`;
      },
      resizable: true
    },
    {
      field: "DOC_STATUS",
      headerName: "Quote Status",
      headerComponentParams: { menuIcon: 'feed' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">feed</i>${this.getStatusName(data.data.DOC_STATUS)}</span>`;
      },
      resizable: true
    }
  ];

  constructor(
    private _snackBar: AppToastService,
    public router: Router,
    private settingsService: SettingsService,
    public authService: AuthService,
    public quoteHistoryService: QuoteHistoryService,
    private calendarService: NgbCalendar
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit(): void {
    this.createForm();
    this.getSettings();
  }

  formatDate(input: string) {
    return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');
  }

  getSettings() {
    this.settingsService
      .getSettings()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          if (data?.sales_quote_type_code) {
            this.filterForm.patchValue({
              DOC_TYPE: data?.sales_quote_type_code,
            });
          }
          this.getAllStatuses();
        },
        error: (e: any) => {
          console.error("Error while processing settings request.", e);
        },
      });
  }

  createForm() {
    this.filterForm = new FormGroup(
      {
        DOCUMENT_DATE: new FormControl(null),
        DOCUMENT_DATE_TO: new FormControl(null),
        DOC_STATUS: new FormControl(""),
        SD_DOC: new FormControl(""),
        DOC_TYPE: new FormControl("QT"),
        SOLDTO: new FormControl(this.sellerDetails.customer_id),
        VKORG: new FormControl(this.sellerDetails.sales_organization),
        COUNT: new FormControl("100"),
      },
      [Validators.required, this.dateRangeValidator]
    );
  }

  clearFormField(name: string) {
    const obj: any = {};
    obj[name] = "";
    this.filterForm.patchValue(obj);
  }

  // convenience getter for easy access to form fields
  get f(): any {
    return this.filterForm?.controls;
  }

  private dateRangeValidator: ValidatorFn = (): {
    [key: string]: any;
  } | null => {
    let invalid = false;
    const from = this.filterForm && this.filterForm.get("DOCUMENT_DATE")?.value;
    const to =
      this.filterForm && this.filterForm.get("DOCUMENT_DATE_TO")?.value;
    if ((from && !to) || (!from && to)) {
      invalid = true;
    } else if (from && to) {
      invalid = new Date(from).valueOf() > new Date(to).valueOf();
    }
    return invalid ? { invalidRange: { from, to } } : null;
  };

  today() {
    return this.calendarService.getToday();
  }

  search() {
    this.getQuoteHistory();
  }

  getStatusName(code: string) {
    const status = this.statuses.find((o: any) => o.code === code);
    if (status) {
      return status.description;
    }
    return "";
  }

  getAllStatuses() {
    this.quoteHistoryService
      .getAllStatuses()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.statuses = res?.data || [];
          const statuses = this.statuses.map((val: any) => val.code).join(";");
          this.statuses.unshift({ code: statuses, description: "All" });
          this.filterForm.patchValue({ DOC_STATUS: statuses });
          this.getQuoteHistory();
        },
        error: () => {
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  getQuoteHistory() {
    this.submitted = true;
    if (this.filterForm.invalid) {
      return;
    }
    const payload: any = this.filterForm.value;
    payload.DOCUMENT_DATE = this.formatSearchDate(payload.DOCUMENT_DATE);
    payload.DOCUMENT_DATE_TO = this.formatSearchDate(payload.DOCUMENT_DATE_TO);
    this.loading = true;
    return this.quoteHistoryService
      .getAll(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.loading = false;
          this.submitted = false;
          this.quotes = res?.data?.SALESQUOTES || [];
        },
        error: () => {
          this.loading = false;
          this.submitted = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  clear() {
    this.filterForm.patchValue({ DOCUMENT_DATE: null });
    this.filterForm.patchValue({ DOCUMENT_DATE_TO: null });
    this.filterForm.patchValue({ SD_DOC: "" });
    const status = this.statuses.find(
      (val: any) => val.description === "All"
    );
    this.filterForm.patchValue({ DOC_STATUS: status.code });
  }

  formatSearchDate(date: NgbDateStruct) {
    if (!date) return "";
    let newDate = new Date(date["year"], date["month"] - 1, date["day"]);
    return moment(newDate).format("YYYYMMDD");
  }

  goToQuote(quote: any) {
    this.router.navigate([`/store/quotes/${quote[0].SD_DOC}`]);
  }

  exportToExcel() {
    const fileName = 'quotes-history.xlsx';
    const data = this.formatData(this.quotes)
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'test');

    XLSX.writeFile(wb, fileName);
  }

  formatData(quotes: any) {
    return quotes.map(({ DOC_NAME, DOC_STATUS, DOC_DATE, SD_DOC
    }: { DOC_NAME: string, DOC_STATUS: string, DOC_DATE: string, SD_DOC: string }) => ({ 'Quote #': SD_DOC, 'Name': DOC_NAME, 'Date Placed': moment(DOC_DATE).format("MM/DD/YYYY"), 'Quote Status': this.getStatusName(DOC_STATUS) }));
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
