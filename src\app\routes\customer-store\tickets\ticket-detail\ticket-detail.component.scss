:host {
  padding: 25px;
}

.ticket-id-sec {
  margin: 0;
  padding: 0;
  position: relative;

  .ticket-id-body {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
    gap: 0 3%;

    .ticket-id-info {
      margin: 0;
      padding: 0;
      flex: 4;

      .ticket-details {
        margin: 0 0 20px 0;
        padding: 24px;
        background: var(--snjy-color-white);
        border: 1px solid var(--snjy-border-color-secondary);
        border-radius: 10px;
        box-shadow: var(--snjy-box-shadow);

        h3 {
          margin: 0 0 15px 0;
          padding: 0;
          position: relative;
          font-size: var(--snjy-font-size-1-125);
          font-weight: var(--snjy-font-weight-bold);
          color: #00216c;
          line-height: 20px;

          span {
            font-size: var(--snjy-font-size-1);
            font-weight: var(--snjy-font-weight-medium);
            color: #959595;
          }
        }

        ul {
          padding: 34px 30px;
          position: relative;
          list-style: none;
          background: #f0f5f6;
          border-radius: 8px;
          display: flex;
          justify-content: flex-start;
          gap: 40px 4%;
          flex-wrap: wrap;

          li {
            margin: 0;
            padding: 0;
            color: #687491;
            font-weight: var(--snjy-font-weight-medium);
            font-size: var(--snjy-font-size-0-8);
            flex: 0 0 21%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 0 3px;
            line-height: 22px;

            .material-icons-outlined {
              margin: 0;
              padding: 0;
              width: fit-content;
              height: fit-content;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              font-size: var(--snjy-font-size-1-25);
              color: #687491;
            }

            span:not(.material-icons-outlined) {
              margin: 0;
              padding: 0 0 0 22px;
              display: block;
              color: #0077d7;
              font-weight: var(--snjy-font-weight-medium);
              font-size: var(--snjy-font-size-0-8);
              width: 100%;
            }
          }
        }

        .t-messages {
          margin: 0 0 20px 0;
          padding: 0 0 20px 0;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid rgba(208, 215, 216, 0.3882352941);

          h4 {
            margin: 0;
            padding: 0;
            position: relative;
            font-size: var(--snjy-font-size-1-25);
            font-weight: var(--snjy-font-weight-bold);
            color: var(--snjy-button-color-primary);
            line-height: 18px;
          }

          .add-message-btn {
            font-size: var(--snjy-font-size-0-75);
            color: #4e4e4e;
            top: 2px;
            position: relative;
            text-transform: uppercase;
            border: 1px solid #dfdfdf;
            padding: 8px 15px 8px 9px;
            border-radius: 8px;
            background: var(--snjy-font-color-primary);
            font-weight: var(--snjy-font-weight-medium);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0 7px;
            box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);
            min-width: 130px;

            .material-icons-outlined {
              width: fit-content;
              height: fit-content;
              font-size: var(--snjy-font-size-1-25);
              color: var(--snjy-button-color-primary);
            }
          }
        }
      }

      .t-messages-box {
        margin: 0;
        padding: 0;
        position: relative;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        h3 {
          width: 100%;
        }

        .t-messages-box-body {
          margin: 0;
          flex: 0 0 55%;
          padding: 25px;
          position: relative;
          list-style: none;
          background: #f0f5f6;
          border-radius: 8px;

          .m-details {
            margin: 5px 0;
            padding: 12px 0px;
            font-size: var(--snjy-font-size-0-9);
            position: relative;
            color: var(--snjy-color-dark-secondary);
            font-weight: var(--snjy-font-weight-medium);
            border-top: 1px solid #cfcfcf;
          }

          .m-file {
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 8px;
            font-size: var(--snjy-font-size-0-9);
            position: relative;
            color: var(--snjy-color-text-ternary);
            font-weight: var(--snjy-font-weight-medium);
          }
        }

        .t-messages-box-table {
          margin: 0;
          flex: 0 0 43%;
          padding: 25px;
          position: relative;
          list-style: none;
          background: #f0f5f6;
          border-radius: 8px;

          h3 {
            margin: 0 0 0 0 !important;
            top: -100px;
            left: -27px;
            color: var(--snjy-button-color-primary) !important;
          }

          a {
            color: #0d6efd;
            display: inline-block;
            max-width: 250px;
            vertical-align: middle;
          }

          table.table {
            border-collapse: separate;
            border-spacing: 0 10px;
          }

          .table thead th {
            padding: 17px 10px;
            background: #0077d7 !important;
            border: none;
          }

          .table tbody td {
            padding: 14px 10px 14px 30px;
            background: var(--snjy-font-color-primary);
            border: none;
            font-size: var(--snjy-font-size-0-875);
            color: var(--snjy-color-dark-secondary);
            font-family: var(--snjy-font-family);
            font-weight: var(--snjy-font-weight-medium);
            position: relative;
          }

          .table tbody td:before {
            position: absolute;
            content: "";
            font-family: "Material Icons Outlined";
            top: 0;
            left: 9px;
            bottom: 0;
            margin: auto 0;
            font-size: var(--snjy-font-size-1-125);
            color: #b7b7b7;
            font-weight: var(--snjy-font-weight-normal);
            line-height: 20px;
            height: fit-content;
          }

          .table tbody td:first-child::before {
            content: "\f045";
          }

          .table tbody td:nth-child(2)::before {
            content: "\ebcc";
          }

          .table tbody td:nth-child(3)::before {
            content: "\e227";
          }

          .table tbody td:nth-child(4)::before {
            content: "\e53e";
          }

          .table tbody td:nth-child(5)::before {
            content: "\ebcc";
          }

          .table tr td:first-child,
          .table tr th:first-child {
            border-top-left-radius: 8px;
          }

          .table tr td:last-child,
          .table tr th:last-child {
            border-top-right-radius: 8px;
          }

          .table tr td:first-child,
          .table tr th:first-child {
            border-bottom-left-radius: 8px;
          }

          .table tr td:last-child,
          .table tr th:last-child {
            border-bottom-right-radius: 8px;
          }

          table.table thead th .invoice-table-box {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-875);
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-color-white);
            line-height: 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 2px;
          }

          table.table thead th .invoice-table-box .material-icons-outlined {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-1);
            color: var(--snjy-color-white);
            width: fit-content;
            height: fit-content;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 991px) {
  .ticket-details ul li {
    flex: 0 0 45% !important;
  }

  .t-messages-box .row .col {
    flex: 100%;
  }

  .t-messages-box .row .col h3 {
    top: 0 !important;
    left: 0 !important;
  }

  .ticket-id-sec .ticket-id-body .ticket-id-info .t-messages-box .t-messages-box-table h3 {
    margin: 0 0 20px 0 !important;
    top: 0;
    left: 0;
  }

  .ticket-id-sec .ticket-id-body .ticket-id-info .t-messages-box .t-messages-box-table tr {
    padding: 0 !important;
  }
}

@media only screen and (max-width: 576px) {
  .t-messages-box .row.w-100 {
    margin: 0 !important;
    max-width: 100% !important;
  }

  .t-messages-box .row.w-100 .col {
    padding: 0;
  }

  .t-messages-box .row.w-100 .col .t-messages-box-body,
  .t-messages-box-table {
    padding: 15px !important;
  }

  .ticket-details {
    padding: 15px !important;
  }

  .ticket-details ul {
    gap: 20px !important;
    flex-direction: column;
    padding: 15px !important;
  }

  .ticket-details ul li {
    flex: 0 0 100% !important;
  }
}