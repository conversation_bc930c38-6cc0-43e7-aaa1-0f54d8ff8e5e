<div class="all-main-title-sec cart-details-main-title">
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>Quick Order</li>
    </ul>
  </div>
  <h1>Quick Order</h1>
  <small>You can add <strong>up to 20</strong> valid SKU’s below and add to cart</small>
</div>
<div class="save-c-table">
  <table class="table all-table">
    <thead>
      <tr>
        <th width="400">
          <div class="save-c-table-box"><span class="material-icons-outlined">ballot</span> Product</div>
        </th>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">attach_money</span> Price</div>
        </th>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">production_quantity_limits</span>
            Quantity</div>
        </th>
        <th>
          <div class="save-c-table-box"><span class="material-icons-outlined">attach_money</span> Total</div>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let product of products;let i = index;">
        <td data-label="Product">
          <ng-template #rt let-r="result" let-t="term">
            <ngb-highlight [result]="r.product_id" [term]="t"></ngb-highlight>
            <h6>{{r?.pd_description}}</h6>
          </ng-template>
          <input type="text" class="form-control" placeholder="SKU#" [(ngModel)]="product.skuNo" [ngbTypeahead]="search"
            [resultTemplate]="rt" [inputFormatter]="formatter" (selectItem)="selectedItem($event, product)">
          <button class="add-sku-btn" (click)="clearRow(i)" *ngIf="!product.loading && !product.error">
            <span class="material-icons-outlined">highlight_off</span>
          </button>
          <button class="add-sku-btn" *ngIf="product.loading">
            <div class="spinner-border text-white" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </button>
          <button class="add-sku-btn" *ngIf="!product.loading && product.error">
            <span class="material-icons-outlined">error</span>
          </button>
        </td>
        <td data-label="Price">{{product.price | currency}}</td>
        <td data-label="Qty"><input type="number" min="1" class="form-control qty" placeholder="Quantity"
            [(ngModel)]="product.qty" (ngModelChange)="calculateTotal(product)"></td>
        <td data-label="Total">{{product.total | currency}}</td>
      </tr>
    </tbody>
  </table>
</div>
<div class="save-c-btn">
  <button type="button" class="btn" (click)="addSku()"><span class="material-icons-outlined">add</span> Add
    SKU</button>
  <button type="button" class="btn" (click)="resetForm()"><span class="material-icons-outlined">restart_alt</span> Reset
    Form</button>
  <button type="button" class="btn" [disabled]="isDisabled() || saving" (click)="addToCart()"><span
      class="material-icons-outlined">add_shopping_cart</span> {{ saving ? 'Adding' : 'Add' }} to
    Cart</button>

</div>