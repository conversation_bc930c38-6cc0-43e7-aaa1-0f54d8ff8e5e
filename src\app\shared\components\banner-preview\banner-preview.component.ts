import { Component, Input, ViewEncapsulation } from "@angular/core";
import { <PERSON><PERSON>anitizer, SafeHtml } from "@angular/platform-browser";
import { Subject, takeUntil } from "rxjs";

import { BannerPreviewService } from "./banner-preview.service";
import { AppToastService } from "../../services/toast.service";

@Component({
  selector: "app-banner-preview",
  templateUrl: "./banner-preview.component.html",
  styleUrls: ["./banner-preview.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class BannerPreviewComponent {
  private ngUnsubscribe = new Subject<void>();
  @Input() customerID: any = null;
  @Input() settings: any = null;
  @Input() placement: any = "HOME";
  public banners: any[] = [];

  constructor(
    private _sanitizer: DomSanitizer,
    private bannerPreviewService: BannerPreviewService,
    private _snackBar: AppToastService
  ) {}

  ngOnInit(): void {
    if (this.customerID && this.placement) {
      this.getCustGroupBanner();
    }
  }

  transform(value: string): SafeHtml {
    return this._sanitizer.bypassSecurityTrustHtml(value);
  }

  getCustGroupBanner() {
    this.bannerPreviewService
      .getBannerByCustId(this.customerID, this.placement)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          this.banners = data.map((o: any) => {
            if (o.caption) {
              o.caption = this.transform(o.caption);
            }
            return o;
          });
        },
        error: (err: any) => {
          this._snackBar.open(
            err.error?.message ||
              "Error while processing get user's customer request.",
            { type: "Error" }
          );
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
