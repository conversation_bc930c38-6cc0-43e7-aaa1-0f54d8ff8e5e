import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';

import { QuickOrderComponent } from './quick-order.component';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [
    QuickOrderComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    NgbTypeaheadModule,
    RouterModule.forChild([{ path: '', component: QuickOrderComponent }]),
  ]
})
export class QuickOrderModule { }
