import { Component, ViewChild } from '@angular/core';
import { SavedCartsService } from '../saved-carts/saved-carts.service';
import { Router } from '@angular/router';
import { AppToastService } from 'src/app/shared/services/toast.service';

@Component({
  selector: 'app-import-cart',
  templateUrl: './import-cart.component.html',
  styleUrls: ['./import-cart.component.scss']
})
export class ImportCartComponent {
  fileName = '';
  file: File | undefined;
  maxFileSize = 300 * 1000;
  saving: boolean = false;

  @ViewChild("fileInput") fileInput: any;

  constructor(
    private service: SavedCartsService,
    private _snackBar: AppToastService,
    public router: Router
  ) { }

  onFileSelected(event: any) {
    this.fileName = '';
    this.file = undefined;
    const file: File = event.target.files[0];
    if (file) {
      if (file.size > this.maxFileSize) {
        this._snackBar.open('Maximum file size limit exceeded.', { type: 'Warning' });
        return;
      }
      this.fileName = file.name;
      this.file = file;
    }
  }

  removeFile(event: any) {
    event.stopPropagation();
    this.fileName = '';
    this.file = undefined;
    this.fileInput.nativeElement.value = '';
  }

  upload() {
    if (!this.file) {
      return;
    }
    this.saving = true;
    const formData = new FormData();
    formData.append("csvFile", this.file);
    this.service.import(formData).subscribe({
      next: (value) => {
        this.saving = false;
        this.router.navigate(["/store/saved-cart"]);
      },
      error: (err: any) => {
        this.saving = false;
        this._snackBar.open(err.error?.message || 'Error while processing your request.', { type: 'Error' });
      }
    })
  }
}
