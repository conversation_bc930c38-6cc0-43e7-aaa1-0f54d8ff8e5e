table {
  min-width: 700px;

  thead {
    background: var(--snjy-color-text-secondary);
    color: var(--snjy-color-main-background);
    font-family: var(--snjy-font-family);

    th {
      padding: 1rem 2rem;
    }
  }

  td {
    font-size: var(--snjy-font-size-0-875);
    padding: 2.5rem 2rem;
    color: var(--snjy-color-dark-secondary);
    font-family: var(--snjy-font-family);
    font-weight: var(--snjy-font-weight-medium);

    .circle {
      height: 12px;
      width: 12px;
      border-radius: 50%;
      display: inline-block;

      &.Cancelled {
        background-color: red;
      }

      &.Completed {
        background-color: green;
      }
    }
  }

  tbody tr {
    cursor: pointer;
  }
}

$color_1: var(--snjy-button-color-primary);
$color_2: #2e3237;
$color_3: #00216c;
$color_4: #999999;
$color_5: var(--snjy-color-dark-secondary);
$color_6: #b3b3b3;
$color_7: #a1a1a1;
$color_8: #b7b7b7;
$color_9: var(--snjy-color-white);
$font-family_1: "Material Icons Outlined";

.order-status-sec {
  margin: 0;
  padding: 0;
  position: relative;

  .order-status-body {
    margin: 0 auto;
    padding: 0 25px 50px 25px;
    position: relative;
    max-width: 100%;
  }
}

.order-contact-list {
  margin: 0;
  padding: 18px 20px;
  background: #e5ecf3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);

  .order-c-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 0 10px;
    flex: 1;

    .order-c-icon {
      margin: 0;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 44px;
      height: 44px;
      background: var(--snjy-font-color-primary);
      border-radius: 50px;
      box-shadow: 0 1px 3px rgba(97, 134, 177, 0.1882352941);
    }
  }
}

.order-c-box.address-box {
  flex: 0 0 42% !important;
}

.order-c-details {
  margin: 0;
  padding: 6px 0 0 0;

  h4 {
    margin: 0;
    padding: 0;
    font-size: var(--snjy-font-size-0-8);
    text-transform: uppercase;
    font-weight: var(--snjy-font-weight-bold);
    color: $color_1;
    line-height: 13px;
  }

  small {
    margin: 0;
    padding: 0;
    font-size: var(--snjy-font-size-0-8);
    text-transform: uppercase;
    font-weight: var(--snjy-font-weight-medium);
    color: $color_2;
    line-height: 20px;
  }
}

.order-status-form {
  margin: 15px 0 0 0;
  padding: 30px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);

  h3 {
    margin: 0 0 15px 0;
    padding: 0;
    position: relative;
    font-size: var(--snjy-font-size-1-125);
    font-weight: var(--snjy-font-weight-bold);
    color: $color_3;
    line-height: 20px;
  }

  >p {
    margin: 0 0 25px 0;
    padding: 0;
    position: relative;
    font-size: var(--snjy-font-size-0-9);
    font-weight: var(--snjy-font-weight-semi-bold);
    color: $color_4;
    line-height: 20px;
  }

  .form {
    display: flex;
    justify-content: space-between;
    gap: 0 2%;
    flex-wrap: wrap;

    .form-group {
      -webkit-box-flex: 0;
      flex: 1;
      margin: 0 0 15px 0;
      position: relative;

      &:before {
        position: absolute;
        content: "";
        font-family: $font-family_1;
        top: 39px;
        right: 10px;
        bottom: 0;
        margin: auto 0;
        font-size: var(--snjy-font-size-1-25);
        color: $color_5;
        font-weight: var(--snjy-font-weight-normal);
        line-height: 23px;
        position: absolute;
        content: "";
        font-family: $font-family_1;
        top: 39px;
        right: 10px;
        bottom: 0;
        margin: auto 0;
        font-size: var(--snjy-font-size-1-25);
        color: $color_8;
        font-weight: var(--snjy-font-weight-normal);
        line-height: 23px;
      }

      label {
        margin: 0 0 8px 0;
        padding: 0;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        color: $color_5;
        line-height: 14px;
        display: flex;
        align-items: center;
        gap: 0 2px;

        .material-icons-outlined {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-1-25);
          color: $color_6;
          width: fit-content;
          height: fit-content;
        }
      }

      .form-control {
        margin: 0;
        padding: 0 11px;
        height: 44px;
        background: #f0f7ff;
        border-radius: 8px;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        border: 1px solid #8699a961;

        &::placeholder {
          color: $color_7;
          font-weight: var(--snjy-font-weight-semi-bold);
        }
      }

      textarea.form-control {
        height: 120px;
      }

      select.form-control {
        -webkit-appearance: none;
      }
    }

    .form-group.o-calendar {
      &:before {
        content: "\ebcc";
      }
    }

    .form-group.p-o-num {
      &:before {
        content: "\f0c5";
        top: 40px;
      }
    }

    .form-group.o-num {
      &:before {
        content: "\f045";
        top: 40px;
      }
    }

    .form-group.o-status {
      &:before {
        content: "\e5cf";
        color: $color_1;
        font-size: 26px;
        line-height: 23px;
        height: -moz-fit-content;
        height: fit-content;
        top: 28px;
      }
    }

    .form-btn-sec {
      margin: 15px 0 0 0;
      flex: 0 0 100%;
      max-width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
      gap: 0 2%;
    }
  }
}

.reg-a-p-form {
  .form-group {
    flex: 0 0 32% !important;
  }
}

.warranty-form {
  form {
    flex: 1 !important;
  }
}

.f-full-width {
  flex: 0 0 100% !important;
}

.order-s-btn {
  border-radius: 10px;
  display: flex;
  font-size: var(--snjy-font-size-0-875);
  height: 48px;
  align-items: center;
  justify-content: center;
  line-height: 14px;
  min-width: 263px;
  padding-bottom: 0;
  padding-top: 0;
  cursor: pointer;
  color: $color_9;
  background: var(--snjy-button-gradient);
  position: relative;
  text-transform: uppercase;
  font-weight: var(--snjy-font-weight-medium);
  transition: all0 0.3s ease-in-out;
  border: none;
}

.order-s-btn.back-btn {
  background: linear-gradient(45deg, #bbbbbb, #afafaf);
}


/*--------------ORDER STATUS SEC------------*/

.file-input {
  display: none;
}

.red {
  color: red;
}

.upload-file-link {
  border-radius: 8px;
  margin: 0 !important;
  padding: 0 40px;
  min-height: 44px;
  font-family: var(--snjy-font-family);
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 0 10px !important;
  font-size: var(--snjy-font-size-0-875);
  line-height: 16px;
  font-weight: var(--snjy-font-weight-medium) !important;
  background-color: var(--snjy-font-color-primary) !important;
  color: var(--snjy-button-color-primary) !important;
  border: 1px solid var(--snjy-button-color-primary) !important;
  width: 100%;
  flex-direction: row !important;
}

.file-name-container {
  width: 100%;
  max-width: 300px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .text-truncate {
    width: 100%;
    display: inline-block;
  }
}

@media only screen and (max-width: 1024px) {
.reg-a-p-form .form-group {
  flex: 0 0 48% !important;
}
}
@media only screen and (max-width: 576px) {
.reg-a-p-form .form-group {
  flex: 0 0 100% !important;
}
}











