<section class="return-order-list-page">
  <div class="mx-4 px-3">
    <div class="row">
      <div class="col all-main-title-sec">
        <h1>Return Order Status</h1>
        <nav class="d-flex justify-content-center all-bedcrumbs" aria-label="breadcrumb">
          <ul class="breadcrumb">
            <li class="breadcrumb-item">
              <a class="d-flex align-center" [routerLink]="['/store/dashboard']">
                <span class="material-icons-outlined">home</span> Home
              </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
              Return Order Status
            </li>
          </ul>
        </nav>
      </div>
    </div>
    <div class="row customer-info">
      <div class="col-3">
        <div class="d-flex gap-2">
          <div class="d-flex align-items-center justify-content-center c-icon">
            <img src="/assets/images/seller-icon.png" alt="" title="" />
          </div>
          <div class="c-details">
            <h4 class="text-uppercase">Customer #</h4>
            <small class="text-uppercase">{{
              sellerDetails.bp_customer_number
              }}</small>
          </div>
        </div>
      </div>
      <div class="col-3">
        <div class="d-flex gap-2">
          <div class="d-flex align-items-center justify-content-center c-icon">
            <img src="/assets/images/phone-icon.png" alt="" title="" />
          </div>
          <div class="c-details">
            <h4 class="text-uppercase">Customer Name</h4>
            <small class="text-uppercase">{{ sellerDetails.name }}</small>
          </div>
        </div>
      </div>
      <div class="col-6">
        <div class="d-flex gap-2">
          <div class="d-flex align-items-center justify-content-center c-icon">
            <img src="/assets/images/address-icon.png" alt="" title="" />
          </div>
          <div class="c-details">
            <h4 class="text-uppercase">ADDRESS</h4>
            <small class="text-uppercase">{{ sellerDetails.address }}</small>
          </div>
        </div>
      </div>
    </div>
    <div class="order-status-form all-form-res mt-4">
      <form [formGroup]="filterForm" class="form">
        <div class="form-group">
          <label class="d-flex gap-1">
            <span class="material-icons-outlined">calendar_month</span>
            Date From
          </label>
          <div class="input-group mt-2">
            <input class="form-control" name="picker1" formControlName="DOCUMENT_DATE" ngbDatepicker #d="ngbDatepicker"
              [maxDate]="today()" />
            <button class="btn btn-outline-secondary d-flex align-items-center" (click)="d.toggle()" type="button">
              <i class="material-icons-outlined"> calendar_month </i>
            </button>
          </div>
        </div>
        <div class="form-group">
          <label class="d-flex gap-1">
            <span class="material-icons-outlined">calendar_month</span>
            Date To
          </label>
          <div class="input-group mt-2">
            <input class="form-control" name="picker2" formControlName="DOCUMENT_DATE_TO" ngbDatepicker
              #d1="ngbDatepicker" [minDate]="f.DOCUMENT_DATE.value" [maxDate]="today()" />
            <button class="btn btn-outline-secondary d-flex align-items-center" (click)="d1.toggle()" type="button">
              <i class="material-icons-outlined"> calendar_month </i>
            </button>
          </div>
        </div>
        <div class="form-group">
          <label class="d-flex gap-1">
            <span class="material-icons-outlined">feed</span>
            Status
          </label>
          <select class="form-control select-arrow-down mt-2" formControlName="DOC_STATUS">
            <option *ngFor="let status of statuses" [value]="status.code">
              {{ status.description }}
            </option>
          </select>
        </div>
        <div class="form-group">
          <label class="d-flex gap-1">
            <span class="material-icons-outlined">pin</span>
            Return Order #
          </label>
          <input type="input" class="form-control mt-2" placeholder="Return Order #" (keyup.enter)="getAllReturnOrder()"
            formControlName="SD_DOC" />
        </div>
        <p class="text-danger search-error" *ngIf="submitted && filterForm.touched && filterForm.invalid">
          Please add a valid from and to date
        </p>
        <div class="form-btn-sec d-flex gap-3 justify-content-center flex-wrap">
          <button type="button" class="order-s-btn" (click)="clear()">
            Clear
          </button>
          <button type="button" class="order-s-btn" (click)="search()"
            [disabled]="loading">
            {{ loading ? "Searching..." : "Search" }}
          </button>
        </div>
      </form>
    </div>
    <div class="row my-4">
      <div class="col px-0">
        <div class="custom-table p-4">
          <app-grid [columns]="columnDefs" (rowClick)="goToRetrunOrder($event)" [data]="returnOrders"
            [showExport]="true" (exportClick)="exportToExcel()" *ngIf="!loading && returnOrders.length"></app-grid>
          <div class="w-100" *ngIf="loading || !returnOrders.length">{{ loading ? 'Loading...' : 'No records found.' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>