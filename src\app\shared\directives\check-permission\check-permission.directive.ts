import { Directive, Input, TemplateRef, ViewContainerRef } from "@angular/core";
import { AuthService } from "src/app/core/authentication/auth.service";

@Directive({
  selector: "[checkPermission]",
})
export class CheckPermissionDirective {
  private hasView = false;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private auth: AuthService
  ) {}

  @Input() set checkPermission(permission: string | any) {
    let permissions = this.auth.getPermissions;
    if (permission && !this.hasView) {
      if (permissions.some((p) => permission.includes(p))) {
        this.viewContainer.createEmbeddedView(this.templateRef);
        this.hasView = true;
      } else {
        this.viewContainer.clear();
        this.hasView = false;
      }
    } else if (!permission && !this.hasView) {
      this.viewContainer.createEmbeddedView(this.templateRef);
      this.hasView = true;
    }
  }
}
