import { NgModule } from "@angular/core";
import { Injectable } from "@angular/core";
import { Routes, RouterModule, Resolve } from "@angular/router";
import { AuthGuard } from "../core/authentication/auth.guard";
import { DashboardComponent } from "./backoffice/dashboard/dashboard.component";
import { BackOfficeLayoutComponent } from "./backoffice/layout/backoffice-layout.component";
import { CustomerStoreLayoutComponent } from "./customer-store/customer-store-layout/customer-store-layout.component";
import { CartService } from "./customer-store/services/cart.service";
import { SelectCustomerAuthGuard } from "./customer-store/guard/select.customer.auth.guard";
import { TurnOnFeaturesGuard } from "./customer-store/guard/fetures.guard";

@Injectable({
  providedIn: "root",
})
export class RouteResolver implements Resolve<any> {
  //inject the api service we initially implemented
  constructor(private cartService: CartService) {}

  //resolve cart data
  async resolve() {
    return await this.cartService.getCartByID();
  }
}

const routes: Routes = [
  {
    path: "backoffice",
    component: BackOfficeLayoutComponent,
    canActivate: [AuthGuard],
    canActivateChild: [AuthGuard],
    children: [
      { path: "", redirectTo: "dashboard", pathMatch: "full" },
      { path: "dashboard", component: DashboardComponent },
      {
        path: "data",
        loadChildren: () =>
          import("./backoffice/backoffice.module").then(
            (m) => m.BackofficeModule
          ),
      },
    ],
    data: { permission: "P0027" },
  },
  {
    path: "store",
    canActivate: [AuthGuard, SelectCustomerAuthGuard],
    canActivateChild: [TurnOnFeaturesGuard, AuthGuard, SelectCustomerAuthGuard],
    component: CustomerStoreLayoutComponent,
    loadChildren: () =>
      import("./customer-store/customer-store.module").then(
        (m) => m.CustomerStoreModule
      ),
    resolve: {
      routeResolver: RouteResolver,
    },
    data: { permission: "P0001" },
  },
  {
    path: "auth",
    loadChildren: () =>
      import("./session/session.module").then((mod) => mod.SessionModule),
  },
  { path: "", pathMatch: "full", redirectTo: "backoffice" },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      useHash: true,
    }),
  ],
  exports: [RouterModule],
})
export class RoutesRoutingModule {}
