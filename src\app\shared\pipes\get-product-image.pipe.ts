import { Pipe, PipeTransform } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { lastValueFrom, map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Pipe({
  name: "getProductImage",
})
export class GetProductImagePipe implements PipeTransform {
  constructor(private http: HttpClient) { }

  async transform(value: unknown, ...args: unknown[]) {
    return await lastValueFrom(
      this.http
        .get<any>(ApiConstant.GET_PRODUCT_IMAGES.replace('{id}', (value || '').toString()))
        .pipe(
          map((res) => {
            const coverImage = res?.data?.find(
              (d: { is_cover_image: string }) => d.is_cover_image
            );
            if (coverImage) {
              return coverImage.url;
            } else {
              const imageData = res?.data?.find(
                (d: { dimension: string }) => d.dimension === "1200X1200"
              );
              if (imageData) {
                return imageData.url;
              } else {
                return "/assets/images/demo-product.png";
              }
            }
          })
        )
    );
  }
}
