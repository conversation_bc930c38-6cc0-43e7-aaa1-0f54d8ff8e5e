import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";

export interface FACETS {
  id?: number;
  name: string;
  visible?: boolean;
  showMore?: boolean;
  values?: [FACETS];
  product_count?: number;
  selected?: boolean;
}

@Component({
  selector: "app-facet",
  templateUrl: "./facet.component.html",
  styleUrls: ["./facet.component.scss"],
})
export class FacetComponent implements OnInit, OnChanges {
  facets: FACETS[] = [];
  @Input() categories!: Omit<FACETS, "values">[];
  maxShow = 5;
  params: string[] = [];

  constructor(public router: Router, private activatedRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe({
      next: (params) => {
        this.params = params["cat"] ? params["cat"].split(",") : [];
      },
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.categories) {
      this.facets = this.updateVisibility(this.createFactes(this.categories));
    }
  }

  filterValues(values: any) {
    const arr = values || [];
    return arr.filter((val: any) => val.visible);
  }

  updateVisibility(data: any): any {
    return data.map((category: any) => {
      if (category.values && category.values.length > 0) {
        category.values = this.updateVisibility(category.values);
        const hasVisibleChild = category.values.some(
          (child: any) => child.visible
        );
        category.visible = hasVisibleChild;
      } else {
        category.visible = category.product_count > 0;
      }
      return category;
    }).filter((category: any)=> category.visible);
  }

  createFactes(items: any, id = null, link = "parent_category_id") {
    return items
      .filter((item: { [x: string]: null }) => item[link] === id)
      .map((item: { id: any; name: string }) => ({
        ...item,
        showMore: false,
        selected: this.params.includes(item.name),
        values: this.createFactes(items, item.id),
      }));
  }

  select(event: any) {
    event.selected = !event.selected;
    if (event.selected) {
      const cat = this.categories.find((cat) => cat.name === event.name);
      if (cat) this.params.push(cat.name);
    } else {
      this.params = this.params.filter((item) => item !== event.name);
    }

    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { cat: this.params.length ? this.params.join(",") : null },
      queryParamsHandling: "merge",
    });
  }
}
