:host {
    padding: 25px;
}

.all-main-title-sec.cart-details-main-title {
    align-items: flex-start;
    justify-content: flex-start;
    text-align: left;
    padding: 0 0 25px 0;

    h1 {
        text-align: left;
        padding: 0;
        display: flex;
        align-items: center;
        gap: 0 10px;
        justify-content: space-between;
    }
}

.secure-checkout-sec {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
    gap: 0 3%;

    .items-container {
        margin: 0;
        padding: 0;
        flex: 4;

        .progress-container {
            max-width: 950px;
            width: 100%;
            margin: 20px 0 0 0;
            gap: 0 20px;

            >div {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                gap: 0 10px;
                position: relative;
                flex: 1;

                .timeline {
                    flex: 1;
                    border: 1px dashed #a0abad;
                }

                .circle {
                    height: 40px;
                    width: 40px;
                    border-radius: 6px;
                    background-color: var(--snjy-font-color-primary);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1;
                    font-size: var(--snjy-font-size-0-875);
                    font-weight: var(--snjy-font-weight-bold);
                    border: 1px solid #dfdfdf;
                    box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);
                }

                .text {
                    font-weight: var(--snjy-font-weight-medium);
                    font-size: var(--snjy-font-size-0-8);
                    color: var(--snjy-font-color-seconday);
                    text-align: center;
                }

                &.selected {
                    .circle {
                        background-color: var(--snjy-button-color-primary);
                        color: var(--snjy-color-white);
                        border: 1px solid var(--snjy-button-color-primary);
                    }

                    .text {
                        color: var(--snjy-button-color-primary);
                    }

                    .timeline {
                        border: 1px dashed var(--snjy-button-color-primary);
                    }
                }
            }
        }

        .steps-container {
            margin: 30px 0 0 0;
            padding: 30px;
            background: var(--snjy-color-white);
            border-radius: 12px;
            border: 1px solid rgba(208, 215, 216, 0.3882352941);
            box-shadow: var(--snjy-box-shadow);

            .form-group {
                margin: 0 0 15px 0;
                padding: 0;
                position: relative;

                &.payment-type {
                    display: flex;
                    align-items: center;
                    gap: 0 9px;
                    margin: 0 0 25px 0;

                    input {
                        margin: 0 !important;
                    }

                    label {
                        margin: 0 !important;
                    }
                }

                .form-label {
                    margin: 0 0 7px 0;
                    padding: 0;
                    font-size: var(--snjy-font-size-0-875);
                    font-weight: var(--snjy-font-weight-medium);
                    color: var(--snjy-button-color-primary);
                    line-height: 14px;
                    display: flex;
                    align-items: center;
                    gap: 0 2px;

                    .material-icons-outlined {
                        margin: 0;
                        padding: 0;
                        font-size: var(--snjy-font-size-1-25);
                        color: #b3b3b3;
                        width: -moz-fit-content;
                        width: fit-content;
                        height: -moz-fit-content;
                        height: fit-content;
                    }
                }
            }

            select,
            input.form-control {
                margin: 0;
                padding: 0 11px;
                height: 44px;
                background: #f0f7ff;
                font-size: var(--snjy-font-size-0-875);
                font-weight: var(--snjy-font-weight-normal) !important;
                border: 1px solid rgba(134, 153, 169, 0.3803921569);
            }

            .btn:not(.btn-outline-secondary) {
                border-radius: 10px;
                display: flex;
                font-size: var(--snjy-font-size-0-875);
                height: 44px;
                align-items: center;
                justify-content: center;
                line-height: 14px;
                min-width: 180px;
                padding-bottom: 0;
                padding-top: 0;
                cursor: pointer;
                color: var(--snjy-color-white);
                background: var(--snjy-button-gradient);
                position: relative;
                text-transform: uppercase;
                font-weight: var(--snjy-font-weight-medium) !important;
                transition: all0 0.3s ease-in-out;
                border: none;

                &.light {
                    background: #1a1a1a;
                    color: var(--snjy-color-white);
                }
            }

            .address-container {

                .py-4 {
                    border-bottom: 1px solid #efefef;
                }

                h3 {
                    margin: 0 0 0 0;
                    padding: 0;
                    position: relative;
                    font-size: var(--snjy-font-size-1-125);
                    font-weight: var(--snjy-font-weight-bold);
                    color: #00216c;
                    line-height: 20px;
                }

                small {
                    margin: 0 0 5px 0;
                    padding: 0;
                    position: relative;
                    font-size: var(--snjy-font-size-0-8);
                    line-height: 20px;
                    color: var(--snjy-color-text-ternary);
                    font-weight: var(--snjy-font-weight-normal);
                }

                .form-check-label {
                    h4 {
                        margin: 0 0 4px 0;
                        padding: 0;
                        position: relative;
                        font-size: var(--snjy-font-size-0-9);
                        font-weight: var(--snjy-font-weight-bold);
                        color: var(--snjy-button-color-primary);
                        line-height: 17px;
                    }

                    p {
                        margin: 0;
                        padding: 0;
                        position: relative;
                        font-size: var(--snjy-font-size-0-8);
                        line-height: 18px;
                        color: var(--snjy-color-text-ternary);
                        font-weight: var(--snjy-font-weight-normal);
                    }
                }

            }

            .shipping-address-container {
                max-width: 390px;
                margin: 0 0 80px 0;
                background: var(--snjy-color-white);
                display: flex;
                align-items: flex-start;
                justify-content: flex-start;
                gap: 0 10px;

                .material-icons-outlined {
                    margin: 0;
                    padding: 0;
                    min-width: 50px !important;
                    height: 50px !important;
                    display: inline-flex !important;
                    align-items: center;
                    justify-content: center;
                    border-radius: 9px;
                    background: #ffc55c;
                }

                .shipping-address-box {
                    h3 {
                        margin: 0 0 0 0;
                        padding: 0;
                        position: relative;
                        font-size: var(--snjy-font-size-1);
                        font-weight: var(--snjy-font-weight-bold);
                        color: #00216c;
                        line-height: 20px;
                    }

                    small {
                        margin: 7px 0 0 0;
                        padding: 0;
                        position: relative;
                        font-size: var(--snjy-font-size-0-8);
                        line-height: 20px;
                        color: var(--snjy-color-text-ternary);
                        font-weight: var(--snjy-font-weight-normal);
                        display: block;
                    }
                }
            }
        }

        .item-details {
            margin: 20px 0 20px 0;
            padding: 24px;
            background: var(--snjy-color-white);
            border: 1px solid var(--snjy-border-color-secondary);
            border-radius: 10px;
            box-shadow: var(--snjy-box-shadow);

            h3 {
                margin: 0 0 15px 0;
                padding: 0;
                position: relative;
                font-size: var(--snjy-font-size-1-125);
                font-weight: var(--snjy-font-weight-bold);
                color: #00216c;
                line-height: 20px;
            }

            .item-box {
                margin: 0 0 18px 0;
                padding: 16px;
                background: #eff4f5;
                display: flex;
                justify-content: flex-start;
                gap: 0 3%;
                position: relative;
                border-radius: 7px;

                .item-box-img {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    flex: 0 0 26%;
                    height: 144px;
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-position: center;
                    border-radius: 7px;
                    overflow: hidden;
                    border: 1px solid rgba(27, 125, 203, 0.29);
                }

                .item-box-content {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    width: 100%;

                    h4 {
                        margin: 15px 0 0 0;
                        padding: 0;
                        position: relative;
                        font-size: var(--snjy-font-size-1);
                        font-weight: var(--snjy-font-weight-medium);
                        color: var(--snjy-color-dark-secondary);
                        line-height: 20px;
                        display: block;
                    }

                    small {
                        margin: 0;
                        padding: 0;
                        font-size: var(--snjy-font-size-0-75);
                        color: #687491;
                        font-weight: var(--snjy-font-weight-medium);
                    }

                    .item-box-bottom-content {
                        margin: 35px 0 0 0;
                        padding: 0;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .quantity {
                            margin: 0;
                            padding: 0 0 0 7px;
                            position: relative;
                            background: var(--snjy-color-white);
                            height: 38px;
                            width: 148px;
                            display: inline-flex;
                            align-items: center;
                            justify-content: space-between;
                            border-radius: 5px;
                            border: 1px solid #ffd383;
                            font-size: var(--snjy-font-size-0-8);
                            font-weight: var(--snjy-font-weight-medium);
                            overflow: hidden;
                            color: var(--snjy-color-dark-secondary);

                            span {
                                margin: 0;
                                padding: 0;
                                width: 40px;
                                height: 38px;
                                background: #ffd383;
                                display: inline-flex;
                                align-items: center;
                                justify-content: center;
                                color: var(--snjy-color-dark-secondary);
                            }
                        }

                        .item-price {
                            margin: 0;
                            padding: 0;
                            position: relative;
                            font-size: var(--snjy-font-size-2);
                            font-weight: var(--snjy-font-weight-bold);
                            color: var(--snjy-color-dark-secondary);
                            line-height: 22px;
                            text-align: right;

                            span {
                                margin: 0;
                                padding: 0;
                                position: relative;
                                font-size: var(--snjy-font-size-0-75);
                                font-weight: var(--snjy-font-weight-medium);
                                color: #687491;
                                display: block;
                            }
                        }

                    }
                }
            }

        }
    }
}


.checkout-id-summary {
    margin: 90px 0 20px 0;
    flex: 2;

    .checkout-id-summary-body {
        margin: 0 0 20px 0;
        padding: 24px;
        background: var(--snjy-color-white);
        border: 1px solid var(--snjy-border-color-secondary);
        border-radius: 10px;
        box-shadow: var(--snjy-box-shadow);
        position: relative;
        height: fit-content;

        h3 {
            margin: 0 0 3px 0;
            padding: 0;
            position: relative;
            font-size: var(--snjy-font-size-1-125);
            font-weight: var(--snjy-font-weight-bold);
            color: #00216c;
            line-height: 20px;
        }

        small {
            margin: 0 0 15px 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-8);
            font-weight: var(--snjy-font-weight-semi-bold);
            color: #878787;
            display: block;

            .material-icons-outlined {
                margin: 0;
                padding: 0;
                font-size: var(--snjy-font-size-1);
                width: fit-content;
                height: fit-content;
                color: var(--snjy-button-color-primary);
            }
        }

        .item-box {
            margin: 0 0 10px 0;
            padding: 0 10px 0 0;
            display: flex;
            justify-content: flex-start;
            gap: 0 3%;
            position: relative;
            border-radius: 8px;
            border: 1px solid #e3e3e3;
            overflow: hidden;

            .item-box-img {
                margin: 0;
                padding: 0;
                position: relative;
                flex: 0 0 30%;
                height: 100px;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }

            .item-box-content {
                margin: 0;
                padding: 0;
                position: relative;
                width: 100%;

                h4 {
                    margin: 5px 0 0 0;
                    padding: 0;
                    position: relative;
                    font-size: var(--snjy-font-size-0-875);
                    font-weight: var(--snjy-font-weight-medium);
                    color: var(--snjy-color-dark-secondary);
                    line-height: 20px;
                    display: block;
                    max-width: 180px;
                    text-wrap: balance;
                }

                .item-box-list {
                    display: flex;
                    gap: 0 15px;
                    margin: 2px 0 2px 0;
                    flex-direction: column;
                }

                small {
                    margin: 0;
                    padding: 0;
                    font-size: 10px;
                    color: #687491;
                    font-weight: var(--snjy-font-weight-medium);

                    span {
                        color: var(--snjy-button-color-primary);
                    }
                }

                .quantity {
                    margin: 0;
                    padding: 0 0 0 4px;
                    position: absolute;
                    background: var(--snjy-color-white);
                    display: inline-flex;
                    align-items: center;
                    justify-content: space-between;
                    border-radius: 4px;
                    border: 1px solid #ffd383;
                    font-size: 11px;
                    font-weight: var(--snjy-font-weight-medium);
                    overflow: hidden;
                    gap: 0 8px;
                    right: 0;
                    bottom: 7px;
                    color: var(--snjy-color-dark-secondary);

                    span {
                        margin: 0;
                        padding: 0 3px;
                        width: fit-content;
                        height: 18px;
                        background: #ffd383;
                        display: inline-flex;
                        align-items: center;
                        justify-content: center;
                        color: var(--snjy-color-dark-secondary);
                    }
                }

                .item-box-bottom-content {
                    margin: 8px 0 0 0;
                    padding: 0;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .unit {
                        margin: 0;
                        padding: 0;
                        position: relative;
                        font-size: 10px;
                        font-weight: var(--snjy-font-weight-medium);
                        color: #919191;
                        line-height: 13px;

                        span {
                            display: block;
                        }
                    }
                }

                .item-price {
                    margin: 0;
                    padding: 0;
                    position: absolute;
                    font-size: var(--snjy-font-size-0-8);
                    font-weight: var(--snjy-font-weight-bold);
                    color: #0086f9;
                    line-height: 22px;
                    text-align: right;
                    top: 5px;
                    right: 0;
                }
            }
        }



        .checkout-summary-price {
            margin: 20px 0 0 0;

            ul {
                margin: 15px 0 15px 0;
                padding: 7px 18px;
                list-style: none;
                border-radius: 8px;
                background: #f0f5f6;

                li {
                    margin: 10px 0;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 0 10px;
                    color: #687491;
                    font-weight: var(--snjy-font-weight-medium);
                    font-size: var(--snjy-font-size-0-8);

                    span {
                        color: #0077d7;
                    }
                }

                .total-price {
                    margin: 5px 0 !important;
                    font-size: 22px;
                    color: var(--snjy-color-dark-secondary);

                    span {
                        font-size: var(--snjy-font-size-1-25);
                        color: var(--snjy-color-dark-secondary);
                        font-weight: var(--snjy-font-weight-bold);
                    }
                }

            }

            button {
                width: 100%;
                border-radius: 8px;
                margin-bottom: 0.8rem;
                padding: 0;
                min-height: 50px;
                font-family: var(--snjy-font-family);
                background-color: var(--snjy-button-color-primary);
                color: var(--snjy-color-white);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0 5px;
                font-size: var(--snjy-font-size-0-875);
                line-height: 16px;
                font-weight: var(--snjy-font-weight-medium);

                &.btn-light {
                    background-color: var(--snjy-font-color-primary);
                    color: var(--snjy-button-color-primary);
                    border: 1px solid var(--snjy-button-color-primary);
                }
            }

            p.terms {
                margin: 24px 0 0 0;
                padding: 0 0 0 13px;
                font-size: var(--snjy-font-size-0-8);
                color: #687491;
                font-weight: var(--snjy-font-weight-medium);
                line-height: 20px;
                a {
                    font-weight: bold;
                    color: var(--snjy-button-color-primary);
                }
            }
        }
    }
}

@media (max-width: 991px) {
    .secure-checkout-sec {
        flex-wrap: wrap;
    }

    .items-container {
        flex: 0 0 100% !important;
    }

    .checkout-id-summary {
        margin: 25px 0 !important;
    }

    .progress-container {
        >div {
            flex-direction: column;
            gap: 5px !important;

            .timeline {
                position: absolute;
                right: -35px;
                width: 50px;
                top: 20px;
            }
        }
    }

    .secure-checkout-sec .items-container .steps-container {
        margin: 20px 0 0 0 !important;
    }
}

@media (max-width: 480px) {
    .btn-container .btn {
        width: 100%;
    }

    .desc {
        text-align: center;
        justify-content: center !important;
    }

    :host {
        padding: 16px;
    }

    .secure-checkout-sec .items-container .steps-container {
        padding: 15px !important;
    }

    .checkout-id-summary {
        .checkout-id-summary-body {
            .item-box {
                flex-direction: column !important;
                padding: 10px !important;

                .item-box-img {
                    min-height: 100px !important;
                }

                .item-box-content {
                    h4 {
                        max-width: 100% !important;
                        text-align: left !important;
                    }

                    .quantity {
                        position: relative !important;
                        bottom: 0 !important;
                    }

                    .item-price {
                        position: relative !important;
                        top: 0 !important;
                        margin: -22px 0 0 0 !important;
                    }
                }
            }
        }

    }

}