import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  CanActivate,
  CanActivateChild,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from "@angular/router";
import { Location } from "@angular/common";
import { AuthService } from "./auth.service";
import { RolesType } from "src/app/constants/api.constants";
import { AppToastService } from "src/app/shared/services/toast.service";

@Injectable({
  providedIn: "root",
})
export class AuthGuard implements CanActivate, CanActivateChild {
  constructor(
    private router: Router,
    private location: Location,
    private auth: AuthService,
    private _snackBar: AppToastService
  ) {}

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree | any> {
    return await this.authenticate(route, state.url, false);
  }

  async canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree | any> {
    return await this.authenticate(childRoute, state.url, true);
  }

  private async authenticate(
    route: ActivatedRouteSnapshot,
    url: string,
    is_child_route: boolean
  ): Promise<boolean | UrlTree | any> {
    if (this.auth.isLoggedIn) {
      // If User already login and try to access auth pages.
      if (url.startsWith("/auth")) {
        return this.checkRoleType();
      }
      const routeData: any = route?.data || null;
      const permission: any = routeData?.permission || null;
      let permissions = this.auth.getPermissions;
      if (!permissions.length) {
        permissions = await this.auth.getUserPermissions();
        // If user don't have access permissions for backoffice and store then redirect on login page.
        if (!permissions.length) {
          this.errMsg();
          this.auth.resetAuth();
          return this.router.parseUrl("/auth/login");
        }
      }

      if (
        permission !== null &&
        !permissions.some((p) => permission.includes(p))
      ) {
        this.errMsg();
        // If user don't have access permission for child route then redirect to default page
        if (is_child_route) {
          // Check when user click on detail page view link and don't have access permissions
          if (url && url !== this.location.path()) {
            return this.router.parseUrl(this.location.path());
          } else {
            if (url.startsWith("/backoffice")) {
              return this.router.parseUrl("/backoffice");
            } else if (url.startsWith("/store")) {
              return this.router.parseUrl("/store");
            }
          }
        } else {
          // If don't have backoffice permission then redirect to store
          if (url.startsWith("/backoffice")) {
            return this.router.parseUrl("/store");
          } else if (url.startsWith("/store")) {
            // If don't have store permission then redirect to backoffice
            return this.router.parseUrl("/backoffice");
          }
        }
      } else {
        if (this.auth.role === RolesType.CUST_SERVICE) {
          if (url.startsWith("/store/dashboard")) {
            return this.router.parseUrl("/store/customer-services");
          }
        } else if (this.auth.role === RolesType.SALES) {
          if (url.startsWith("/store/dashboard")) {
            return this.router.parseUrl("/store/sales");
          }
        } else if (this.auth.role === RolesType.VENDOR) {
          if (url.startsWith("/store/dashboard")) {
            return this.router.parseUrl("/store/vendor");
          }
        }
      }

      return true;
    } else {
      if (!url.startsWith("/auth")) {
        return this.router.parseUrl("/auth/login");
      }
    }

    return true;
  }

  private checkRoleType() {
    switch (this.auth.role) {
      case RolesType.STOREFRONT:
        return this.router.parseUrl("/store");
      case RolesType.CUST_SERVICE:
        return this.router.parseUrl("/store/customer-services");
      case RolesType.SALES:
        return this.router.parseUrl("/store/sales");
      case RolesType.VENDOR:
          return this.router.parseUrl("/store/vendor");
      default:
        return this.router.parseUrl("/backoffice");
    }
  }

  private errMsg() {
    this._snackBar.open(
      "Your don't have access permissions. Please contact snjya support team for assistance.",
      {
        type: "Error",
      }
    );
  }
}
