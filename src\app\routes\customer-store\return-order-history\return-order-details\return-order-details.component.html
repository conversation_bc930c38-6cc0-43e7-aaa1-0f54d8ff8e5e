<ng-container *ngIf="!loading">
  <div class="all-main-title-sec product-details-main-title">
    <h1>Return Order ID: {{ returnOrderDetail?.RETURN_ORDER_HDR?.DOC_NUMBER }}</h1>
    <div class="all-bedcrumbs">
      <ul>
        <li>
          <a [routerLink]="['/store/dashboard']">
            <span class="material-icons-outlined">home</span> Home
          </a>
        </li>
        <li>
          <a [routerLink]="['/store/return-order-history']">
            <span class="material-icons-outlined">list_alt</span> Return Orders
          </a>
        </li>
        <li>Return Order Details</li>
      </ul>
    </div>
  </div>
  <div class="return-id-sec">
    <div class="return-id-body">
      <div class="return-id-info">
        <div class="order-details">
          <h3>Return Details</h3>
          <ul>
            <li>
              <span class="material-icons-outlined">pin</span> Return Order #
              <span>{{ returnOrderDetail?.RETURN_ORDER_HDR?.DOC_NUMBER }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">
                person_outline
              </span>
              Customer Name <span>{{ sellerDetails?.name }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">pin</span> Customer
              # <span>{{ sellerDetails?.bp_customer_number }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">pin</span> Ref. Sales Order #
              <span>{{ returnOrderDetail?.RETURN_ORDER_HDR?.REF_ORDER }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">event_note</span>
              Date Placed
              <span>{{
                moment(returnOrderDetail?.RETURN_ORDER_HDR?.DOC_DATE).format("MM/DD/YYYY")
                }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">motion_photos_on</span>
              Status
              <span>{{ getStatusName(returnOrderDetail?.RETURN_ORDER_HDR?.DOC_STAT) }}</span>
            </li>
          </ul>
        </div>
        <div class="order-details">
          <h3>Items</h3>
          <div class="item-box" *ngFor="let item of returnOrderDetail.RETURN_ORDER_LINE_DETAIL">
            <div class="item-box-img" [ngStyle]="{
                  'background-image':
                    'url(' + (item.MATERIAL | getProductImage | async) + ')'
                }"></div>
            <div class="item-box-content">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h4>{{ item.SHORT_TEXT }}</h4>
                  <small>{{ item.MATERIAL }}</small>
                </div>
                <div class="item-price">
                  {{
                  item?.NET_AMOUNT | currency : item?.TXN_CURRENCY
                  }}
                </div>
              </div>
              <div class="item-box-bottom-content">
                <div class="return-reason flex-wrap gap-3 w-100">
                  <div class="return-r-dropdown">
                    <label>Return Reason</label>
                    <select class="form-control select-arrow-down" disabled [(ngModel)]="item.RETURN_REASON">
                      <option value="">Select Return Reason</option>
                      <option *ngFor="let reason of returnReason" [value]="reason.code">
                        {{ reason.description }}
                      </option>
                    </select>
                  </div>
                  <div class="d-flex flex-column return-refund-type">
                    <label class="fw-semibold mb-2">
                      Return Type
                    </label>
                    <div class="initiate-return-list flex-wrap">
                      <label>
                        <input type="radio" class="form-control" value="" disabled [(ngModel)]="item.REFUND_TYPE"/>
                        <span>Return & Refund</span>
                      </label>
                      <label>
                        <input type="radio" class="form-control" value="1" disabled [(ngModel)]="item.REFUND_TYPE"/>
                        <span>Replace Item </span>
                      </label>
                    </div>
                  </div>
                  <div class="">
                    <div class="d-flex justify-content-end">
                      <div class="quantity">
                        <span class="px-2"> Return Quantity: </span>
                        <span class="req-qty px-2">
                          {{ item.REQ_QTY }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>
<app-loader *ngIf="loading"></app-loader>