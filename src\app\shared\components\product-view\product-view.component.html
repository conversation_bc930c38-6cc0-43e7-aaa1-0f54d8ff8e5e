<div class="main-container d-flex flex-column h-100" #productView>
  <div [routerLink]="['/store/product-details', detail.product_id]">
    <div class="img-container m-0 position-relative w-100 d-flex flex-column justify-content-end overflow-hidden"
      [ngStyle]="{'background-image': 'url('+ trustUrl(imgUrl$ | async) +')' }">
      <!-- <img crossorigin="anonymous" [src]="detail.product_id | getProductImage | async" loading="lazy" appImgFallback /> F-->
    </div>
  </div>
  <button type="button" class="pro-btn d-block border-0" (click)="salesPrice && addToCart($event, detail)"
    [disabled]="!salesPrice" [title]="!salesPrice ? 'Sales price: Loading or N/A' : ''" *checkPermission="'P0019'">Add to cart</button>
  <div class="pro-details m-0 flex-grow-1 d-flex flex-column justify-content-between">
    <div [routerLink]="['/store/product-details', detail.product_id]"
      class="pro-details-title d-flex justify-content-between flex-column flex-grow-1">
      <h3 class="mb-2 p-0 position-relative">{{ detail.name ? detail.name: detail.product_desc?
        detail.product_desc.replaceAll('"','') : "" }}</h3>
      <div class="pro-extra-c m-0 p-0 position-relative">SKU ID: {{ detail.product_id }}</div>
    </div>
    <div class="d-flex align-items-center justify-content-between quantity-container">
      <item-counter [max]="maxQuantity" [control]="$any(addToCartForm.get('quantity'))" *checkPermission="'P0019'"></item-counter>
      <div class="p-price m-0 p-0 position-relative">
        <span *ngIf="(salesPrice$ | async) as price; else loading">
          {{price | currency}}
        </span>
      </div>
    </div>
  </div>
</div>
<ng-template #loading>
  <app-loader></app-loader>
</ng-template>