.right-sidebar {
    width: 400px;
    z-index: 99999;

    .link-dark {
        margin: 20px 0 0 0;
        padding: 15px;
        gap: 0 10px;
        border-radius: 11px;
        background-image: url(/assets/images/price-bg.jpg);
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        min-height: 130px;
        width: 100%;

        .shopping-cart-icon {
            height: 65px;
            min-width: 65px !important;
            font-size: 65px;
            color: var(--snjy-font-color-primary);
        }

        small {
            font-size: 16px !important;
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-color-white);

            span {
                font-size: 14px !important;
                text-transform: capitalize;
                font-weight: var(--snjy-font-weight-semi-bold);
                margin: 1px 0 0 0;
            }
        }
    }

    .item-box {
        margin: 0 0 30px 0;
        padding: 0 10px 0 0;
        gap: 0 3%;

        .item-box-img {
            flex: 0 0 30%;
            height: 100px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border: 1px solid #e3e3e3;
            border-radius: 8px;
        }

        .item-box-content {

            h4 {
                max-width: 250px;
                margin: 5px 0 0 0;
                font-size: var(--snjy-font-size-0-875);
                font-weight: var(--snjy-font-weight-medium);
                color: var(--snjy-color-dark-secondary);
                line-height: 20px;
            }

            .item-box-list {
                gap: 0 15px;
                margin: 2px 0 2px 0;
            }

            small {
                font-size: 10px;
                color: #687491;
                font-weight: var(--snjy-font-weight-medium);

                span {
                    color: var(--snjy-button-color-primary);
                }
            }

            .quantity {
                margin: 8px 0;
                padding: 0px 0 0 4px;
                background: var(--snjy-color-white);
                border-radius: 4px;
                border: 1px solid #ffd383;
                font-size: var(--snjy-font-size-0-75);
                font-weight: var(--snjy-font-weight-medium);
                gap: 0 8px;
                color: var(--snjy-color-dark-secondary);
                max-width: 40%;

                span {
                    padding: 0 10px;
                    width: -moz-fit-content;
                    width: fit-content;
                    height: 24px;
                    background: #ffd383;
                    color: var(--snjy-color-dark-secondary);
                }
            }

            .item-price {
                margin: -31px 0 5px 0;
                font-size: var(--snjy-font-size-0-8);
                font-weight: var(--snjy-font-weight-bold);
                color: #0086f9;
                line-height: 22px;
                text-align: right;
            }
        }
    }
}

button {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 0.8rem;
    padding: 0;
    min-height: 50px;
    font-family: var(--snjy-font-family);
    background-color: var(--snjy-button-color-primary) !important;
    color: var(--snjy-color-white) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 5px;
    font-size: var(--snjy-font-size-0-875);
    line-height: 16px;
    font-weight: var(--snjy-font-weight-medium);

    &.btn-light {
        background-color: var(--snjy-font-color-primary) !important;
        color: var(--snjy-button-color-primary) !important;
        border: 1px solid var(--snjy-button-color-primary) !important;
    }
}

@media screen and (max-width: 480px) {
.right-sidebar{width: 85%;}
.right-sidebar .item-box .item-box-content h4 {
    white-space: break-spaces;
}
}










