
import { ColDef } from 'ag-grid-community';
import { Observable } from 'rxjs';

/** Column definition of grid. */
export interface GridColumn extends ColDef {
  hide?: boolean;
  show?: boolean;

}

/** Possible column pin values.  */
export declare type GridColumnPinValue = 'left' | 'right' | null;

/** Column pin option  */
export interface GridColumnPinOption {
  label: string | Observable<string>;
  value: GridColumnPinValue;
}

