<div class="d-flex">
    <ng-content select="[header]"></ng-content>
    <button type="button" (click)="export()" class="export mb-3" *ngIf="showExport">
        <span class="material-icons-outlined me-2">download</span>
        Export
    </button>
</div>
<ag-grid-angular [rowSelection]="'single'" [components]="components" style="width: 100%; height: 730px;"
    class="ag-theme-alpine" [columnDefs]="columns" [defaultColDef]="defaultColDef"
    (selectionChanged)="onSelectionChanged()" (gridReady)="onGridReady($event)" [pagination]="true" [rowData]="data"
    [rowClass]="rowClass" [animateRows]="true" [getRowHeight]="getRowHeight"
    [paginationPageSize]="10" [paginationPageSizeSelector]="[10, 20, 50, 100]"></ag-grid-angular>