import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { NgbDatepickerModule } from "@ng-bootstrap/ng-bootstrap";
import { ReactiveFormsModule } from "@angular/forms";

import { RegisterProductComponent } from "./register-product.component";
import { SharedModule } from "src/app/shared/shared.module";


@NgModule({
  declarations: [RegisterProductComponent],
  imports: [
    CommonModule,
    SharedModule,
    NgbDatepickerModule,
    ReactiveFormsModule,
    RouterModule.forChild([{ path: "", component: RegisterProductComponent }]),
  ],
})
export class RegisterProductModule {}
