import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";

@Component({
  selector: "app-input-radio-btn",
  templateUrl: "./input-radio-btn.component.html",
  styleUrls: ["./input-radio-btn.component.scss"],
})
export class InputRadioBtnComponent implements ICellRendererAngularComp {
  public params: any;
  public label: string;
  public selectedCustomerId: string = "";

  agInit(params: any): void {
    this.params = params;
    if (params?.node?.data?.is_selected) {
      this.selectedCustomerId = params?.node?.data?.customer_id || "";
    }
    this.label = this.params.label || null;
  }

  refresh(params?: any): boolean {
    this.selectedCustomerId = "";
    return true;
  }

  onChange($event: any) {
    if (this.params.onChange instanceof Function) {
      // put anything into params u want pass into parents component
      const params = {
        event: $event,
        rowData: this.params.node.data,
        selectedCustomerId: this.selectedCustomerId,
      };
      this.params.onChange(params);
    }
  }
}
