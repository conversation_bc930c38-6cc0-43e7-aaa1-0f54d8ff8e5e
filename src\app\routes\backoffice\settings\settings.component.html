<div>
    <h2 class="mb-3">Settings</h2>
    <form [formGroup]="form">
        <div class="form-group mb-3">
            <label>Country</label>
            <select formControlName="country" class="form-select mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['country'].errors }">
                <option *ngFor="let country of countries" [value]="country">{{country}}</option>
            </select>
            <div *ngIf="submitted && f['country'].errors" class="invalid-feedback">
                <div *ngIf="submitted && f['country'].errors && f['country'].errors['required']">Country is
                    required
                </div>
            </div>
        </div>
        <div class="form-group mb-3">
            <label>Currency</label>
            <select formControlName="currency" class="form-select mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['currency'].errors }">
                <option *ngFor="let currency of currencies" [value]="currency">{{currency}}</option>
            </select>
            <div *ngIf="submitted && f['country'].errors" class="invalid-feedback">
                <div *ngIf="submitted && f['country'].errors && f['country'].errors['required']">Currency is
                    required
                </div>
            </div>
        </div>
        <div class="form-group mb-3">
            <label>Time Zone</label>
            <select formControlName="timezone" class="form-select mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['timezone'].errors }">
                <option *ngFor="let timezone of timezones" [value]="timezone.abbr">{{timezone.text}}</option>
            </select>
            <div *ngIf="submitted && f['country'].errors" class="invalid-feedback">
                <div *ngIf="submitted && f['country'].errors && f['country'].errors['required']">Time Zone is
                    required
                </div>
            </div>
        </div>
        <div class="form-group mb-3">
            <label>Company Logo</label>
            <input type="text" formControlName="company_logo" class="form-control mt-1" />
        </div>
        <div class="form-group mb-3">
            <label>Company Name</label>
            <input type="text" formControlName="company_name" class="form-control mt-1" />
        </div>
    </form>
    <button type="button" (click)="onSubmit()" [disabled]="saving" class="btn btn-primary">{{ saving ? 'Saving' :
        'Save' }}</button>

</div>