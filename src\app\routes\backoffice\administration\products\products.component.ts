import { Component, OnInit, ViewChild } from "@angular/core";
import { Subject, takeUntil, take } from "rxjs";

import { BukUploadService } from "./buk-upload.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-products",
  templateUrl: "./products.component.html",
  styleUrls: ["./products.component.scss"],
})
export class ProductsComponent implements OnInit {
  private ngUnsubscribe = new Subject<void>();
  public products: Array<any> = [];
  public fileName = "";
  public file: File | undefined;
  public maxFileSize = 300 * 1000;
  public saving: boolean = false;

  @ViewChild("fileInput") fileInput: any;

  constructor(
    private service: BukUploadService,
    private _snackBar: AppToastService
  ) {}

  ngOnInit(): void {
    this.getUploadStatus();
  }

  getUploadStatus() {
    this.service.getStatus().subscribe({
      next: (res) => {
        const inProgress = res.find(
          (file: any) => file.status === "IN_PROGRESS"
        );
        if (inProgress) {
          setTimeout(() => {
            this.getUploadStatus();
          }, 5000);
        }
        this.products = res;
      },
      error: (err: any) => {
        this.saving = false;
        this._snackBar.open(
          err.error?.message || "Error while processing your request.",
          { type: "Error" }
        );
      },
    });
  }

  onFileSelected(event: any) {
    this.fileName = "";
    this.file = undefined;
    const file: File = event.target.files[0];
    if (file) {
      if (file.size > this.maxFileSize) {
        this._snackBar.open("Maximum file size limit exceeded.", {
          type: "Warning",
        });
        return;
      }
      this.fileName = file.name;
      this.file = file;
    }
  }

  removeFile(event?: any) {
    event && event.stopPropagation();
    this.fileName = "";
    this.file = undefined;
    this.fileInput.nativeElement.value = "";
  }

  upload() {
    if (!this.file) {
      return;
    }
    this.saving = true;
    const formData = new FormData();
    formData.append("file", this.file);
    this.service.upload(formData).subscribe({
      next: (value) => {
        this.saving = false;
        this._snackBar.open("File uploaded successfully");
        this.getUploadStatus();
        this.removeFile();
      },
      error: (err: any) => {
        this.saving = false;
        this._snackBar.open(
          err.error?.message || "Error while processing your request.",
          { type: "Error" }
        );
      },
    });
  }

  exportFileSummary(product: any) {
    product.loading = true;
    this.service
      .exportFileSummary(product.file_name)
      .pipe(takeUntil(this.ngUnsubscribe), take(1))
      .subscribe((response: any) => {
        const downloadLink = document.createElement("a");
        //@ts-ignore
        downloadLink.href = URL.createObjectURL(
          new Blob([response.body], { type: response.body.type })
        );
        downloadLink.download = `${product.file_name}`;
        downloadLink.click();
        product.loading = false;
      });
  }

  removeMediaUploadStatus(id: any) {
    this.service
      .removeMediaUploadStatus(id)
      .pipe(takeUntil(this.ngUnsubscribe), take(1))
      .subscribe((response: any) => {
        this.getUploadStatus();
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
