import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable, map } from "rxjs";

import { ApiConstant } from "src/app/constants/api.constants";

export interface UserListParam {
  perPage: number;
  pageNo: number;
  search: string;
  [param: string]: any;
}

@Injectable({
  providedIn: "root",
})
export class ManageUserService {
  constructor(private http: HttpClient) {}

  getUsers(data: UserListParam): Observable<any[]> {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(`${ApiConstant.USERS}`, {
      params,
    });
  }

  getUserById(userId: number): Observable<any> {
    return this.http.get<any>(`${ApiConstant.USERS}/${userId}`);
  }

  createUser(user: any): Observable<any> {
    return this.http.post<any>(`${ApiConstant.USERS}`, user);
  }

  updateUser(userId: number, updatedUser: any): Observable<any> {
    return this.http.put<any>(`${ApiConstant.USERS}/${userId}`, updatedUser);
  }

  deleteUser(userId: number): Observable<any> {
    return this.http.delete<any>(`${ApiConstant.USERS}/${userId}`);
  }

  updateUserPassword(id: any, data: any) {
    return this.http.put(`${ApiConstant.USERS}/${id}/change-password`, data);
  }

  getUserRoles(): Observable<any[]> {
    return this.http.get<any[]>(`${ApiConstant.USER_ROLES}`).pipe(
      map((res: any) => {
        const userRoles: any = res?.data || [];
        userRoles.forEach((role: any) => {
          role.roleHierarchy = [];

          const getHierarchyIds = (roleId: any) => {
            const childRoles = userRoles.filter(
              (item: any) => item.parent_role_id === roleId
            );
            for (const childRole of childRoles) {
              role.roleHierarchy.push(childRole.id);
              getHierarchyIds(childRole.id);
            }
          };

          getHierarchyIds(role.id);
        });
        return { ...res, data: userRoles };
      })
    );
  }

  getCustomers(data: any): Observable<any[]> {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(`${ApiConstant.CUSTOMERS}`, {
      params,
    });
  }

  getCustomersByUserId(id: any, data: any): Observable<any[]> | any {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(`${ApiConstant.USERS}/${id}/customers`, {
      params,
    });
  }

  getLoggedInUserCustomers(data: any): Observable<any[]> | any {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(`${ApiConstant.USERS}-customers`, {
      params,
    });
  }

  updateSelectedCustomer(cust_id: any, data: any) {
    return this.http.put(`${ApiConstant.USERS}-customers/${cust_id}`, data);
  }

  deleteAllCustomersByUserId(id: any): Observable<any[]> | any {
    return this.http.delete<any>(`${ApiConstant.USERS}/${id}/customers`);
  }

  removeCustByUserIdAndCustId(id: any, cust_id: any): Observable<any[]> | any {
    return this.http.delete<any>(
      `${ApiConstant.USERS}/${id}/customers/${cust_id}`
    );
  }
}
