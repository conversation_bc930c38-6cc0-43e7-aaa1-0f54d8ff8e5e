import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { BehaviorSubject, map, lastValueFrom, catchError } from "rxjs";
import moment from "moment";
import { ApiConstant } from "src/app/constants/api.constants";
import { AuthService } from "src/app/core/authentication/auth.service";
import { SalesOrderService } from "./sales-order/sales-order.service";
import { SalesOrderSchedulerService } from "./sales-order-scheduler/sales-order-scheduler.service";

@Injectable({
  providedIn: "root",
})
export class CartService {
  private sosReq: any = null;
  private material: any = null;
  private materialEvent: any = new BehaviorSubject<any>(null);
  private product = new BehaviorSubject<any>(null);
  private order = new BehaviorSubject<any>(null);
  private cart = new BehaviorSubject<any>(null);
  private soScheduler = new BehaviorSubject<any>(null);

  constructor(
    private http: HttpClient,
    private auth: AuthService,
    private salesOrderService: SalesOrderService,
    private sosService: SalesOrderSchedulerService
  ) {}

  async getCartByID() {
    if (this.getCartID()) {
      return await lastValueFrom(
        this.http.get<any>(`${ApiConstant.CARTS}/${this.getCartID()}`).pipe(
          map((res) => {
            if (res.status === "success") {
              this.sosReq = res && res.data && res.data.so_simulation_req_data;
              this.cart.next(this.sosReq);
            }
            return res;
          })
        )
      );
    }
  }

  addToCart(item: any) {
    if (this.sosReq && this.sosReq?.to_Item) {
      const findIndex = this.sosReq?.to_Item.findIndex(
        (o: any) => o.Material === item.Material
      );
      if (findIndex === -1) {
        item.RequestedQuantity = item.RequestedQuantity.toString();
        item.to_PricingElement = [];
        this.sosReq.to_Item.push(item);
      } else {
        const totalRQ =
          parseInt(this.sosReq?.to_Item[findIndex]?.RequestedQuantity) +
          item.RequestedQuantity;
        this.sosReq.to_Item[findIndex].RequestedQuantity = totalRQ.toString();
      }
      this.salesOrderSimulation();
    }
  }

  removeFromCart(product_id: string) {
    if (this.sosReq && this.sosReq?.to_Item) {
      const findIndex = this.sosReq?.to_Item.findIndex(
        (o: any) => o.Material === product_id
      );
      if (findIndex > -1) {
        this.sosReq?.to_Item.splice(findIndex, 1);
      }
    }
    this.setMaterial(null);
  }

  updateCart(item: any) {
    if (this.sosReq && this.sosReq?.to_Item) {
      const findIndex = this.sosReq?.to_Item.findIndex(
        (o: any) => o.Material === item.Material
      );
      if (findIndex > -1) {
        this.sosReq.to_Item[findIndex].RequestedQuantity =
          item.RequestedQuantity.toString();
      }
    }
  }

  async salesOrderSimulation() {
    return await lastValueFrom(
      this.salesOrderService
        .salesOrderSimulation(this.sosReq, this.getCartID())
        .pipe(
          map((res: any) => {
            if (res.status === "success") {
              this.cart.next(this.sosReq);
              const data: any =
                res?.data?.SALESORDERSIMULATE?.A_SalesOrderSimulationType || [];
              this.product.next(
                data?.to_Item?.A_SalesOrderItemSimulationType.find(
                  (item: any) => item.Material === this.material
                )
              );
              return data;
            }
            return res;
          })
        )
        .pipe(
          catchError((error) => {
            this.sosReq.to_Item.pop();
            return error;
          })
        )
    );
  }

  async salesOrderCreation(oData: any) {
    const params = new HttpParams().appendAll({ cartID: this.getCartID() });
    const socReq: any = { ...this.sosReq };
    socReq.PurchaseOrderByCustomer = oData.PurchaseOrderByCustomer;
    socReq.SalesOrderType = oData.salesOrderTypeCode || "";
    const RequestedDeliveryDate = moment(oData.RequestedDeliveryDate);
    if (RequestedDeliveryDate.isValid()) {
      socReq.RequestedDeliveryDate = RequestedDeliveryDate.format("YYYY-MM-DD");
    }
    socReq.to_Partner = [
      {
        PartnerFunction: oData.shippingAddress.partner_function,
        Customer: oData.shippingAddress.bp_customer_number,
      },
    ];
    if (oData?.specialInstructions && oData?.textCode) {
      socReq.to_Text = [
        {
          LongTextID: oData?.textCode,
          LongText: oData?.specialInstructions,
          Language: "EN",
        },
      ];
    }
    socReq.User = {
      Email: this.auth.getAuth()?.email || "",
    };
    return await lastValueFrom(
      this.salesOrderService.salesOrderCreation(socReq, this.getCartID()).pipe(
        map((res: any) => {
          if (res.status === "success") {
            this.sosReq.to_Item = [];
            const data: any =
              res?.data?.SALESORDERCREATE?.A_SalesOrderType || [];
            data.shippingAddress = oData.shippingAddress;
            data.user = this.auth.getAuth();
            this.order.next(data);
            return data;
          }
          return res;
        })
      )
    );
  }

  salesQuoteCreation(data: any) {
    const sqcReq: any = {};
    sqcReq.SalesQuotationType = data.salesQuoteTypeCode || "";
    sqcReq.SalesOrganization = this.sosReq?.SalesOrganization || "";
    sqcReq.DistributionChannel = this.sosReq?.DistributionChannel || "";
    sqcReq.OrganizationDivision = this.sosReq?.OrganizationDivision || "";
    sqcReq.SoldToParty = this.sosReq?.SoldToParty || "";
    sqcReq.PurchaseOrderByCustomer = data?.name || "";
    sqcReq.to_Text = [
      {
        Language: "EN",
        LongTextID: data.quoteTextCode || "",
        LongText: data?.description || "",
      },
    ];
    sqcReq.to_Item =
      this.sosReq?.to_Item.map((o: any) => {
        return { Material: o.Material, RequestedQuantity: o.RequestedQuantity };
      }) || [];

    return this.http.post<any>(ApiConstant.SALES_QUOTE_CREATION, sqcReq);
  }

  async createSalesOrderScheduler(oData: any, sData: any) {
    const socReq: any = { ...this.sosReq };
    socReq.PurchaseOrderByCustomer = oData.PurchaseOrderByCustomer;
    socReq.SalesOrderType = oData.salesOrderTypeCode || "";
    const RequestedDeliveryDate = moment(oData.RequestedDeliveryDate);
    if (RequestedDeliveryDate.isValid()) {
      socReq.RequestedDeliveryDate = RequestedDeliveryDate.format("YYYY-MM-DD");
    }
    socReq.to_Partner = [
      {
        PartnerFunction: oData.shippingAddress.partner_function,
        Customer: oData.shippingAddress.bp_customer_number,
      },
    ];
    if (oData?.specialInstructions && oData?.textCode) {
      socReq.to_Text = [
        {
          LongTextID: oData?.textCode,
          LongText: oData?.specialInstructions,
          Language: "EN",
        },
      ];
    }
    socReq.User = {
      Email: this.auth.getAuth()?.email || "",
    };
    const payload = { ...sData };
    payload.customer_id = socReq.SoldToParty;
    payload.req_data = socReq;
    return await lastValueFrom(
      this.sosService.salesOrderSchedulerCreation(payload).pipe(
        map((res: any) => {
          if (res.status === "success") {
            res.data.shippingAddress = oData.shippingAddress;
            res.data.user = this.auth.getAuth();
            this.soScheduler.next(res.data);
            return res.data;
          }
          return res;
        })
      )
    );
  }

  getCartID() {
    return this.auth.getAuth()?.cart?.id || null;
  }

  getCreatedOrder() {
    return this.order.asObservable();
  }

  getCreatedOrderScheduler() {
    return this.soScheduler.asObservable();
  }

  getCart() {
    return this.cart.asObservable();
  }

  setMaterial(product_id: any) {
    this.material = product_id;
    this.materialEvent.next(product_id);
  }

  getMaterial() {
    return this.materialEvent.asObservable();
  }

  getProduct() {
    return this.product.asObservable();
  }

  setSOSReq(sosReq: any) {
    this.sosReq = sosReq;
  }

  getSOSReq() {
    return this.sosReq;
  }

  mergeToCart(data: any) {
    return this.http.post(
      `${ApiConstant.CARTS}/${this.getCartID()}/merge`,
      data
    );
  }
}
