:host {
	padding: 25px;
}
.order-id-sec {
	margin: 0;
	padding: 0;
	position: relative;

	.order-id-body {
		margin: 0;
		padding: 0;
		display: flex;
		justify-content: space-between;
		gap: 0 3%;

		.order-id-info {
			margin: 0;
			padding: 0;
			flex: 4;

			.order-details {
				margin: 0 0 20px 0;
				padding: 24px;
				background: var(--snjy-color-white);
				border: 1px solid var(--snjy-border-color-secondary);
				border-radius: 10px;
				box-shadow: var(--snjy-box-shadow);

				h3 {
					margin: 0 0 15px 0;
					padding: 0;
					position: relative;
					font-size: var(--snjy-font-size-1-125);
					font-weight: var(--snjy-font-weight-bold);
					color: #00216c;
					line-height: 20px;
				}

				ul {
					padding: 34px 30px;
					position: relative;
					list-style: none;
					background: #f0f5f6;
					border-radius: 8px;
					display: flex;
					justify-content: flex-start;
					gap: 40px 4%;
					flex-wrap: wrap;

					li {
						margin: 0;
						padding: 0;
						color: #687491;
						font-weight: var(--snjy-font-weight-medium);
						font-size: var(--snjy-font-size-0-8);
						flex: 0 0 30%;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						flex-wrap: wrap;
						gap: 0 3px;
						line-height: 22px;

						.material-icons-outlined {
							margin: 0;
							padding: 0;
							width: fit-content;
							height: fit-content;
							display: inline-flex;
							align-items: center;
							justify-content: center;
							font-size: var(--snjy-font-size-1-25);
							color: #687491;
						}

						span:not(.material-icons-outlined) {
							margin: 0;
							padding: 0 0 0 22px;
							display: block;
							color: #0077d7;
							font-weight: var(--snjy-font-weight-medium);
							font-size: var(--snjy-font-size-0-8);
							width: 100%;
						}
					}
				}

				.item-box {
					margin: 0 0 18px 0;
					padding: 16px;
					background: #eff4f5;
					display: flex;
					justify-content: flex-start;
					gap: 0 3%;
					position: relative;
					border-radius: 7px;

					.item-box-img {
						margin: 0;
						padding: 0;
						position: relative;
						flex: 0 0 26%;
						height: 144px;
						background-size: contain;
						background-repeat: no-repeat;
						background-position: center;
						border-radius: 7px;
						overflow: hidden;
						border: 1px solid rgba(27, 125, 203, 0.2901960784);
					}

					.item-box-content {
						margin: 0;
						padding: 0;
						position: relative;
						width: 100%;

						h4 {
							margin: 15px 0 0 0;
							padding: 0;
							position: relative;
							font-size: var(--snjy-font-size-1);
							font-weight: var(--snjy-font-weight-medium);
							color: var(--snjy-color-dark-secondary);
							line-height: 20px;
							display: block;
						}

						small {
							margin: 0;
							padding: 0;
							font-size: var(--snjy-font-size-0-75);
							color: #687491;
							font-weight: var(--snjy-font-weight-medium);
						}

						.item-box-bottom-content {
							margin: 35px 0 0 0;
							padding: 0;
							position: relative;
							display: flex;
							align-items: center;
							justify-content: space-between;

							.quantity {
								margin: 0;
								padding: 0 0 0 7px;
								position: relative;
								background: var(--snjy-color-white);
								height: 38px;
								width: 148px;
								display: inline-flex;
								align-items: center;
								justify-content: space-between;
								border-radius: 5px;
								border: 1px solid #ffd383;
								font-size: var(--snjy-font-size-0-8);
								font-weight: var(--snjy-font-weight-medium);
								overflow: hidden;

								span {
									margin: 0;
									padding: 0;
									width: 40px;
									height: 38px;
									background: #ffd383;
									display: inline-flex;
									align-items: center;
									justify-content: center;
									color: var(--snjy-color-dark-secondary);
								}
							}

							.item-price {
								margin: 0;
								padding: 0;
								position: relative;
								font-size: var(--snjy-font-size-2);
								font-weight: var(--snjy-font-weight-bold);
								color: var(--snjy-color-dark-secondary);
								line-height: 22px;
								text-align: right;

								span {
									margin: 0;
									padding: 0;
									position: relative;
									font-size: var(--snjy-font-size-0-75);
									font-weight: var(--snjy-font-weight-medium);
									color: #687491;
									display: block;
								}
							}

						}
					}
				}

			}
		}
	}
}

button,
button:disabled,
button:hover {
	border-radius: 8px;
	margin-bottom: 0.8rem;
	padding: 0.5rem 1rem;
	font-family: var(--snjy-font-family);
	background-color: var(--snjy-button-color-primary);
	color: var(--snjy-color-white);
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 0 5px;
	font-size: var(--snjy-font-size-0-875);
	line-height: 16px;
	font-weight: var(--snjy-font-weight-medium);
}

button:disabled {
	opacity: .8;
}

.btn-light:hover,
.btn-light {
	background-color: var(--snjy-font-color-primary);
	color: var(--snjy-button-color-primary);
	border: 1px solid var(--snjy-button-color-primary);
}

@media only screen and (max-width: 1024px) {
:host {
	padding: 15px;
}
}