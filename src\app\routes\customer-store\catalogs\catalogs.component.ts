import { Component, HostListener, OnInit } from "@angular/core";
import {
  ProductSearchParam,
  StoreProductService,
} from "../services/store.product.service";
import {
  Subject,
  Observable,
  map,
  startWith,
  switchMap,
  tap,
  of,
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
} from "rxjs";
import { PagerService } from "../services/pager.service";
import { AuthService } from "src/app/core/authentication/auth.service";
import { ActivatedRoute, Params } from "@angular/router";
import { ProductService } from "../../backoffice/product/product.service";
import { FACETS } from "./facet/facet.component";
import { FormControl } from "@angular/forms";

@Component({
  selector: "app-catalogs",
  templateUrl: "./catalogs.component.html",
  styleUrls: ["./catalogs.component.scss"],
  host: { id: 'catalogs-page' }
})
export class CatalogsComponent implements OnInit {
  search = new FormControl('');
  products: any = [];
  pageSizes = [12, 24, 36, 48];
  selectedPageSize = 12;
  categories!: Omit<FACETS, "values">[];
  searchByField: any = [{
    status: 'ACTIVE'
  }]
  searchParam: ProductSearchParam = {
    perPage: 12,
    pageNo: 1,
  };
  loading = false;
  totaleProducts = 0;
  subject = new Subject();
  products$!: Observable<any>;
  pager: any = {};
  showFacet = true;

  constructor(
    private storeProductService: StoreProductService,
    private pagerService: PagerService,
    private authService: AuthService,
    private productService: ProductService,
    private route: ActivatedRoute
  ) { }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.setFacet();
  }

  setFacet() {
    if (window.innerWidth <= 900) {
      this.showFacet = false;
    }
  }

  ngOnInit(): void {
    this.setFacet();
    this.searchInputListener();
    const allCat$ = this.productService.getAllCategory();
    const catParam$ = this.route.queryParams;
    combineLatest([allCat$, catParam$]).subscribe((res) => {
      this.categories = res[0];
      const catParam = res[1];
      if (catParam["cat"]) {
        const _cats = catParam["cat"].split(",");
        const _catIds = _cats.map((cat: string) => {
          const category = this.categories.find((_c) => _c.name === cat);
          return category ? category.id : "";
        });
        this.searchByField = this.searchByField.filter(
          (s: any) =>
            s.product_category_id ? _catIds.includes(s.product_category_id) : true
        );
        _catIds.forEach((id: any) => {
          this.updateSearchByFieldVal('product_category_id', id);
        });
        this.fetchProducts();
      } else {
        this.searchByField = this.searchByField.filter(
          (s: any) => !s.product_category_id
        );
        this.fetchProducts();
      }
    });
  }

  toggleFacet() {
    this.showFacet = !this.showFacet;
  }

  updateSearchByFieldVal(key: string, val: any) {
    const index = this.searchByField.findIndex((s: any) => s[key] === val);
    const obj: any = {};
    obj[key] = val;
    if (index > -1) {
      this.searchByField[index] = obj;
    } else {
      this.searchByField.push(obj);
    }
  }

  searchInputListener() {
    this.search.valueChanges
      .pipe(
        debounceTime(1000), // Waiting for 1 sec while you are typing
        distinctUntilChanged() // Prevents the emitting if the 'start' value and the 'end' value are the same
      )
      .subscribe(value => {
        this.searchByField = this.searchByField.filter(
          (s: any) => !s.name && !s.product_id
        );
        if (value && value.length > 3) {
          this.searchByField.push({ product_id: value });
          this.searchByField.push({ name: value });
          this.fetchProducts();
        }
        if (!value) {
          this.fetchProducts();
        }
      });
  }

  fetchProducts() {
    this.products$ = this.subject.asObservable().pipe(
      startWith(0),
      switchMap((d) => {
        this.loading = true;
        this.searchParam.searchBy = this.searchByField.map((o: any) => Object.keys(o)[0]).join();
        this.searchParam.search = this.searchByField.map((o: any) => Object.values(o)[0]).join();
        return this.storeProductService.search(this.searchParam).pipe(
          map((res) => {
            this.totaleProducts = res.total;
            if (d === 0) this.setPage(1, false);
            return res.data;
          }),
          // switchMap((products: any) => {
          //   return this.loadSalesPrice(products).pipe(
          //     map((res) => {
          //       if (res?.data?.length) {
          //         return products.map((product: any) => ({
          //           ...product,
          //           ...res.data.find(
          //             (val: any) => val.Material === product.product_id
          //           ),
          //         }));
          //       }
          //       return products;
          //     }),
          //     catchError((err) => {
          //       return of(products);
          //     })
          //   );
          // }),
          tap((_) => (this.loading = false))
        );
      })
    );
  }

  loadSalesPrice(data: any) {
    if (!data?.length) return of(data);
    const userDetail = this.authService.userDetail.partner_function;
    const obj = {
      SalesOrg: userDetail.sales_organization,
      Dist_Channel: userDetail.distribution_channel,
      Division: userDetail.division,
      SoldTo: userDetail.bp_customer_number,
      Products: data.map((val: any) => ({ Material: val.product_id })),
    };
    return this.storeProductService.getSalesPrice(obj);
  }

  setPage(page: number, load: boolean) {
    this.pager = this.pagerService.getPager(
      this.totaleProducts,
      page,
      this.searchParam.perPage
    );
    this.searchParam = {
      perPage: this.pager.pageSize,
      pageNo: this.pager.currentPage,
    };
    if (load) {
      this.subject.next(page);
    }
  }
}
