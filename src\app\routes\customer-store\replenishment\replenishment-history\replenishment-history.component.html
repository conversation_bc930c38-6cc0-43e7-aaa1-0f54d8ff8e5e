<section class="order-status-sec">
    <div class="order-status-body">
        <div class="all-main-title-sec">
            <h1>Scheduled Orders</h1>
            <div class="all-bedcrumbs">
                <ul>
                    <li>
                        <a [routerLink]="['/store/dashboard']">
                            <span class="material-icons-outlined">home</span> Home
                        </a>
                    </li>
                    <li>Scheduled Orders</li>
                </ul>
            </div>
        </div>
        <div class="order-contact-list">
            <div class="order-c-box">
                <div class="order-c-icon"><img src="/assets/images/seller-icon.png" alt="" title="" /></div>
                <div class="order-c-details">
                    <h4>Customer #</h4>
                    <small>{{sellerDetails.bp_customer_number}}</small>
                </div>
            </div>
            <div class="order-c-box">
                <div class="order-c-icon"><img src="/assets/images/phone-icon.png" alt="" title="" /></div>
                <div class="order-c-details">
                    <h4>Customer Name</h4>
                    <small>{{sellerDetails.name}}</small>
                </div>
            </div>
            <div class="order-c-box address-box">
                <div class="order-c-icon"><img src="/assets/images/address-icon.png" alt="" title="" /></div>
                <div class="order-c-details">
                    <h4>ADDRESS</h4>
                    <small>{{sellerDetails.address}}</small>
                </div>
            </div>
        </div>
        <form class="order-status-form all-form-res" [formGroup]="filterForm">
            <div class="form">
                <div class="form-group">
                    <label><span class="material-icons-outlined">calendar_month</span> Start Date</label>
                    <div class="input-group">
                        <input class="form-control" name="picker1" formControlName="start_date" ngbDatepicker
                            #d="ngbDatepicker" />
                        <button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
                            <i class="material-icons-outlined">
                                calendar_month
                            </i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label><span class="material-icons-outlined">calendar_month</span> End Date</label>
                    <div class="input-group">
                        <input class="form-control" name="picker2" formControlName="end_date" ngbDatepicker
                            #d1="ngbDatepicker" [minDate]="f?.start_date?.value" />
                        <button class="btn btn-outline-secondary" (click)="d1.toggle()" type="button">
                            <i class="material-icons-outlined">
                                calendar_month
                            </i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label>
                        <span class="material-icons-outlined">feed</span> Status
                    </label>
                    <select class="form-control select-arrow-down" formControlName="status">
                        <option value="">All</option>
                        <option value="Scheduled">Scheduled</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="form-group o-num">
                    <label>
                        <span class="material-icons-outlined">pin</span> Frequency
                    </label>
                    <select class="form-control select-arrow-down" formControlName="schedule_type">
                        <option value="">All</option>
                        <option value="DAILY">Daily</option>
                        <option value="WEEKLY">Weekly</option>
                        <option value="MONTHLY">Monthly</option>
                    </select>
                </div>
                <div class="form-group p-o-num fg-md">
                    <label class="text-truncate">
                        <span class="material-icons-outlined">fact_check</span> Scheduled Order #
                    </label>
                    <input type="input" class="form-control" placeholder="Scheduled Order #" formControlName="id"
                        (keyup.enter)="getOrderHistory()">
                </div>
                <p class="text-danger d-flex w-100" *ngIf="submitted && filterForm.touched && filterForm.invalid">
                    Please add a valid start and end date
                </p>
                <div class="form-btn-sec d-flex justify-content-center gap-1">
                    <button type="button" class="mx-4 order-s-btn" (click)="clear()">Clear</button>
                    <button type="button" class="mx-4 order-s-btn" (click)="getOrderHistory()" [disabled]="loading">
                        {{ loading ? "Searching..." : "Search" }}
                    </button>
                </div>
            </div>
        </form>
        <div class="order-s-table d-flex flex-column align-items-end">
            <app-grid [columns]="columnDefs" (rowClick)="rowToggle($event)" [data]="orders" [showExport]="true"
                (exportClick)="exportToExcel()" *ngIf="!loading && orders.length">
                <button header type="button" (click)="updateReplenishOrder()" class="btn btn-light-cancel mb-3 me-3"
                    [disabled]="!selectedOrders.length">
                    Update Scheduled Order
                </button>
            </app-grid>
            <div class="w-100" *ngIf="loading || !orders.length">{{ loading ? 'Loading...' : 'No records found.' }}
            </div>
        </div>
    </div>
</section>