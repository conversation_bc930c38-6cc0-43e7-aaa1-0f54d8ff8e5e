import { Component, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { Subject, takeUntil, take } from "rxjs";
import moment from "moment";

import { AddMessageComponent } from "./add-message/add-message.component";
import { TicketsService } from "../tickets.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { AuthService } from "src/app/core/authentication/auth.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
  selector: "app-ticket-detail",
  templateUrl: "./ticket-detail.component.html",
  styleUrls: ["./ticket-detail.component.scss"],
})
export class TicketDetailComponent implements OnInit, OnDestroy {
  public moment: any = moment;
  public ticketID: any = null;
  public ticket: any = null;
  public sellerDetails: any = {};
  public auth: any = null;
  private ngUnsubscribe = new Subject<void>();
  public loading: any = false;

  constructor(
    private _snackBar: AppToastService,
    public dialog: NgbModal,
    public authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private ticketsService: TicketsService
  ) {
    this.auth = this.authService.getAuth();
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit() {
    this.activatedRoute.paramMap
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((params) => {
        this.ticketID = params.get("ticket_id");
        if (this.ticketID) {
          this.getTicketDetails();
        }
      });
  }

  getTicketDetails() {
    const payload: any = {};
    payload.TICKET = this.ticketID;
    payload.TICKET_TYPE = "SRRQ";
    this.loading = true;
    this.ticketsService
      .getTicketDetails(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.ticket = res?.data?.SERVICEREQUEST || null;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  openAddMessageDialog() {
    const dialogRef = this.dialog.open(AddMessageComponent);
    dialogRef.componentInstance.data = {
      TICKET_ID: this.ticket?.TICKET_ID
    };
    dialogRef.result.then((result: any) => {
      const TicketId = result?.data?.SERVICEREQUESTUPDATE?.TicketId;
      if (TicketId === this.ticket?.TICKET_ID) {
        this.getTicketDetails();
      }
    });
  }

  downloadAttachment(ticketID: string, attachment: any) {
    attachment.downloading = true;
    this.ticketsService
      .getTicketAttachment(ticketID, attachment?.FILE_LINK)
      .pipe(take(1))
      .subscribe((response: any) => {
        attachment.downloading = false;
        const downloadLink = document.createElement("a");
        downloadLink.href = URL.createObjectURL(
          new Blob([response.body], { type: response.body.type })
        );
        downloadLink.download = attachment?.NAME;
        downloadLink.click();
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
