<h4 class="mb-4">Forgot Password</h4>
<form [formGroup]="form">
    <div class="form-group mb-4 required">
        <label class="mb-2">Email</label>
        <input type="text" formControlName="email" class="form-control mt-1 mb-2"
            placeholder="Enter your registerd email" [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" />
        <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
            <div *ngIf="f['email'].errors['required']">Email is required</div>
            <div *ngIf="f['email'].errors['email']">Email is invalid</div>
        </div>
        <span class="form-text hint">We will send reset instructions on your registered email</span>
    </div>
    <div class="d-flex justify-content-between">
        <button type="submit" class="btn btn-light" [disabled]="!!form.invalid || saving" (click)="onSubmit()">Reset
            Password</button>
        <button type="submit" class="btn btn-primary" (click)="activeModal.dismiss()">
            Cancel
        </button>
    </div>
</form>