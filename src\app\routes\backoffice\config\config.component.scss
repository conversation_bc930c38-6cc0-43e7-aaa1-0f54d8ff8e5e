::ng-deep {
    .checkbox-input {
        width: 30px;
        height: 30px !important;
    }
}

.hasDetails {
    height: 40vh;

    tr {
        cursor: pointer;
    }
}

:host::ng-deep {
    .sub-tab {
        .tab-group {
            margin: 0 !important;
        }

        .tab-content {
            border-radius: 0 !important;
        }
    }

    .config-tab-links {
        ul.tab-group {
            li {
                &:nth-child(2)::before {
                    content: "\e873" !important;
                }

                &:nth-child(3)::before {
                    content: "\e6b1" !important;
                }

                &:nth-child(4)::before {
                    content: "\f009" !important;
                }

                &:nth-child(5)::before {
                    content: "\ef42" !important;
                }

                &:nth-child(6)::before {
                    content: "\e66b" !important;
                }

                &:nth-child(7)::before {
                    content: "\f233" !important;
                }

                &:nth-child(8)::before {
                    content: "\ef43" !important;
                }

                &:nth-child(9)::before {
                    content: "\e1a1" !important;
                }

                &:nth-child(10)::before {
                    content: "\e638" !important;
                }
            }
        }

        .tab-content .form-check input[type=radio]~label {
            padding: 0;
            margin: 0 !important;
            line-height: 25px;

            &::before {
                display: none;
            }
        }
    }
}