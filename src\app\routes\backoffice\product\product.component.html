<div class="grid-toolbar d-flex justify-content-end align-items-center pb-3">
      <input type="text" [ngModel]="searchText" class="form-control me-2" placeholder="Search by...Contains, Starts with, Equal to"
            (ngModelChange)="onSearchChange($event)">
      <select [ngModel]="searchBy" (ngModelChange)="onSearchByChange($event)" class="form-select me-2"
            aria-label="Default select example">
            <option value="product_id" selected>Product ID</option>
            <option value="pd_description">Product Description</option>
      </select>
      <app-grid-column #columnMenu class="btn-column-sec" [columns]="columnDefs" buttonText="Columns"
            selectableChecked="show" (columnChange)="_onColumnChange($event)"
            (columnPositionChange)="_columnPositionChange($event)">
      </app-grid-column>
</div>
<ag-grid-angular  [getRowId]="getRowId" style="width: 100%; height: 485px;" class="ag-theme-alpine bck-table-body" [columnDefs]="columnDefs"
      [defaultColDef]="defaultColDef" (cellClicked)="handleCellClicked($event)" [pagination]="true"
      [paginationPageSize]="10" [paginationPageSizeSelector]="[10, 20, 50, 100]" [gridOptions]="gridOptions" (gridReady)="onGridReady($event)"></ag-grid-angular>

<ng-container *ngIf="rowClicked">
      <app-product-details [model]="model" class="bck-table-details" [relationshipTypes]="relationshipTypes" (onUpdate)="refreshRowData($event)"/>
</ng-container>