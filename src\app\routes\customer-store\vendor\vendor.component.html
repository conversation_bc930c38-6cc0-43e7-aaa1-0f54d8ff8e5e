<section class="vendor-sec">
    <app-banner-preview [customerID]="customer_id" [settings]="settings" [placement]="'VENDOR'"></app-banner-preview>
    <!-- <div class="vendor-img">
        <video width="100%" height="100%" autoplay loop controls>
            <source src="/assets/images/vendor.mp4" type="video/mp4">
        </video>
    </div> -->
    <div class="cus-services-body">
        <!-- <div class="cus-services-title">
            <h1>Welcome to <span>vendor Portal</span></h1>
        </div> -->
        <div class="cus-services-list">
            <a class="cus-services-box" [routerLink]="['/store/vendor/account']">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">person</i>
                </div>
                <div class="cus-services-box-text">Vendor Account</div>
            </a>
            <ng-container *checkAppFeature="'F0006'">
                <a class="cus-services-box" [routerLink]="['/store/order-history']" *checkPermission="'P0052'">
                    <div class="cus-services-box-icon">
                        <img src="/assets/images/order-status.svg" />
                    </div>
                    <div class="cus-services-box-text">Purchase orders</div>
                </a>
            </ng-container>
            <ng-container *checkAppFeature="'F0002'">
                <a class="cus-services-box" [routerLink]="['/store/invoices']" *checkPermission="'P0054'">
                    <div class="cus-services-box-icon">
                        <img src="/assets/images/invoices.svg" />
                    </div>
                    <div class="cus-services-box-text">Invoices</div>
                </a>
            </ng-container>
            <a class="cus-services-box" [routerLink]="['/store/vendor/quote-manager']">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">request_quote</i>
                </div>
                <div class="cus-services-box-text">Quote Manager</div>
            </a>
            <a class="cus-services-box" href="javascript:void(0);">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">sell</i>
                </div>
                <div class="cus-services-box-text">Stock</div>
            </a>
            <a class="cus-services-box" href="javascript:void(0);">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">notifications</i>
                </div>
                <div class="cus-services-box-text">ASN</div>
            </a>
            <a class="cus-services-box" [routerLink]="['/store/vendor/reports']">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">summarize</i>
                </div>
                <div class="cus-services-box-text">Reports</div>
            </a>
        </div>
    </div>

</section>