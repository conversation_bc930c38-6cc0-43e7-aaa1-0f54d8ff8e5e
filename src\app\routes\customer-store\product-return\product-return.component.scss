:host {
  padding: 25px;
}

.all-main-title-sec.cart-details-main-title {
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  padding: 0 0 25px 0;

  h1 {
    text-align: left;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 0 10px;
    justify-content: space-between;
  }

  small {
    margin: 0;
    padding: 0;
    font-size: 15px !important;
    color: var(--snjy-color-dark-secondary);

    strong {
      color: var(--snjy-button-color-primary);
    }
  }
}

.order-details {
  margin: 0 0 20px 0;
  padding: 24px;
  background: var(--snjy-color-white);
  border: 1px solid var(--snjy-border-color-secondary);
  border-radius: 10px;
  box-shadow: var(--snjy-box-shadow);
  position: relative;

  h3 {
    margin: 0 0 15px 0;
    padding: 0;
    position: relative;
    font-size: var(--snjy-font-size-1-125);
    font-weight: var(--snjy-font-weight-bold);
    color: #00216c;
    line-height: 20px;
  }

  .check-all-btn-part {
    margin: 0;
    padding: 0;
    position: absolute;
    top: 18px;
    right: 28px;

    label {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 0 7px;
      position: relative;
      font-size: var(--snjy-font-size-0-875);
      text-transform: uppercase;
      font-weight: var(--snjy-font-weight-medium);
    }
  }

  ul {
    padding: 34px 30px;
    position: relative;
    list-style: none;
    background: #f0f5f6;
    border-radius: 8px;
    display: flex;
    justify-content: flex-start;
    gap: 40px 4%;
    flex-wrap: wrap;

    li {
      margin: 0;
      padding: 0;
      color: #687491;
      font-weight: var(--snjy-font-weight-medium);
      font-size: var(--snjy-font-size-0-8);
      flex: 0 0 30%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: 0 3px;
      line-height: 22px;

      .material-icons-outlined {
        margin: 0;
        padding: 0;
        width: fit-content;
        height: fit-content;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: var(--snjy-font-size-1-25);
        color: #687491;
      }

      span:not(.material-icons-outlined) {
        margin: 0;
        padding: 0 0 0 22px;
        display: block;
        color: #0077d7;
        font-weight: var(--snjy-font-weight-medium);
        font-size: var(--snjy-font-size-0-8);
        width: 100%;
      }
    }
  }

  .item-box {
    margin: 0 0 18px 0;
    padding: 16px 16px 16px 80px;
    background: #eff4f5;
    display: flex;
    justify-content: flex-start;
    gap: 0 3%;
    position: relative;
    border-radius: 7px;

    .checkbox-input {
      position: absolute;
      top: 0;
      left: 25px;
      bottom: 0;
      margin: auto 0;
      width: 27px;
      height: 27px;
      z-index: 99;
      -webkit-appearance: auto !important;
      appearance: auto !important;
      outline: none;
      box-shadow: none;
    }

    .item-box-img {
      margin: 0;
      padding: 0;
      position: relative;
      flex: 0 0 200px;
      height: 200px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 7px;
      overflow: hidden;
      border: 1px solid rgba(27, 125, 203, 0.2901960784);
    }

    .item-box-content {
      margin: 0;
      padding: 0;
      position: relative;
      width: 100%;

      h4 {
        margin: 15px 0 10px 0;
        padding: 0;
        position: relative;
        font-size: var(--snjy-font-size-1);
        font-weight: var(--snjy-font-weight-medium);
        color: var(--snjy-color-dark-secondary);
        line-height: 20px;
        display: block;
      }

      .item-box-list {
        display: flex;
        gap: 0 15px;
        margin: 2px 0;
        flex-direction: column;

        small {
          margin: 0 0 2px 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-75);
          color: var(--snjy-button-color-primary);
          font-weight: var(--snjy-font-weight-medium);
          display: flex;
          align-items: center;
          gap: 0 5px;

          span {
            color: #687491;
            min-width: 80px;
          }
        }
      }

      .item-box-bottom-content {
        margin: 25px 0 0 0;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .quantity {
          margin: 0;
          padding: 0;
          position: relative;
          background: var(--snjy-color-white);
          height: 38px;
          width: fit-content;
          display: inline-flex;
          align-items: center;
          justify-content: space-between;
          border-radius: 5px;
          border: 1px solid #ffd383;
          font-size: var(--snjy-font-size-0-8);
          font-weight: var(--snjy-font-weight-medium);
          overflow: hidden;

          span.req-qty {
            margin: 0;
            padding: 0;
            width: 70px;
            height: 38px;
            background: #ffd383;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--snjy-color-dark-secondary);

            input {
              margin: 0;
              padding: 0;
              width: 100%;
              background: no-repeat;
              border: none;
              font-size: var(--snjy-font-size-1);
              text-align: center;
              outline: none;
              box-shadow: none;
            }
          }
        }

        .item-price {
          margin: 0;
          padding: 0;
          position: absolute;
          font-size: var(--snjy-font-size-1-125);
          font-weight: var(--snjy-font-weight-bold);
          color: #0077d7;
          line-height: 22px;
          text-align: right;
          top: 15px;

          span {
            margin: 0;
            padding: 0;
            position: relative;
            font-size: var(--snjy-font-size-0-75);
            font-weight: var(--snjy-font-weight-medium);
            color: #687491;
            display: block;
          }
        }
      }
    }
  }
}

$color_1: var(--snjy-color-dark-secondary);
$color_2: var(--snjy-color-white);

.return-reason {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  position: relative;

  .return-r-dropdown {
    margin: 0;
    padding: 0;
    max-width: 350px;
    width: 100%;

    label {
      margin: 0 0 5px 0;
      padding: 0;
      position: relative;
      font-size: var(--snjy-font-size-1);
      font-weight: var(--snjy-font-weight-medium);
      color: $color_1;
      line-height: 20px;
      display: block;
    }

    select.form-control {
      margin: 0;
      padding: 0 11px;
      height: 35px;
      background: #f0f7ff;
      border-radius: 10px;
      font-size: var(--snjy-font-size-0-875);
      font-weight: var(--snjy-font-weight-normal) !important;
      border: 1px solid rgba(134, 153, 169, 0.3803921569);
    }
  }
}

.return-r-btn {
  button {
    border-radius: 10px;
    display: flex;
    font-size: var(--snjy-font-size-0-875);
    height: 44px;
    align-items: center;
    justify-content: center;
    line-height: 14px;
    min-width: 180px;
    padding-bottom: 0;
    padding-top: 0;
    cursor: pointer;
    color: $color_2;
    background: var(--snjy-button-gradient);
    position: relative;
    text-transform: uppercase;
    font-weight: var(--snjy-font-weight-medium) !important;
    transition: all0 0.3s ease-in-out;
    border: none;
  }
}

.initiate-return-reason {
  margin: 40px 0 0 0;
  padding: 0;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  position: relative;
}

.return-refund-type {
  label {
    font-size: var(--snjy-font-size-1);
    font-weight: var(--snjy-font-weight-medium);
  }
}

.initiate-return-list {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 15px;
  position: relative;
  flex-direction: row;
  height: 35px;

  label {
    display: flex;
    align-items: center;
    gap: 0 7px;

    .form-control {
      margin: auto 0;
      width: 18px;
      height: 18px;
      appearance: auto !important;
      outline: none;
      box-shadow: none;
    }

    span {
      margin: 0;
      padding: 0;
      font-size: var(--snjy-font-size-0-9);
      color: $color_1;
      font-weight: var(--snjy-font-weight-normal);
      line-height: 23px;
    }
  }
}

@media only screen and (max-width: 1366px) {
.return-reason .return-r-dropdown {
    max-width: 230px !important;
}
}

@media only screen and (max-width: 1366px) {
.return-reason{
    align-items: start !important;
}
.initiate-return-list {
  flex-direction: column !important;
  align-items: start !important;
  height: fit-content !important;
  gap: 7px !important;
}
}

@media only screen and (max-width: 1024px) {
  .return-reason {
    align-items: start !important;
    flex-direction: column;
    gap: 14px;
    .return-r-dropdown {
      max-width: 50% !important;
    }
    .initiate-return-list {
      flex-direction: row !important;
    }
    .w-25 {
      width: fit-content !important;
    }
  }
}

@media only screen and (max-width: 768px) {
.item-price {
  bottom: 8px;
  right: 0;
  top: auto !important;
}
.order-details .item-box .checkbox-input {
  top: 25px;
  left: 25px;
  bottom: auto;
  width: 22px;
  height: 22px;
}
.return-reason .return-r-dropdown {
  max-width: 100% !important;
}
}

@media only screen and (max-width: 414px) {
.item-price {
    bottom: 0 !important;
    position: relative !important;
}
}

