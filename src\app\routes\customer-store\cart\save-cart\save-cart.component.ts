import { Component, Input } from '@angular/core';
import { Validators, FormBuilder } from '@angular/forms';
import { SavedCartsService } from '../../saved-carts/saved-carts.service';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-save-cart',
  templateUrl: './save-cart.component.html',
  styleUrls: ['./save-cart.component.scss']
})
export class SaveCartComponent {

  form = this.fb.group({
    name: ["", Validators.required],
    description: ["", Validators.required],
  });
  saving = false;
  @Input() data = {};

  get f() {
    return this.form.controls;
  }

  constructor(
    public fb: FormBuilder,
    private _snackBar: AppToastService,
    private service: SavedCartsService,
    public activeModal: NgbActiveModal
  ) {

  }
  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    const data = {
      ...this.form.value,
      payload: JSON.stringify(this.data)
    }
    this.service.save(data).subscribe({
      complete: () => {
        this.onReset();
        this.activeModal.dismiss();
        this.saving = false;
        this._snackBar.open('Cart saved successfully!');
      },
      error: (err) => {
        this.saving = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      },
    })
  }

  onReset(): void {
    this.form.reset();
  }

}
