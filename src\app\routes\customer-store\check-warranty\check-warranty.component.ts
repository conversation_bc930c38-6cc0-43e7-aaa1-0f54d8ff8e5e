import { Component } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { Subject, takeUntil } from "rxjs";
import moment from "moment";

import { AuthService } from "src/app/core/authentication/auth.service";
import { ProductRegisterService } from "../services/product-register/product-register.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-check-warranty",
  templateUrl: "./check-warranty.component.html",
  styleUrls: ["./check-warranty.component.scss"],
})
export class CheckWarrantyComponent {
  private ngUnsubscribe = new Subject<void>();
  public submitted: boolean = false;
  public saving: boolean = false;
  public loading: boolean = false;
  public sellerDetails: any = null;
  public registerProductList: any[] = [];
  public params: any = { perPage: 100, pageNo: 1 };
  public filterForm: FormGroup;
  public warrantyStatus: any = null;

  constructor(
    private _snackBar: AppToastService,
    private formBuilder: FormBuilder,
    public authService: AuthService,
    public productRegisterService: ProductRegisterService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit(): void {
    this.createForm();
  }

  // convenience getter for easy access to form fields
  get f(): any {
    return this.filterForm?.controls;
  }

  createForm() {
    this.filterForm = this.formBuilder.group({
      serial: [""],
      product_id: [""],
    });
  }

  clear() {
    this.filterForm.patchValue({ serial: "", product_id: "" });
  }

  getRegisterProductList() {
    this.submitted = true;
    if (this.filterForm.invalid) {
      return;
    }
    const payload: any = this.filterForm.value;
    if (!Object.values(payload).some((item) => item)) {
      this._snackBar.open("At least one value required from the filters.", {
        type: "Error",
      });
      this.registerProductList = [];
      return;
    }
    const search: any[] = [];
    const searchBy: any[] = [];
    for (let key in payload) {
      if (payload.hasOwnProperty(key) && payload[key]) {
        search.push(payload[key]);
        searchBy.push(key);
      }
    }
    if (search.length) {
      this.params.search = search.join(",");
    } else {
      delete this.params.search;
    }
    if (searchBy.length) {
      this.params.searchBy = searchBy.join(",");
    } else {
      delete this.params.searchBy;
    }
    this.loading = true;
    return this.productRegisterService
      .getAlls(this.params)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.loading = false;
          this.submitted = false;
          this.registerProductList =
            res?.data?.map((d: any) => {
              d.name = d.product_id;
              d.startDate = new Date(d.warranty_start);
              d.stilRuning = moment().isSameOrBefore(moment(d.warranty_end));
              if (d.stilRuning) {
                d.endDate = new Date(
                  new Date().getFullYear(),
                  new Date().getMonth()
                );
              } else {
                d.endDate = new Date(d.warranty_end);
              }
              return d;
            }) || [];
          this.warrantyStatus = this.getRemainingWarranty(
            this.registerProductList
          );
          if (!this.registerProductList.length) {
            this._snackBar.open("Warranty not found.", {
              type: "Error",
            });
          }
        },
        error: () => {
          this.loading = false;
          this.submitted = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  getRemainingWarranty(registerProductList: any) {
    const runningWarranty: any = registerProductList.find(
      (o: any) => o.stilRuning
    );
    const obj = { message: "Out of warranty", status: "Expired" };
    if (!runningWarranty) {
      return obj;
    }
    const currentDate = moment();
    const endDate = moment(runningWarranty.warranty_end);

    if (endDate.isBefore(currentDate)) {
      return obj;
    }

    const duration = moment.duration(endDate.diff(currentDate));
    const monthsRemaining = duration.asMonths();
    const daysRemaining = duration.asDays();

    if (monthsRemaining >= 1) {
      obj.status = "Running";
      obj.message = `${Math.floor(monthsRemaining)} months remaining`;
    } else {
      obj.status = "Running";
      obj.message = `${Math.ceil(daysRemaining)} days remaining`;
    }
    return obj;
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
