import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { ProductDetailComponent } from './product-detail.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AngularEditorModule } from '@kolkov/angular-editor';
import { NgxImageZoomModule } from 'ngx-image-zoom';

@NgModule({
  declarations: [
    ProductDetailComponent
  ],
  imports: [
    CommonModule,
    NgxImageZoomModule,
    SharedModule,
    AngularEditorModule,
    RouterModule.forChild([{ path: '', component: ProductDetailComponent }]),
  ]
})
export class ProductDetailModule { }
