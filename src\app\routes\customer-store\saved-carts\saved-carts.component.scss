:host {
	padding: 25px;
}

.all-main-title-sec.cart-details-main-title {
	align-items: flex-start;
	justify-content: flex-start;
	text-align: left;
	padding: 0 0 25px 0;

	h1 {
		text-align: left;
		padding: 0;
		display: flex;
		align-items: center;
		gap: 0 10px;
		justify-content: space-between;

		span:not(.material-icons-outlined) {
			font-size: var(--snjy-font-size-0-75);
			color: var(--snjy-color-dark-secondary);
			top: 2px;
			position: relative;
			text-transform: uppercase;
			border: 1px solid #dfdfdf;
			padding: 5px 15px 5px 9px;
			border-radius: 6px;
			background: var(--snjy-font-color-primary);
			font-weight: var(--snjy-font-weight-medium);
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 0 7px;
		}

		.material-icons-outlined {
			width: fit-content;
			height: fit-content;
			font-size: var(--snjy-font-size-1-25);
			color: var(--snjy-button-color-primary);
		}
	}
}

$color_1: var(--snjy-color-dark-secondary);
$color_2: #b7b7b7;
$color_3: #f00;
$color_4: var(--snjy-color-white);
$font-family_1: var(--snjy-font-family);
$font-family_2: "Material Icons Outlined";

.save-c-table {
	margin: 50px 0 30px 0;
	padding: 30px;
	background: var(--snjy-color-white);
	border-radius: 12px;
	border: 1px solid rgba(208, 215, 216, 0.3882352941);
	box-shadow: var(--snjy-box-shadow);
	display: block;

	table.table {
		border-collapse: separate;
		border-spacing: 0 10px;
		margin: 0;

		thead {
			th {
				padding: 17px 10px;
				background: #0077d7;
				border: none;

				.save-c-table-box {
					margin: 0;
					padding: 0;
					font-size: var(--snjy-font-size-0-875);
					font-weight: var(--snjy-font-weight-medium);
					color: $color_4;
					display: flex;
					align-items: center;
					justify-content: flex-start;
					gap: 0 2px;
					line-height: 16px;

					.material-icons-outlined {
						margin: 0;
						padding: 0;
						font-size: var(--snjy-font-size-1-125);
						color: $color_4;
						width: fit-content;
						height: fit-content;
					}
				}
			}
		}

		tbody {
			td {
				padding: 12px 10px 12px 30px;
				background: #f5f5f5;
				border: none;
				font-size: var(--snjy-font-size-0-875);
				color: $color_1;
				font-family: $font-family_1;
				font-weight: var(--snjy-font-weight-medium);
				vertical-align: top;
				position: relative;

				&:before {
					position: absolute;
					content: "";
					font-family: $font-family_2;
					top: 12px;
					left: 8px;
					font-size: var(--snjy-font-size-1-125);
					color: $color_2;
					font-weight: var(--snjy-font-weight-normal);
					line-height: 20px;
					height: -moz-fit-content;
					height: fit-content;
				}

				&:first-child {
					&::before {
						content: "\e7ff";
					}
				}

				&:nth-child(2) {
					&::before {
						content: "\ea67";
					}
				}

				&:nth-child(3) {
					&::before {
						content: "\e161";
					}
				}

				&:nth-child(4) {
					&::before {
						content: "\e873";
					}
				}

				&:nth-child(5) {
					&::before {
						content: "\f045";
					}
				}

				&:nth-child(6) {
					&::before {
						content: "\f071";
					}
				}

				i {
					color: $color_3;
					cursor: pointer;
				}
			}

			.loader {
				td {
					&:first-child {
						&::before {
							content: "";
						}
					}
				}
			}
		}

		tr {
			td {
				&:first-child {
					border-top-left-radius: 8px;
					border-bottom-left-radius: 8px;
				}

				&:last-child {
					border-top-right-radius: 8px;
					border-bottom-right-radius: 8px;
				}
			}

			th {
				&:first-child {
					border-top-left-radius: 8px;
					border-bottom-left-radius: 8px;
				}

				&:last-child {
					border-top-right-radius: 8px;
					border-bottom-right-radius: 8px;
				}
			}
		}
	}

	tbody {
		tr {
			cursor: pointer;
		}
	}
}

.save-c-btns {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 0 8px;

	.btn {
		margin: 0;
		padding: 0;
		position: relative;
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #d32323;
		color: var(--snjy-color-white);

		.material-icons-outlined {
			margin: 0;
			padding: 0;
			height: fit-content;
			width: fit-content;
			font-size: 17px;
		}

		&:first-child {
			background: #ffc55c !important;
			width: 70px !important;
			font-size: var(--snjy-font-size-0-8);
			color: var(--snjy-color-dark-secondary);
			font-weight: var(--snjy-font-weight-medium);
		}
	}
}

.save-c-btn {
	margin: 15px 0 0 0;

	button {
		border-radius: 10px;
		display: flex;
		font-size: var(--snjy-font-size-0-8);
		height: 46px;
		align-items: center;
		justify-content: center;
		min-width: 240px;
		padding-bottom: 0;
		padding-top: 0;
		cursor: pointer;
		color: var(--snjy-color-white);
		background: var(--snjy-button-gradient);
		position: relative;
		margin: 0 auto;
		text-transform: uppercase;
		font-weight: var(--snjy-font-weight-medium);
		transition: all0 0.3s ease-in-out;
		border: none;
	}

}