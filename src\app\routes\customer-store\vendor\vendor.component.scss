.vendor-sec {
  margin: 0;
  text-align: center;
  position: relative;

  .vendor-img {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 40vh);
    position: relative;
    overflow: hidden;

    &:before {
      position: absolute;
      content: "";
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        360deg,
        rgba(0, 0, 0, 0.13) 10%,
        transparent 30%
      );
      z-index: 1;
    }

    video {
      margin: 0;
      padding: 0;
      position: absolute;
      left: 0;
      right: 0;
      width: 100%;
      height: auto;
    }
  }

  .cus-services-body {
    position: relative;
    margin: -80px auto 0 auto;
    padding: 0 25px;
    max-width: 1280px;
    z-index: 99;

    .cus-services-title {
      margin: 0 0 50px 0;
      padding: 0 10px;
      text-align: left;

      h1 {
        font-size: 4rem;
        color: var(--snjy-color-white) !important;
        font-weight: var(--snjy-font-weight-bold);

        span {
          font-size: 3rem;
          color: var(--snjy-color-white) !important;
          display: block;
        }
      }
    }

    .cus-services-list {
      margin: 0;
      position: relative;
      display: flex;
      justify-content: space-between;
      gap: 0 2%;
      z-index: 99;

      .cus-services-box {
        margin: 0;
        padding: 0;
        position: relative;
        flex: 1;
        overflow: hidden;

        .cus-services-box-icon {
          padding: 15px;
          position: relative;
          max-width: 125px;
          width: 100%;
          height: 125px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          background: var(--snjy-button-gradient);
          box-shadow: 0 7px 11px rgba(0, 0, 0, 0.1098039216);
          border-radius: 25px;
          margin: 0 auto;

          img {
            height: 50px;
            width: 50px;
          }

          i {
            font-size: 50px;
          }
        }

        .cus-services-box-text {
          margin: 0;
          padding: 20px 5px;
          position: relative;
          line-height: 22px;
          font-weight: var(--snjy-font-weight-bold);
          font-size: var(--snjy-font-size-0-9);
          color: var(--snjy-color-dark-secondary);
          text-align: center;
          text-transform: uppercase;
        }
      }
    }
  }
}

@media only screen and (max-width: 1024px) {
  .vendor-sec .vendor-img {
    height: 470px !important;
  }
  .vendor-sec .cus-services-body {
    max-width: 930px !important;
    margin: -152px auto 0 auto !important;
    .cus-services-title {
      text-align: center;
    }
    .cus-services-list {
      flex-wrap: wrap !important;
      .cus-services-box {
        .cus-services-box-icon {
          height: 130px !important;
        }
        .cus-services-box-text {
          font-size: var(--snjy-font-size-0-8) !important;
        }
      }
    }
  }
}

@media only screen and (max-width: 800px) {
  .vendor-sec .vendor-img {
    height: 370px !important;
    video {
      width: auto !important;
      height: 100% !important;
      position: relative !important;
    }
  }
  .vendor-sec .cus-services-body {
    margin: -100px auto 0 auto !important;
    .cus-services-title {
      h1 {
        font-size: 2rem !important;
        span {
          font-size: 2rem;
        }
      }
    }
    .cus-services-list {
      .cus-services-box {
        flex: 0 0 30% !important;
      }
    }
  }
}

@media only screen and (max-width: 480px) {
  .vendor-sec .vendor-img {
    height: 300px !important;
  }
  .vendor-sec .cus-services-body {
    margin: -80px auto 0 auto !important;
    .cus-services-title {
      padding: 0 0px !important;
      h1 {
        font-size: 24px !important;
        span {
          font-size: 24px !important;
        }
      }
    }
    .cus-services-list {
      gap: 0 25px !important;
      justify-content: center;
      .cus-services-box {
        flex: 0 0 38% !important;
        .cus-services-box-text {
          line-height: 18px !important;
        }
      }
    }
  }
}
