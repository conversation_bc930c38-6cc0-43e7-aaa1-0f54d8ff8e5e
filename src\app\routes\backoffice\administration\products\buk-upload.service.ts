import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class BukUploadService {
  constructor(private http: HttpClient) {}

  upload(data: any) {
    return this.http
      .post(ApiConstant.BULK_UPLOAD, data)
      .pipe(map((res: any) => res.data));
  }

  exportFileSummary(fileName: string) {
    return this.http.get<Blob>(
      `${ApiConstant.EXPORT_FILE_SUMMARY}/${fileName}`,
      {
        observe: "response",
        responseType: "blob" as "json",
      }
    );
  }

  getStatus() {
    return this.http
      .get(ApiConstant.BULK_UPLOAD_STATUS)
      .pipe(map((res: any) => res.data));
  }

  removeMediaUploadStatus(id: string) {
    return this.http.delete<any>(`${ApiConstant.BULK_UPLOAD_STATUS}/${id}`);
  }
}
