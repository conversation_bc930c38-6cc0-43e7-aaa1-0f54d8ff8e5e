:host {
	padding: 25px;
}

.account-id-sec {
	margin: 0;
	padding: 0;
	position: relative;

	.account-id-body {
		margin: 0;
		padding: 0;
		display: flex;
		justify-content: space-between;
		gap: 0 3%;

		.account-id-info {
			margin: 0;
			padding: 0;
			flex: 4;

			.account-details {
				margin: 0 0 20px 0;
				padding: 24px;
				background: var(--snjy-color-white);
				border: 1px solid var(--snjy-border-color-secondary);
				border-radius: 10px;
				box-shadow: var(--snjy-box-shadow);

				h3 {
					margin: 0 0 15px 0;
					padding: 0;
					position: relative;
					font-size: var(--snjy-font-size-1-125);
					font-weight: var(--snjy-font-weight-bold);
					color: #00216c;
					line-height: 20px;
				}

				ul {
					padding: 34px 30px;
					position: relative;
					list-style: none;
					background: #f0f5f6;
					border-radius: 8px;
					display: flex;
					justify-content: flex-start;
					gap: 40px 4%;
					flex-wrap: wrap;

					li {
						margin: 0;
						padding: 0;
						color: #687491;
						font-weight: var(--snjy-font-weight-medium);
						font-size: var(--snjy-font-size-0-8);
						flex: 0 0 30%;
						display: flex;
						align-items: center;
						justify-content: flex-start;
						flex-wrap: wrap;
						gap: 0 3px;
						line-height: 22px;

						.material-icons-outlined {
							margin: 0;
							padding: 0;
							width: fit-content;
							height: fit-content;
							display: inline-flex;
							align-items: center;
							justify-content: center;
							font-size: var(--snjy-font-size-1-25);
							color: #687491;
						}

						span:not(.material-icons-outlined) {
							margin: 0;
							padding: 0 0 0 22px;
							display: block;
							color: #0077d7;
							font-weight: var(--snjy-font-weight-medium);
							font-size: var(--snjy-font-size-0-8);
							width: 100%;
						}
					}
				}

				.item-box {
					margin: 0 0 18px 0;
					padding: 16px;
					background: #eff4f5;
					display: flex;
					justify-content: flex-start;
					gap: 0 3%;
					position: relative;
					border-radius: 7px;

					.item-box-img {
						margin: 0;
						padding: 0;
						position: relative;
						flex: 0 0 26%;
						height: 144px;
						background-size: contain;
						background-repeat: no-repeat;
						background-position: center;
						border-radius: 7px;
						overflow: hidden;
						border: 1px solid rgba(27, 125, 203, 0.2901960784);
					}

					.item-box-content {
						margin: 0;
						padding: 0;
						position: relative;
						width: 100%;

						h4 {
							margin: 15px 0 0 0;
							padding: 0;
							position: relative;
							font-size: var(--snjy-font-size-1);
							font-weight: var(--snjy-font-weight-medium);
							color: var(--snjy-color-dark-secondary);
							line-height: 20px;
							display: block;
						}

						small {
							margin: 0;
							padding: 0;
							font-size: var(--snjy-font-size-0-75);
							color: #687491;
							font-weight: var(--snjy-font-weight-medium);
						}

						.item-box-bottom-content {
							margin: 35px 0 0 0;
							padding: 0;
							position: relative;
							display: flex;
							align-items: center;
							justify-content: space-between;

							.quantity {
								margin: 0;
								padding: 0 0 0 7px;
								position: relative;
								background: var(--snjy-color-white);
								height: 38px;
								width: 148px;
								display: inline-flex;
								align-items: center;
								justify-content: space-between;
								border-radius: 5px;
								border: 1px solid #ffd383;
								font-size: var(--snjy-font-size-0-8);
								font-weight: var(--snjy-font-weight-medium);
								overflow: hidden;

								span {
									margin: 0;
									padding: 0;
									width: 40px;
									height: 38px;
									background: #ffd383;
									display: inline-flex;
									align-items: center;
									justify-content: center;
									color: var(--snjy-color-dark-secondary);
								}
							}

							.item-price {
								margin: 0;
								padding: 0;
								position: relative;
								font-size: var(--snjy-font-size-2);
								font-weight: var(--snjy-font-weight-bold);
								color: var(--snjy-color-dark-secondary);
								line-height: 22px;
								text-align: right;

								span {
									margin: 0;
									padding: 0;
									position: relative;
									font-size: var(--snjy-font-size-0-75);
									font-weight: var(--snjy-font-weight-medium);
									color: #687491;
									display: block;
								}
							}

						}
					}
				}

			}
		}

		.account-id-summary {
			margin: 0 0 20px 0;
			flex: 2;
			padding: 24px;
			background: var(--snjy-color-white);
			border: 1px solid var(--snjy-border-color-secondary);
			border-radius: 10px;
			box-shadow: var(--snjy-box-shadow);
			position: relative;
			height: fit-content;

			h3 {
				margin: 0 0 3px 0;
				padding: 0;
				position: relative;
				font-size: var(--snjy-font-size-1-125);
				font-weight: var(--snjy-font-weight-bold);
				color: #00216c;
				line-height: 20px;
			}

			small {
				margin: 10px 0 5px 0;
				padding: 0;
				font-size: var(--snjy-font-size-0-875);
				color: #525252;
				display: flex;
				align-items: center;
				gap: 0 6px;
				top: 14px;
				right: 25px;
				font-weight: var(--snjy-font-weight-medium);

				.material-icons-outlined {
					margin: 0;
					padding: 0;
					font-size: var(--snjy-font-size-1);
					width: fit-content;
					height: fit-content;
					color: var(--snjy-button-color-primary);
				}
			}

			.account-summary-price {
				margin: 30px 0 0 0;
			}

			.account-summary-price ul {
				margin: 15px 0 0 0;
				padding: 7px 18px;
				list-style: none;
				border-radius: 8px;
				background: #f0f5f6;

				li {
					margin: 10px 0;
					padding: 0;
					display: flex;
					align-items: center;
					justify-content: space-between;
					gap: 0 10px;
					color: #687491;
					font-weight: var(--snjy-font-weight-medium);
					font-size: var(--snjy-font-size-0-8);

					span {
						color: #0077d7;
					}
				}

				.total-price {
					margin: 5px 0 !important;

					span {
						font-size: var(--snjy-font-size-1-25);
						color: var(--snjy-color-dark-secondary);
						font-weight: var(--snjy-font-weight-bold);
					}
				}

			}

		}
	}
}

.q-account-buttons-container {
	margin: -57px 0 30px auto;
	padding: 0;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	max-width: fit-content;
	gap: 0 20px;

	.q-account-btn {
		font-size: var(--snjy-font-size-0-75);
		color: #4e4e4e;
		top: 2px;
		position: relative;
		text-transform: uppercase;
		border: 1px solid #dfdfdf;
		padding: 8px 15px 8px 9px;
		border-radius: 8px;
		background: var(--snjy-font-color-primary);
		font-weight: var(--snjy-font-weight-medium);
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0 7px;
		box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);
		min-width: 130px;

		.material-icons-outlined {
			width: fit-content;
			height: fit-content;
			font-size: var(--snjy-font-size-1-25);
			color: var(--snjy-button-color-primary);
		}
	}
}

.tracking-d {
	margin: 0;
	padding: 0;
	position: absolute;
	top: 12px;
	right: 0;
	font-size: var(--snjy-font-size-0-875);
	font-weight: var(--snjy-font-weight-medium);
	color: var(--snjy-button-color-primary);
	text-decoration: underline;
	cursor: pointer;
}

$color_1: #b7b7b7;
$color_2: var(--snjy-color-white);
$color_3: var(--snjy-font-color-primary);
$color_4: #b3b3b3;
$color_5: #2da066;
$color_6: #b52c2c;
$font-family_1: "Material Icons Outlined";

.account-s-table {
  margin: 15px 0 0 0;
  padding: 18px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.**********);
  box-shadow: var(--snjy-box-shadow);

  table.table {
    border-collapse: separate;
    border-spacing: 0 10px;
    margin: 0;

    thead {
      td {
        padding: 16px;
        background: #0077d7;
        border: none;

        .account-s-table-box {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-875);
          font-weight: var(--snjy-font-weight-medium);
          color: $color_2;
          line-height: 12px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0 2px;

          .material-icons-outlined {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-1-125);
            color: $color_3;
            width: -moz-fit-content;
            width: fit-content;
            height: -moz-fit-content;
            height: fit-content;
          }
        }
      }
    }

    tbody {
      td {
        padding: 16px 10px 16px 37px;
        background: #f5f5f5;
        border: none;
        position: relative;

        &:before {
          position: absolute;
          content: "";
          font-family: $font-family_1;
          top: 0;
          left: 15px;
          bottom: 0;
          margin: auto 0;
          font-size: var(--snjy-font-size-1-25);
          color: $color_1;
          font-weight: var(--snjy-font-weight-normal);
          line-height: 20px;
          height: fit-content;
        }

        &:first-child {
          &::before {
            content: "\f045";
          }
        }

        &:nth-child(2) {
          &::before {
            content: "\f0c5";
          }
        }

        &:nth-child(3) {
          &::before {
            content: "\ebcc";
          }
        }

        &:nth-child(4) {
          &::before {
            content: "\ef42";
          }
        }

        .account-s-table-box {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-875);
          font-weight: var(--snjy-font-weight-semi-bold);
          color: $color_2;
          line-height: 13px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0 2px;

          .material-icons-outlined {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-1-25);
            color: $color_4;
            width: fit-content;
            height: fit-content;
          }

          .box-checked {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 2px;
            color: $color_5;
            font-weight: var(--snjy-font-weight-medium);

            .material-icons-outlined {
              color: $color_5;
            }
          }

          .box-unchecked {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 2px;
            color: $color_6;
            font-weight: var(--snjy-font-weight-medium);

            .material-icons-outlined {
              color: $color_6;
            }
          }
        }
      }
    }

    tr {
      td {
        &:first-child {
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
        }

        &:last-child {
          border-top-right-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }
    }
  }
}