<div class="table-responsive" [formGroup]="form">
  <table class="table table-striped table-hover">
    <thead>
      <tr>
        <th>
          Role Name
          <span class="text-danger">*</span>
        </th>
        <th>
          Role Type
          <span class="text-danger">*</span>
        </th>
        <th>
          Parent Role Name
          <span class="text-danger">*</span>
        </th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <tr formGroupName="new_role">
        <td>
          <input type="text" class="form-control" placeholder="Enter user role name" formControlName="role_name" />
          <div *ngIf="submitted && newRoleF['role_name'].errors" class="invalid-feedback d-block">
            <span *ngIf="newRoleF['role_name'].errors['required']">
              Role name is required.
            </span>
          </div>
        </td>
        <td>
          <select class="form-control select-arrow-down" formControlName="role_type">
            <option value="">Select Role Type</option>
            <option *ngFor="let type of userRoleTypes" [value]="type.code">
              {{ type.description }}
            </option>
          </select>
          <div *ngIf="submitted && newRoleF['role_type'].errors" class="invalid-feedback d-block">
            <span *ngIf="newRoleF['role_type'].errors['required']">
              Role type is required.
            </span>
          </div>
        </td>
        <td>
          <select class="form-control select-arrow-down" formControlName="parent_role_id">
            <option value="">Select Role</option>
            <ng-container *ngFor="let ur of userRoles">
              <option [value]="ur.id" *ngIf="roleHierarchy.includes(ur.id) || ur.id === loggedInUserRole?.id">
                {{ ur.role_name }}
              </option>
            </ng-container>
          </select>
          <div *ngIf="submitted && newRoleF['parent_role_id'].errors" class="invalid-feedback d-block">
            <span *ngIf="newRoleF['parent_role_id'].errors['required']">
              Parent role name is required.
            </span>
          </div>
        </td>
        <td>
          <button class="btn btn-primary ms-2" (click)="!saving && addRole()" [disabled]="saving">
            <i class="material-icons-outlined">add</i>
          </button>
        </td>
      </tr>
      <ng-container formArrayName="list">
        <ng-container *ngFor="let r of roles.controls; let i = index">
          <tr formGroupName="{{ i }}">
            <td>
              <ng-container *ngIf="!r.get('editing')?.value">
                {{ r.get("role_name")?.value }}
              </ng-container>
              <ng-container *ngIf="r.get('editing')?.value">
                <input type="text" class="form-control" placeholder="Enter user role name"
                  formControlName="role_name" />
                <div *ngIf="
                    r.get('submitted')?.value && r.get('role_name')?.errors
                  " class="invalid-feedback d-block">
                  <span *ngIf="r.get('role_name')?.errors['required']">
                    Role name is required.
                  </span>
                </div>
              </ng-container>
            </td>
            <td>
              <ng-container *ngIf="!r.get('editing')?.value">
                <select class="form-control select-arrow-down" disabled>
                  <option value=""></option>
                  <option *ngFor="let type of userRoleTypes" [value]="type.code"
                    [selected]="r.get('role_type')?.value === type.code">
                    {{ type.description }}
                  </option>
                </select>
              </ng-container>
              <ng-container *ngIf="r.get('editing')?.value">
                <select class="form-control select-arrow-down" formControlName="role_type">
                  <option value="">Select Role Type</option>
                  <option *ngFor="let type of userRoleTypes" [value]="type.code">
                    {{ type.description }}
                  </option>
                </select>
                <div *ngIf="r.get('submitted')?.value && r.get('role_type')?.errors" class="invalid-feedback d-block">
                  <span *ngIf="r.get('role_type')?.errors['required']">
                    Role type is required.
                  </span>
                </div>
              </ng-container>
            </td>
            <td>
              <ng-container *ngIf="!r.get('editing')?.value">
                <select class="form-control select-arrow-down" disabled>
                  <option value=""></option>
                  <option *ngFor="let ur of userRoles" [value]="ur.id"
                    [selected]="r.get('parent_role_id')?.value === ur.id">
                    {{ ur.role_name }}
                  </option>
                </select>
              </ng-container>
              <ng-container *ngIf="r.get('editing')?.value">
                <select class="form-control select-arrow-down" formControlName="parent_role_id">
                  <option value="">Select Role</option>
                  <ng-container *ngFor="let ur of userRoles">
                    <option [value]="ur.id"
                      *ngIf="ur.id !== r.get('id')?.value && (roleHierarchy.includes(ur.id) || ur.id === loggedInUserRole?.id)">
                      {{ ur.role_name }}
                    </option>
                  </ng-container>
                </select>
                <div *ngIf="
                    r.get('submitted')?.value &&
                    r.get('parent_role_id')?.errors
                  " class="invalid-feedback d-block">
                  <span *ngIf="r.get('parent_role_id')?.errors['required']">
                    Parent role name is required.
                  </span>
                </div>
              </ng-container>
            </td>
            <td width="150">
              <div class="d-flex">
                <ng-container *ngIf="!r.get('editing')?.value">
                  <button class="btn btn-primary ms-2" (click)="editRole(r)"
                    *ngIf="roleHierarchy.includes(r.get('id')?.value)">
                    <i class="material-icons-outlined">edit</i>
                  </button>
                  <button class="btn btn-primary ms-2" (click)="!r.get('saving')?.value && deleteRole(r)"
                    [disabled]="r.get('saving')?.value" *ngIf="roleHierarchy.includes(r.get('id')?.value)">
                    <i class="material-icons-outlined">delete</i>
                  </button>
                </ng-container>
                <ng-container *ngIf="r.get('editing')?.value">
                  <button class="btn btn-primary ms-2" (click)="!r.get('saving')?.value && saveRole(r)"
                    [disabled]="r.get('saving')?.value" *ngIf="roleHierarchy.includes(r.get('id')?.value)">
                    <i class="material-icons-outlined">done</i>
                  </button>
                  <button class="btn btn-primary ms-2" (click)="cancelEditing(r, i)">
                    <i class="material-icons-outlined">close</i>
                  </button>
                </ng-container>
                <button class="btn btn-primary ms-2" (click)="openDialog(r.value)"
                  *ngIf="roleHierarchy.includes(r.get('id')?.value)">
                  <i class="material-icons-outlined">manage_accounts</i>
                </button>
              </div>
            </td>
          </tr>
        </ng-container>
      </ng-container>
      <tr *ngIf="!userRoles.length">
        <td colspan="3">No records found.</td>
      </tr>
    </tbody>
  </table>
</div>