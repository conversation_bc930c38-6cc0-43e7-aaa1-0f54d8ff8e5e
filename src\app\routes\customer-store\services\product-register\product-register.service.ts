import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class ProductRegisterService {
  constructor(private http: HttpClient) {}

  // Get a list of all product register
  getAlls(data: any): Observable<any> {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(`${ApiConstant.PRODUCT_REGISTER}`, { params });
  }

  // Get details of a single register product by serial
  getBySerial(serial: string): Observable<any> {
    return this.http.get<any>(`${ApiConstant.PRODUCT_REGISTER}/${serial}`);
  }

  // Create a new product register
  create(productRegister: any): Observable<any> {
    return this.http.post<any>(
      `${ApiConstant.PRODUCT_REGISTER}`,
      productRegister
    );
  }

  // Update an existing product register
  update(serial: string, productRegister: any): Observable<any> {
    return this.http.put<any>(
      `${ApiConstant.PRODUCT_REGISTER}/${serial}`,
      productRegister
    );
  }

  // Delete a product register
  delete(serial: string): Observable<any> {
    return this.http.delete<any>(`${ApiConstant.PRODUCT_REGISTER}/${serial}`);
  }

  // Download Purchase Proof
  downloadPurchaseProof(serial: string): Observable<any> {
    return this.http.get<Blob>(`${ApiConstant.PRODUCT_REGISTER}/${serial}/download-purchase-proof`, {
      observe: "response",
      responseType: "blob" as "json",
    });
  }
}
