<div class="content py-md-0 py-3">
    <div class="sidebar" *ngIf="showFacet">
        <app-facet [categories]="categories" />
        <div class="facet-toggle-container" (click)="showFacet = false;">
            <span class="material-icons-outlined">close</span>
        </div>
    </div>
    <div class="product position-relative">
        <div class="header">
            <div class="all-main-title-sec cart-details-main-title">
                <div class="all-bedcrumbs">
                    <ul>
                        <li>
                            <a [routerLink]="['/store/dashboard']">
                                <span class="material-icons-outlined">home</span> Home
                            </a>
                        </li>
                        <li>Product Catalog</li>
                    </ul>
                </div>
                <h1>Product Catalog</h1>
                <div class="product-id"><span class="material-icons-outlined">inventory_2</span>
                    <span>{{totaleProducts}}</span> Products found
                </div>
            </div>
            <div class="search-c-sec">
                <div class="d-flex align-items-center mb-3 gap-3 justify-content-end">
                    <span class="material-icons-outlined toggle-filter" (click)="toggleFacet()">filter_alt</span>
                    <div class="search-container">
                        <input type="text" class="form-control search" placeholder="Search by part or product name"
                            [formControl]="search" />
                        <span class="material-icons-outlined">search</span>
                    </div>
                </div>
                <ng-container
                    *ngTemplateOutlet="pagination;context:{showRPP: true,showPaginator: false, pager: pager}"></ng-container>
            </div>
        </div>
        <div class="products-container">
            <app-product-view *ngFor="let product of products$ | async" [detail]="product"></app-product-view>
        </div>
        <div class="footer-paginator">
            <ng-container
                *ngTemplateOutlet="pagination;context:{showRPP: false, showPaginator: true, pager: pager}"></ng-container>
        </div>
        <app-loader *ngIf="loading"></app-loader>
    </div>
</div>
<ng-template #pagination let-showRPP="showRPP" let-showPaginator="showPaginator" let-pager="pager">
    <div class="pagination-container">
        <div class="pages" *ngIf="showPaginator">
            <span class="material-icons-outlined" [ngClass]="{disable:pager.currentPage === 1}"
                (click)="setPage(1, true)">arrow_backward</span>
            <span *ngFor="let page of pager.pages" [ngClass]="{selected: pager.currentPage === page}"
                (click)="setPage(page, true)">{{page}}</span>
            <span class="material-icons-outlined" [ngClass]="{disable:pager.currentPage === pager.totalPages}"
                (click)="setPage(pager.totalPages, true)">arrow_forward</span>
        </div>
        <div *ngIf="showRPP" class="rpp-container">
            <span class="rpp">Results per page</span>
            <select [(ngModel)]="searchParam.perPage" class="form-control" (ngModelChange)="setPage(1, true)">
                <option *ngFor="let pageSize of pageSizes" [value]="pageSize">{{pageSize}}</option>
            </select>
        </div>
        <div class="product-l-filter">
            <div class="product-l-filter-box"> Sort by:
                <select class="form-control">
                    <option selected hidden>Choose</option>
                    <option>Ascending</option>
                    <option>Descending</option>
                </select>
            </div>
        </div>
    </div>
</ng-template>