import {
  Component,
  ContentChildren,
  QueryList,
  AfterContentInit,
  Output,
  EventEmitter,
} from "@angular/core";

import { TabComponent } from "./tab.component";

@Component({
  selector: "asar-tabs",
  template: `
    <div class="tabs">
      <ul class="tab-group">
        <li
          *ngFor="let tab of tabs"
          class="tab"
          (click)="selectTab(tab)"
          [class.active]="tab.active"
        >
          {{ tab.title }}
        </li>
      </ul>
      <div class="tab-content-group">
        <div class="tab-content">
          <ng-content></ng-content>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./tabs.component.scss'],
})
export class TabsComponent implements AfterContentInit {
  @ContentChildren(TabComponent) tabs!: QueryList<TabComponent>;
  @Output() activeTab = new EventEmitter<string>();
  
  ngAfterContentInit() {
    let activeTabs = this.tabs.filter((tab) => tab.active);

    if (activeTabs.length === 0) {
      this.selectTab(this.tabs.first);
    }
  }

  selectTab(tab: TabComponent) {
    this.tabs.toArray().forEach((tab) => (tab.active = false));
    this.activeTab.emit(tab.title);
    tab.active = true;
  }
}
