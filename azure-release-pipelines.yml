# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code. 
# Add steps that build, run tests, deploy, and more:  
# https://aka.ms/yaml

trigger:
- production

pool:
  vmImage: ubuntu-latest

variables:
- group: CF_SecretKeys

steps:
- task: CloudFoundryCLIInstall@0
  inputs:
    cfVersion: '6.43.0'

- script: cd '$(System.DefaultWorkingDirectory)'
- script: npm install --legacy-peer-deps
- script: npm run build-prod
- script: sudo apt-get update
- script: cd '$(System.DefaultWorkingDirectory)/dist/ppferls'
- script: cf login -a $(API_ENDPOINT) -u $(USER) -p $(PASSWORD) -o "ASAR AMERICA INC._portaldev-foundry" -s $(SPACE_DEV)
- script: cf push  
  displayName: 'Run a multi-line script'