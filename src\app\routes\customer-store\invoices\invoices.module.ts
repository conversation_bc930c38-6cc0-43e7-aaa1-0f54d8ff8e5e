import { NgModule } from '@angular/core';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { RouterModule } from '@angular/router';

import { InvoicesComponent } from './invoices.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

@NgModule({
  declarations: [
    InvoicesComponent
  ],
  providers: [CurrencyPipe],
  imports: [
    CommonModule,
    SharedModule,
    NgbDatepickerModule,
    RouterModule.forChild([{ path: '', component: InvoicesComponent }]),
  ]
})
export class InvoicesModule { }
