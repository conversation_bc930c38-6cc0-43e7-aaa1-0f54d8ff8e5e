import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class SalesOrderSchedulerService {
  constructor(private http: HttpClient) {}

  getAll(data: any) {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(ApiConstant.SALES_ORDER_SCHEDULER, {
      params,
    });
  }

  salesOrderSchedulerCreation(payload: any): any {
    return this.http.post<any>(
      ApiConstant.SALES_ORDER_SCHEDULER_CREATION,
      payload
    );
  }

  salesOrderSchedulerUpdation(id: any, payload: any): any {
    return this.http.put<any>(
      `${ApiConstant.SALES_ORDER_SCHEDULER_CREATION}/${id}`,
      payload
    );
  }
}
