import {
  Compo<PERSON>,
  ElementRef,
  HostBinding,
  Input,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from "@angular/core";
import { UntypedFormControl } from "@angular/forms";
import { Subscription } from "rxjs";
import { startWith } from "rxjs/operators";

@Component({
  selector: "item-counter",
  templateUrl: "./item-counter.component.html",
  styleUrls: ["./item-counter.component.scss"],
})
export class ItemCounterComponent implements OnInit, OnDestroy {
  @Input() control: UntypedFormControl;
  @Input() min = 1;
  @Input() max!: number;
  @Input() step = 1;
  @Input() allowZero = false;

  @HostBinding("class.readonly") @Input() readonly = false;

  @ViewChild("qty") private input!: ElementRef<HTMLInputElement>;

  private sub!: Subscription;

  ngOnInit() {
    this.sub = this.control.valueChanges
      .pipe(startWith(this.control.value))
      .subscribe((value) =>
        this.control.setValue(this.getValidCount(value), { emitEvent: false })
      );
  }

  changeVal(data: any) {
    if (data != null) {
      this.setInputValue(data);
    }
  }

  setInputValue(data: any) {
    const val = this.getValidCount(data);
    this.control.setValue(val);
    this.input.nativeElement.value = val.toString();
  }

  ngOnDestroy() {
    if (this.sub) {
      this.sub.unsubscribe();
    }
  }

  increment() {
    this.control.setValue(this.control.value + this.step);
    this.control.markAsDirty();
  }

  decrement() {
    this.control.setValue(this.control.value - this.step);
    this.control.markAsDirty();
  }

  private getValidCount(value: number) {
    if (value < this.min && !(value === 0 && this.allowZero)) {
      value = this.min;
    }
    if (this.max && value > this.max) {
      value = this.max;
    }
    return value;
  }
}
