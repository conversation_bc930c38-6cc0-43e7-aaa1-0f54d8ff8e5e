import { Component, OnD<PERSON>roy, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { Subject, takeUntil } from "rxjs";
import moment from "moment";
import { CartService } from "../services/cart.service";
import { OrderHistoryService } from "../order-history/order-history.service";
import { CustomerService } from "../../backoffice/customer/customer.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-checkout-confirmation",
  templateUrl: "./checkout-confirmation.component.html",
  styleUrls: ["./checkout-confirmation.component.scss"],
})
export class CheckoutConfirmationComponent implements OnInit, OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  public moment = moment;
  public order: any = null;
  public customer: any = null;

  constructor(
    public router: Router,
    private _snackBar: AppToastService,
    private cartService: CartService,
    private orderHistoryService: OrderHistoryService,
    private customerService: CustomerService
  ) { }

  ngOnInit(): void {
    this.cartService
      .getCreatedOrder()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((order) => {
        this.order = order;
        if (!this.order) {
          this.router.navigate([`store/catalogues`]);
        } else {
          this.getAllStatus();
          const partnerData = this.order?.to_Partner?.A_SalesOrderHeaderPartnerType;
          if (Array.isArray(partnerData)) {
            this.order.soldToParty = partnerData.find(
              (p: any) => p.PartnerFunction === "SP"
            );
          } else if (partnerData && typeof partnerData === 'object') {
            this.order.soldToParty = partnerData.PartnerFunction === "SP" ? partnerData : null;
          }
          if (this.order?.soldToParty?.Customer) {
            this.getCustomer(this.order?.soldToParty?.Customer);
          }
        }
      });
  }

  getAllStatus() {
    return this.orderHistoryService
      .getAllStatuses()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          if (res.status === "success") {
            const desc = res.data.find(
              (s: any) => s.code === this.order?.OverallDeliveryStatus || ""
            );
            this.order.OverallDeliveryStatus = desc?.description || "";
          }
        },
        error: (e) => {
          console.error(e);
        },
      });
  }

  getCustomer(customerID: string) {
    this.customerService
      .getCustomerByID(customerID)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (value: any) => {
          this.customer = value;
        },
        error: (err) => {
          this._snackBar.open(
            "Error while processing get customer request.", { type: 'Error' }
          );
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
