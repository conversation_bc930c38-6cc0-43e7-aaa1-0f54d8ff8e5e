import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { CheckoutComponent } from './checkout.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

import { AddShippingAddressComponent } from "./add-shipping-address/add-shipping-address.component";

const Routes = [
  { path: "", component: CheckoutComponent },
  { path: "add-shipping-address", component: AddShippingAddressComponent },
];

@NgModule({
  declarations: [CheckoutComponent, AddShippingAddressComponent],
  imports: [
    CommonModule,
    NgbDatepickerModule,
    SharedModule,
    RouterModule.forChild(Routes)
  ]
})
export class CheckoutModule {}
