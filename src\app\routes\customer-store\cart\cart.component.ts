import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  TemplateRef,
} from "@angular/core";
import { debounceTime } from "rxjs";
import { UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { CartService } from "../services/cart.service";
import { SaveCartComponent } from "./save-cart/save-cart.component";
import { SettingsService } from "../../backoffice/settings/settings.service";
import { CurrencyPipe } from "@angular/common";
import { RequestQuoteComponent } from "./request-quote/request-quote.component";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-quantity-input",
  template: `
    <item-counter
      [max]="maxQuantity"
      [control]="$any(addToCartForm.get('quantity'))"
    ></item-counter>
  `,
})
export class QuantityInputComponent implements OnInit {
  @Input() quantity: any;
  @Output() changeQuantity = new EventEmitter<number>();
  public maxQuantity: number = 999;
  public addToCartForm: any = null;

  constructor() {}

  ngOnInit(): void {
    this.addToCartForm = new UntypedFormGroup({
      quantity: new UntypedFormControl(parseInt(this.quantity), {
        updateOn: "blur",
      }),
    });
    this.addToCartForm
      .get("quantity")
      .valueChanges.pipe(debounceTime(750))
      .subscribe((quantity: any) => {
        this.changeQuantity.emit(quantity);
      });
  }
}

@Component({
  selector: "app-cart",
  templateUrl: "./cart.component.html",
  styleUrls: ["./cart.component.scss"],
})
export class CartComponent implements OnInit {
  public sosRes: any = null;
  public cartID: any = null;
  public loading: any = true;
  public isItemRemoving = false;
  public settings: any = null;
  public minQuotePrice = 0;
  public salesQuoteTypeCode: any = null;
  public quoteTextCode: any = null;
  public quateNo = "";
  public requestQuotePrice = "";
  @ViewChild("postAlert", { static: true }) postAlert!: TemplateRef<any>;
  @ViewChild("preAlert", { static: true }) preAlert!: TemplateRef<any>;

  constructor(
    private cp: CurrencyPipe,
    private settingsService: SettingsService,
    private cartService: CartService,
    private dialog: NgbModal,
    private _snackBar: AppToastService
  ) {
    this.cartID = this.cartService.getCartID();
  }

  ngOnInit(): void {
    this.getSettings();
    this.getSalesOrderSimulation();
  }

  async getSalesOrderSimulation() {
    this.isItemRemoving = true;
    this.setToPartner([]);
    this.sosRes = await this.cartService.salesOrderSimulation();
    this.loading = false;
    this.isItemRemoving = false;
  }

  setToPartner(toPartner: any) {
    const sosReq: any = this.cartService.getSOSReq();
    sosReq.to_Partner = toPartner;
    this.cartService.setSOSReq(sosReq);
  }

  deleteProductFromCart(product: any) {
    product.isItemRemoving = true;
    this.cartService.removeFromCart(product.Material);
    this.getSalesOrderSimulation();
  }

  updateCart(quantity: number, product: any) {
    if (quantity > 0) {
      product.RequestedQuantity = quantity;
      this.cartService.updateCart(product);
      this.getSalesOrderSimulation();
    }
  }

  trackBy(index: number, product: any) {
    return product.Material;
  }

  saveCart() {
    if (!this.sosRes?.to_Item?.A_SalesOrderItemSimulationType?.length) {
      return;
    }
    const data = this.sosRes?.to_Item?.A_SalesOrderItemSimulationType.map(
      (item: any) => {
        const newItem: any = {};
        newItem.SalesOrderItemText = item?.SalesOrderItemText;
        newItem.Material = item?.Material;
        newItem.RequestedQuantity = item?.RequestedQuantity;
        newItem.ConditionAmount = item?.base_price;
        newItem.ConditionRateValue = item?.base_price_each;
        newItem.formatted_base_price = item?.formatted_base_price;
        newItem.formatted_base_price_each = item?.formatted_base_price_each;
        return newItem;
      }
    );
    const dialogRef = this.dialog.open(SaveCartComponent);
    dialogRef.componentInstance.data = data;
  }

  clearCart() {
    if (!this.sosRes?.to_Item?.A_SalesOrderItemSimulationType?.length) {
      return;
    }
    const products = this.sosRes?.to_Item?.A_SalesOrderItemSimulationType;
    for (let i = 0; i < products.length; i++) {
      const element = products[i];
      this.cartService.removeFromCart(element.Material);
    }
    this.getSalesOrderSimulation();
  }

  getSettings() {
    this.settingsService.getSettings().subscribe({
      next: (data: any) => {
        this.settings = data;
        this.minQuotePrice = data?.min_quote_price || 0;
        this.salesQuoteTypeCode = data?.sales_quote_type_code || "";
        this.quoteTextCode = data?.quote_text_code || "";
      },
      error: (e) => {
        console.error("Error while processing settings request.", e);
      },
    });
  }

  requestQuoteDialog(): void {
    const dialogRef = this.dialog.open(RequestQuoteComponent);
    dialogRef.componentInstance.sosRes = this.sosRes;
    dialogRef.componentInstance.salesQuoteTypeCode = this.salesQuoteTypeCode;
    dialogRef.componentInstance.quoteTextCode = this.quoteTextCode;
    dialogRef.result.then((res) => {
      this.quateNo = res?.data?.data?.SALESQUOTECREATE?.SalesQuotation || "";
      this._snackBar.open(this.postAlert);
    });
  }

  requestQuote() {
    if (this.sosRes?.total < this.minQuotePrice) {
      const price = this.cp.transform(
        this.minQuotePrice,
        "USD",
        "symbol",
        "1.2-2"
      );
      this.requestQuotePrice = price || "";
      this._snackBar.open(this.preAlert, { type: "Warning" });
    } else {
      this.requestQuoteDialog();
    }
  }
}
