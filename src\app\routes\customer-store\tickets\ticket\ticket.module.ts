import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { TicketComponent } from "./ticket.component";
import { SharedModule } from "src/app/shared/shared.module";
import { NgbTypeaheadModule } from "@ng-bootstrap/ng-bootstrap";

@NgModule({
  declarations: [TicketComponent],
  imports: [
    CommonModule,
    SharedModule,
    NgbTypeaheadModule,
    RouterModule.forChild([{ path: "", component: TicketComponent }]),
  ],
})
export class TicketModule {}



