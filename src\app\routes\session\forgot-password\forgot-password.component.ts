import { Component } from '@angular/core';
import { FormGroup, Validators, FormBuilder, AbstractControl } from '@angular/forms';
import { ForgotPasswordService } from './forgot-password.service';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent {
  form: FormGroup = this.formBuilder.group(
    {
      email: ['', [Validators.required, Validators.email]],
    }
  );
  submitted = false;
  saving = false;

  constructor(
    private formBuilder: FormBuilder,
    private _snackBar: AppToastService,
    public activeModal: NgbActiveModal,
    private service: ForgotPasswordService
  ) { }

  ngOnInit(): void { }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    this.service.forgotPassword(this.form.value).subscribe({
      complete: () => {
        this.onReset();
        this.activeModal.close();
        this.saving = false;
        this._snackBar.open('Reset password link sent successfully!');
      },
      error: (err) => {
        this.saving = false;
        this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });
      },
    })
  }

  onReset(): void {
    this.submitted = false;
    this.form.reset();
  }
}
