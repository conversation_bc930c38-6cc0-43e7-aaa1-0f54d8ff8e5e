import { Component } from "@angular/core";
import { Router } from "@angular/router";
import { ColDef } from "ag-grid-community";
import { Observable, Subject, takeUntil } from "rxjs";
import * as XLSX from "xlsx";
import moment from "moment";

import { AuthService } from "src/app/core/authentication/auth.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { SalesOrderSchedulerService } from "../../services/sales-order-scheduler/sales-order-scheduler.service";
import {
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from "@angular/forms";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SalesOrdersSchedulerComponent } from "src/app/shared/components/sales-orders-scheduler/sales-orders-scheduler.component";

@Component({
  selector: "app-replenishment-history",
  templateUrl: "./replenishment-history.component.html",
  styleUrls: ["./replenishment-history.component.scss"],
})
export class ReplenishmentHistoryComponent {
  private ngUnsubscribe = new Subject<void>();
  public orders: any = [];
  public sellerDetails: any = {};
  public loading = false;
  public submitted = false;
  public filterForm: FormGroup;
  public rowData$!: Observable<any[]>;
  public selectedOrders: any[] = [];

  public columnDefs: ColDef[] = [
    {
      field: "id",
      headerCheckboxSelection: false,
      checkboxSelection: true,
      maxWidth: 36,
    },
    {
      field: "id",
      headerName: "Scheduled Order #",
      headerComponentParams: { menuIcon: "pin" },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell first-column"><i class="material-icons-outlined">pin</i>${data.data.id}</span>`;
      },
      comparator: (valueA, valueB, nodeA, nodeB, isDescending) =>
        parseInt(valueA) - parseInt(valueB),
      sortable: true,
      resizable: true,
      onCellClicked: (data: any) => {
        this.router.navigate([`/store/replenishment/${data.value}`]);
      }
    },
    {
      field: "start_date",
      headerName: "Start Date",
      sortable: true,
      headerComponentParams: { menuIcon: "calendar_month" },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell">
                  <i class="material-icons-outlined">calendar_month</i>
                  ${this.formatDate(data.data.start_date)}
                </span>`;
      },
      resizable: true,
    },
    {
      field: "end_date",
      headerName: "End Date",
      sortable: true,
      headerComponentParams: { menuIcon: "calendar_month" },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell">
                  <i class="material-icons-outlined">calendar_month</i>
                  ${this.formatDate(data.data.end_date)}
                </span>`;
      },
      resizable: true,
    },
    {
      field: "next_order_date",
      headerName: "Next Order Date",
      sortable: true,
      headerComponentParams: { menuIcon: "calendar_month" },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell">
                  <i class="material-icons-outlined">calendar_month</i>
                  ${this.formatDate(data.data.next_order_date)}
                </span>`;
      },
      resizable: true,
    },
    {
      field: "status",
      headerName: "Status",
      headerComponentParams: { menuIcon: "article" },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">article</i>${data.data.status}</span>`;
      },
      resizable: true,
    },
    {
      field: "frequency_format",
      headerName: "Frequency",
      headerComponentParams: { menuIcon: "article" },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell" style="white-space: normal;word-wrap: break-word;">
                  <i class="material-icons-outlined">article</i>${data.data.frequency_format}
                </span>`;
      },
      minWidth: 350,
      resizable: true,
    },
    {
      field: "created_by",
      headerName: "Created by",
      headerComponentParams: { menuIcon: "article" },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">article</i>${data.data.created_by}</span>`;
      },
      resizable: true,
    },
  ];

  constructor(
    private service: SalesOrderSchedulerService,
    private _snackBar: AppToastService,
    public router: Router,
    private dialog: NgbModal,
    public authService: AuthService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit(): void {
    this.createForm();
    this.getOrderHistory();
  }

  createForm() {
    this.filterForm = new FormGroup(
      {
        id: new FormControl(""),
        start_date: new FormControl(null),
        end_date: new FormControl(null),
        status: new FormControl(""),
        schedule_type: new FormControl(""),
      },
      [Validators.required, this.dateRangeValidator]
    );
  }

  // convenience getter for easy access to form fields
  get f(): any {
    return this.filterForm?.controls;
  }

  private dateRangeValidator: ValidatorFn = (): {
    [key: string]: any;
  } | null => {
    let invalid = false;
    const from = this.filterForm && this.filterForm.get("start_date")?.value;
    const to = this.filterForm && this.filterForm.get("end_date")?.value;
    if ((from && !to) || (!from && to)) {
      invalid = true;
    } else if (from && to) {
      invalid = new Date(from).valueOf() > new Date(to).valueOf();
    }
    return invalid ? { invalidRange: { from, to } } : null;
  };

  clear() {
    this.filterForm.patchValue({ id: "" });
    this.filterForm.patchValue({ start_date: null });
    this.filterForm.patchValue({ end_date: null });
    this.filterForm.patchValue({ status: "" });
    this.filterForm.patchValue({ schedule_type: "" });
  }

  getOrderHistory() {
    this.submitted = true;
    if (this.filterForm.invalid) {
      return;
    }
    const value: any = this.filterForm.value;
    const payload: any = {
      perPage: 100,
      sortBy: "created_at",
      sortOrder: "DESC",
    };
    const searchBy = ["customer_id"];
    const search = [this.sellerDetails.customer_id];
    if (value?.id) {
      searchBy.push("id");
      search.push(value?.id);
    }
    if (value?.start_date) {
      searchBy.push("start_date");
      search.push(moment(value?.start_date).format("YYYY-MM-DD"));
    }
    if (value?.end_date) {
      searchBy.push("end_date");
      search.push(moment(value?.end_date).format("YYYY-MM-DD"));
    }
    if (value?.status) {
      searchBy.push("status");
      search.push(value?.status);
    }
    if (value?.schedule_type) {
      searchBy.push("schedule_type");
      search.push(value?.schedule_type);
    }
    if (searchBy.length && search.length) {
      payload.searchBy = searchBy.join(",");
      payload.search = search.join(",");
    }
    this.loading = true;
    return this.service
      .getAll(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (value) => {
          this.submitted = false;
          this.loading = false;
          this.orders = value?.data || [];
        },
        error: () => {
          this.loading = false;
          this.submitted = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  rowToggle(orders: any[]) {
    this.selectedOrders = orders;
  }

  formatDate(input: string) {
    return input ? moment(input).format("MM/DD/YYYY") : "";
  }

  exportToExcel() {
    const fileName = "replenishment-order-history.xlsx";
    const data: any = this.formatData(this.orders);
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "test");
    XLSX.writeFile(wb, fileName);
  }

  formatData(orders: any) {
    return orders.map(
      ({
        id,
        start_date,
        end_date,
        next_order_date,
        status,
        frequency_format,
        created_by,
      }: {
        id: string;
        start_date: string;
        end_date: string;
        next_order_date: string;
        status: string;
        frequency_format: string;
        created_by: string;
      }) => ({
        "Scheduled Order #": id,
        "Start Date": this.formatDate(start_date),
        "End Date": this.formatDate(end_date),
        "Next Order Date": this.formatDate(next_order_date),
        Status: status,
        Frequency: frequency_format,
        "Created By": created_by,
      })
    );
  }

  updateReplenishOrder() {
    const dialogRef = this.dialog.open(SalesOrdersSchedulerComponent, {
      modalDialogClass: "sales-orders-scheduler-modal",
    });
    dialogRef.componentInstance.data = this.selectedOrders[0];
    dialogRef.result.then((res) => {
      if (res.status === "success") {
        this.service
          .salesOrderSchedulerUpdation(this.selectedOrders[0].id, res.data)
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe({
            next: (value: any) => {
              this.selectedOrders = [];
              this._snackBar.open(
                "Replenish order scheduler saved successfully!"
              );
              this.getOrderHistory();
            },
            error: () => {
              this._snackBar.open(
                "Error while processing your update request.",
                {
                  type: "Error",
                }
              );
            },
          });
      }
    });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
