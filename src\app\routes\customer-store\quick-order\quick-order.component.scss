:host {
  padding: 25px;
}

.all-main-title-sec.cart-details-main-title {
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  padding: 0 0 25px 0;

  h1 {
    text-align: left;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 0 10px;
    justify-content: space-between;
  }

  small {
    margin: 0;
    padding: 0;
    font-size: 15px !important;
    color: var(--snjy-color-dark-secondary);

    strong {
      color: var(--snjy-button-color-primary);
    }
  }
}

.q-order-buttons-container {
  margin: -57px 0 30px auto;
  padding: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 440px;

  .q-order-btn {
    font-size: var(--snjy-font-size-0-75);
    color: #4e4e4e;
    top: 2px;
    position: relative;
    text-transform: uppercase;
    border: 1px solid #dfdfdf;
    padding: 8px 15px 8px 9px;
    border-radius: 8px;
    background: var(--snjy-font-color-primary);
    font-weight: var(--snjy-font-weight-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 7px;
    box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);
    min-width: 130px;

    .material-icons-outlined {
      width: fit-content;
      height: fit-content;
      font-size: var(--snjy-font-size-1-25);
      color: var(--snjy-button-color-primary);
    }
  }
}

$color_1: var(--snjy-color-dark-secondary);
$color_2: var(--snjy-color-white);
$color_3: #b7b7b7;
$color_4: #f00;
$font-family_1: var(--snjy-font-family);
$font-family_2: "Material Icons Outlined";

.save-c-table {
  margin: 50px 0 30px 0;
  padding: 30px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);
  display: block;

  table.table {
    border-collapse: separate;
    border-spacing: 0 10px;
    margin: 0;

    thead {
      th {
        padding: 17px 10px;
        background: #0077d7;
        border: none;

        .save-c-table-box {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-875);
          font-weight: var(--snjy-font-weight-medium);
          color: $color_2;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0 2px;
          line-height: 12px;

          .material-icons-outlined {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-1-125);
            color: $color_2;
            width: fit-content;
            height: fit-content;
          }
        }
      }
    }

    tbody {
      td {
        padding: 12px 10px 12px 30px;
        background: #f5f5f5;
        border: none;
        font-size: var(--snjy-font-size-0-875);
        color: $color_1;
        font-family: $font-family_1;
        font-weight: var(--snjy-font-weight-medium);
        vertical-align: middle;
        position: relative;

        input {
          margin: 0;
          padding: 0 11px;
          height: 44px;
          background: var(--snjy-color-white);
          border-radius: 8px;
          font-size: var(--snjy-font-size-0-875);
          font-weight: var(--snjy-font-weight-medium);
          border: 1px solid rgba(134, 153, 169, 0.3803921569);
        }

        button {
          margin: auto 0;
          padding: 0;
          position: absolute;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #d32323;
          color: $color_2;
          border: none;
          border-radius: 5px;
          top: 0;
          right: 17px;
          bottom: 0;
        }

        &:before {
          position: absolute;
          content: "";
          font-family: $font-family_2;
          left: 8px;
          top: 0;
          bottom: 0;
          margin: auto 0;
          font-size: var(--snjy-font-size-1-125);
          color: $color_3;
          font-weight: var(--snjy-font-weight-normal);
          line-height: 20px;
          height: -moz-fit-content;
          height: fit-content;
        }

        &:first-child {
          &::before {
            content: "";
          }
        }

        i {
          color: $color_4;
          cursor: pointer;
        }
      }
    }

    tr {
      td {
        &:first-child {
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
        }

        &:last-child {
          border-top-right-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }

      th {
        &:first-child {
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
        }

        &:last-child {
          border-top-right-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }
    }
  }
}


.save-c-btn {
  margin: 15px 0 0 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0 2%;

  button {
    border-radius: 10px;
    display: flex;
    font-size: var(--snjy-font-size-0-8);
    height: 46px;
    align-items: center;
    justify-content: center;
    min-width: 200px;
    padding-bottom: 0;
    padding-top: 0;
    cursor: pointer;
    color: var(--snjy-color-white);
    background: var(--snjy-button-gradient);
    position: relative;
    margin: 0;
    text-transform: uppercase;
    font-weight: var(--snjy-font-weight-medium);
    transition: all0 0.3s ease-in-out;
    border: none;
    gap: 0 5px;

    .material-icons-outlined {
      width: fit-content;
      height: fit-content;
      font-size: var(--snjy-font-size-1-25);
      color: var(--snjy-color-white);
    }
  }

}

.qty {
  width: 200px;
}

.spinner-border {
  height: 20px;
  width: 20px;
}

@media only screen and (max-width: 1024px) {
  .q-order-buttons-container {
    margin: 0 !important;
    max-width: 100% !important;
  }

  .save-c-table {
    margin: 30px 0 !important;
    padding: 15px;
  }
}

@media only screen and (max-width: 800px) {
  .save-c-btn {
    flex-direction: column !important;
    gap: 10px !important;
  }
}

@media only screen and (max-width: 576px) {
  .q-order-buttons-container .q-order-btn {
    font-size: 10px !important;
    padding: 8px 6px 8px 6px !important;
    gap: 0 5px !important;
    min-width: 110px !important;
    width: 100% !important;

    .material-icons-outlined {
      font-size: var(--snjy-font-size-0-875);
    }
  }
}