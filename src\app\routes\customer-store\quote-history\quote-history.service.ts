import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { map } from "rxjs";

import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class QuoteHistoryService {
  constructor(private http: HttpClient) { }

  getAll(data: any) {
    return this.http.post<any>(`${ApiConstant.SALES_QUOTE}/list`, data);
  }

  getQuoteDetails(data: any) {
    return this.http.post<any>(`${ApiConstant.SALES_QUOTE}/detail`, data);
  }

  getAllStatuses() {
    return this.http.get<any>(ApiConstant.QUOTE);
  }
}
