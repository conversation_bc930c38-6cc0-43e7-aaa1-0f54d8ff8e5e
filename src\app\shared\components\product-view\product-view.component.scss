:host {
    cursor: pointer;
    margin: 0 0 25px 0;
    display: block;
    position: relative;
    background: var(--snjy-font-color-primary);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.0588235294);
    transition: all 0.3s ease-in-out;
    width: 100%;

    &:hover {
        box-shadow: 0 3px 22px #00000021;
    }
}

.main-container {
    .img-container {
        padding: 18px;
        min-height: 210px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        border-radius: 12px;
        will-change: transform;

        &::before {
            position: absolute;
            content: "";
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #efefef;
            z-index: 0;
            mix-blend-mode: multiply;
        }

    }

    button.pro-btn {
        position: absolute;
        padding: 0 28px;
        right: 1rem;
        top: 190px;
        width: fit-content;
        height: 36px;
        background: linear-gradient(60deg, #008eff, #007cde);
        border-radius: 50px;
        color: var(--snjy-color-white);
        font-weight: var(--snjy-font-weight-bold);
        font-size: var(--snjy-font-size-0-875);
        box-shadow: 0 2px 6px #00000040;
        cursor: pointer;
    }

    .pro-details {
        padding: 15px;

        .quantity-container {
            border-top: 1px solid rgba(49, 70, 86, 0.1803921569);
            padding-top: 15px;
        }

        .pro-details-title {
            margin: 15px 0;

            h3 {
                font-size: var(--snjy-font-size-1);
                font-weight: var(--snjy-font-weight-medium);
                color: var(--snjy-color-dark-secondary);
            }

            .pro-extra-c {
                font-size: var(--snjy-font-size-0-8);
                font-weight: var(--snjy-font-weight-medium);
                color: #959595;
            }
        }

        .p-price {
            font-size: var(--snjy-font-size-1-25);
            font-weight: var(--snjy-font-weight-bold);
            color: #008bf9;
            line-height: 18px;

            i {
                color: inherit;
            }
        }
    }
}

button:disabled,
button[disabled] {
    background: #cccccc !important;
    color: #666666 !important;
    cursor: not-allowed !important;
}

:host::ng-deep .custom-loader {
    position: relative !important;
    width: 100% !important;
}