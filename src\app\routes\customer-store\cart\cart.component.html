<div class="all-main-title-sec cart-details-main-title">
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>Shopping Cart</li>
    </ul>
  </div>
  <h1>
    Shopping Cart:
    <span><span class="material-icons-outlined">shopping_bag</span>
      {{ sosRes?.to_Item?.A_SalesOrderItemSimulationType.length || 0 }}
      Items</span>
  </h1>
  <div class="product-id">
    <span class="material-icons-outlined">shopping_cart</span> Cart ID
    <span>{{ cartID }}</span>
  </div>
</div>

<div class="cart-id-sec">
  <div class="cart-id-body">
    <div class="cart-id-info">
      <div class="cart-details">
        <div class="d-flex w-100 h-100 justify-content-center align-items-center my-5 py-5" *ngIf="loading">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        <h3 class="cart-empty" *ngIf="
            !loading && !sosRes?.to_Item?.A_SalesOrderItemSimulationType.length
          ">
          Your cart is empty.
        </h3>
        <div class="item-box" *ngFor="
            let product of sosRes?.to_Item?.A_SalesOrderItemSimulationType;
            trackBy: trackBy
          ">
          <ng-container *ngIf="product.Material | getProductImage | async as url">
            <div class="item-box-img" [ngStyle]="{
              'background-image':
                'url(' + url + ')'
            }"></div>
          </ng-container>
          <div class="item-box-content">
            <a [routerLink]="['/store/product-details', product?.Material]">
              <h4>{{ product?.SalesOrderItemText }}</h4>
              <small>{{ product?.Material }}</small>
            </a>
            <button type="button" class="close-btn" (click)="!isItemRemoving && deleteProductFromCart(product)"
              [disabled]="isItemRemoving" [title]="isItemRemoving ? 'Updating cart' : ''">
              <span class="material-icons-outlined">{{product?.isItemRemoving ? 'sync' : 'close'}}</span>
            </button>
            <div class="item-box-bottom-content">
              <app-quantity-input [quantity]="product?.RequestedQuantity"
                (changeQuantity)="updateCart($event, product)"></app-quantity-input>
              <div class="item-price">
                {{ product?.formatted_base_price }}
                <span>{{ product?.formatted_base_price_each }}
                  each</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="cart-id-summary">
      <div class="cart-buttons-container">
        <button type="button" class="cart-btn" *checkPermission="'P0007'"
          (click)="sosRes?.to_Item?.A_SalesOrderItemSimulationType.length && requestQuote();"
          [disabled]="!sosRes?.to_Item?.A_SalesOrderItemSimulationType.length">
          <span class="material-icons-outlined">request_quote</span>
          Request Quote
        </button>
        <ng-container *checkAppFeature="'F0007'">
          <button type="button" class="cart-btn" [routerLink]="['../saved-cart']" *checkPermission="'P0014'">
            <span class="material-icons-outlined">bookmark_added</span>
            Saved Carts
          </button>
        </ng-container>
        <ng-container *checkAppFeature="'F0007'">
          <button type="button" class="cart-btn"
            (click)="sosRes?.to_Item?.A_SalesOrderItemSimulationType.length && saveCart();"
            [disabled]="!sosRes?.to_Item?.A_SalesOrderItemSimulationType.length" *checkPermission="'P0035'">
            <span class="material-icons-outlined">save</span> Save Cart
          </button>
        </ng-container>
        <button type="button" class="cart-btn"
          (click)="sosRes?.to_Item?.A_SalesOrderItemSimulationType.length && clearCart()"
          [disabled]="!sosRes?.to_Item?.A_SalesOrderItemSimulationType.length">
          <span class="material-icons-outlined">layers_clear</span>
          Clear Cart
        </button>
      </div>
      <div class="cart-id-summary-body">
        <h3>Order Summary</h3>
        <small><span class="material-icons-outlined">motion_photos_on</span>
          Not yet Processed</small>
        <div class="cart-summary-price">
          <ul>
            <li>
              Subtotal
              <span>{{sosRes?.formatted_sub_total || (0 | currency : sosRes?.TransactionCurrency)}}</span>
            </li>
            <li>
              Shipping
              <span>{{0 | currency : sosRes?.TransactionCurrency}}</span>
            </li>
            <li> Tax
              <span>{{0 | currency : sosRes?.TransactionCurrency}}</span>
            </li>
          </ul>
          <ul>
            <li class="total-price">
              Total
              <span>{{sosRes?.formatted_sub_total || (0 | currency : sosRes?.TransactionCurrency)}}</span>
            </li>
          </ul>
          <button type="button" class="btn" [routerLink]="['../checkout']"
            *ngIf="sosRes?.to_Item?.A_SalesOrderItemSimulationType.length">
            <span class="material-icons-outlined">done_all</span> Proceed to Checkout
          </button>
          <button type="button" class="btn btn-light" [routerLink]="['../catalogues']"> <span
              class="material-icons-outlined">local_mall</span> Continue Shopping </button>
          <p class="terms">By placing the order, you agree to our <a [href]="settings?.terms_url" target="_blank">Terms
              & Conditions</a> and our <a [href]="settings?.privacy_policy_url" target="_blank">Privacy Policy</a>.</p>
        </div>
      </div>
      <div class="need-help-sec">
        <span class="material-icons-outlined">wifi_calling_3</span>
        <div class="need-help-details">
          Need Help? <span>Call 1-800-776-8765</span>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #preAlert>
  <p>
    In order to request a quote for your order,
    we kindly inform you that a minimum total of <b>{{requestQuotePrice}}</b> is required.
  </p>
  <p>
    This requirement ensures that we can efficiently process and
    fulfill your quote to meet your expectations.
  </p>
</ng-template>
<ng-template #postAlert>
  <p>Quote: <b>{{quateNo}}</b></p>
  <p>We have received your request succesfully. Thank you for your interest in our product line.</p>
  <p>To provide you with a proposal, one of our advisors will contact you shortly.</p>
</ng-template>