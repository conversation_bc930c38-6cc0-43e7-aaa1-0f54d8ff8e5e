import { Component, ElementRef, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { StoreProductService } from "../services/store.product.service";
import {
  Subject,
  catchError,
  forkJoin,
  map,
  of,
  switchMap,
  takeUntil,
} from "rxjs";
import { CartService } from "../services/cart.service";
import { UntypedFormControl, UntypedFormGroup } from "@angular/forms";
import { AuthService } from "src/app/core/authentication/auth.service";
import { SimilarProductsService } from "../../backoffice/product/similar-products.service";
import { SettingsService } from "../../backoffice/settings/settings.service";
import { AngularEditorConfig } from "@kolkov/angular-editor";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { SalesOrderService } from "../services/sales-order/sales-order.service";

export interface ActiveSlides {
  previous: number;
  current: number;
  next: number;
}

@Component({
  selector: "app-product-detail",
  templateUrl: "./product-detail.component.html",
  styleUrls: ["./product-detail.component.scss"],
  host: { id: "product-detail-page" },
})
export class ProductDetailComponent implements OnInit {
  private ngUnsubscribe = new Subject<void>();
  public similarProducts: any = [];
  public productId!: string;
  public loading = false;
  public loadingImage = false;
  public loadingPrice = false;
  public subject = new Subject();
  public product: any;
  public stock = 0;
  public sub_total = 0;
  public product_price = 0;
  public accPanel = [{ isActive: false }, { isActive: false }];
  public productImages: { [key: string]: any[] } = {};
  public lowStockQty = 0;
  public productResources: { [key: string]: any[] } = {};
  public maxQuantity: number = 999;
  private _activeSlides!: ActiveSlides;
  public classification: any = null;
  public loadingStock = false;
  editorConfig: AngularEditorConfig = {
    editable: false,
    spellcheck: false,
    translate: "no",
    showToolbar: false,
    defaultParagraphSeparator: "p",
    defaultFontName: "Inter",
    toolbarHiddenButtons: [["insertImage"], ["insertVideo"]],
    fonts: [{ class: "inter", name: "Inter" }],
  };

  get activeSlides() {
    return this._activeSlides;
  }

  set activeSlides(activeSlides: ActiveSlides) {
    this._activeSlides = activeSlides;
  }

  addToCartForm = new UntypedFormGroup({
    quantity: new UntypedFormControl(1, { updateOn: "blur" }),
  });

  constructor(
    private route: ActivatedRoute,
    private storeProductService: StoreProductService,
    private cartService: CartService,
    private similarProductsService: SimilarProductsService,
    private authService: AuthService,
    private service: SettingsService,
    private elRef: ElementRef,
    private modalService: NgbModal,
    private salesOrderService: SalesOrderService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe({
      next: (params) => {
        this.productId = params["productId"];
        this.init();
      },
    });
  }

  init() {
    this.similarProducts = [];
    this.stock = 0;
    this.sub_total = 0;
    this.productImages = {};
    this.productResources = {};
    this.addToCartForm.reset();
    this.getSettings();
    this.fetchProducts();
    this.loadImage(this.productId);
    this.loadAvailableStock();
    this.loadSalesPrice();
    this.loadSimilarProducts(this.productId);
    this.getProductClassification();
    this.onChangeProductQuantity();
  }

  onChangeProductQuantity() {
    this.addToCartForm.valueChanges.subscribe(() => {
      const val = this.addToCartForm.controls["quantity"].value;
      if (val) {
        const sosReq = { ...this.cartService.getSOSReq() };
        sosReq.to_Partner = [];
        sosReq.to_Pricing = {};
        sosReq.to_Item = [
          {
            Material: this.productId,
            RequestedQuantity: val.toString(),
            to_PricingElement: [],
          },
        ];
        delete sosReq.User;
        delete sosReq.to_Text;
        delete sosReq.RequestedDeliveryDate;
        this.salesOrderService
          .salesOrderSimulation(sosReq)
          .pipe(takeUntil(this.ngUnsubscribe))
          .subscribe({
            next: (res: any) => {
              const data =
                res?.data?.SALESORDERSIMULATE?.A_SalesOrderSimulationType ||
                null;
              const item =
                data?.to_Item.A_SalesOrderItemSimulationType[0] || null;
              this.product_price = item?.base_price_each || 0;
              this.sub_total = data?.sub_total || 0;
            },
            error: (err: any) => {},
          });
      }
    });
  }

  getSettings() {
    this.service.getSettings().subscribe({
      next: (data) => {
        this.lowStockQty = data?.low_stock_qty || 0;
      },
      error: (e) => {
        console.error("Error while processing settings request.", e);
      },
    });
  }

  fetchProducts() {
    this.loading = true;
    this.storeProductService.getById(this.productId).subscribe({
      next: (res) => {
        this.product = res?.data || null;
        this.loading = false;
      },
      error: (e) => {
        this.loading = false;
        console.error("Error while processing GET product request.", e);
      },
    });
  }

  getProductClassification() {
    this.storeProductService
      .getProductClassification(this.productId)
      .subscribe({
        next: (res: any) => {
          const data = res?.data || [];
          this.classification = data.reduce((r: any, a: any) => {
            r[a.class_type_descr] = r[a.class_type_descr] || [];
            r[a.class_type_descr].push(a);
            return r;
          }, Object.create(null));
        },
        error: (e) => {
          console.error("Error while processing classification request.", e);
        },
      });
  }

  getPreviousCurrentNextIndexes(index: number): ActiveSlides {
    const images = this.productImages["1200X1200"];

    return {
      previous: (index === 0 ? images?.length - 1 : index - 1) % images?.length,
      current: index % images?.length,
      next: (index === images?.length - 1 ? 0 : index + 1) % images?.length,
    };
  }

  selectImage(index: number): void {
    this.activeSlides = this.getPreviousCurrentNextIndexes(index);
    setTimeout(() => {
      let el = this.elRef.nativeElement.querySelector("img.active");
      el && el.scrollIntoViewIfNeeded();
    });
  }

  loadImage(pid: string) {
    this.loadingImage = true;
    this.storeProductService.getMedia(pid).subscribe({
      next: (res) => {
        const data = res?.data || [];
        this.productImages = this.groupBy(
          data.filter((o: any) => o.media_type !== "SPECIFICATION"),
          "dimension"
        );
        this.productResources = this.groupBy(res.data, "media_type");
        this.loadingImage = false;
        this.activeSlides = this.getPreviousCurrentNextIndexes(0);
      },
      error: (err) => {},
    });
  }

  loadAvailableStock() {
    this.loadingStock = true;
    this.storeProductService.getMaterialStock(this.productId).subscribe({
      next: (value) => {
        if (value?.data?.materialstock?.stock) {
          this.stock = value?.data?.materialstock?.stock;
        }
        this.loadingStock = false;
      },
    });
  }

  loadSalesPrice() {
    this.loadingPrice = true;
    const userDetail = this.authService.userDetail.partner_function;
    const obj = {
      SalesOrg: userDetail.sales_organization,
      Dist_Channel: userDetail.distribution_channel,
      Division: userDetail.division,
      SoldTo: userDetail.bp_customer_number,
      Products: {
        Material: this.productId,
      },
    };
    this.storeProductService.getSalesPrice(obj).subscribe({
      next: (value) => {
        this.loadingPrice = false;
        if (value?.data?.ConditionRateValue) {
          this.product_price = value.data.ConditionRateValue;
          this.sub_total = value.data.ConditionRateValue;
        }
      },
      error: (err) => {
        this.loadingPrice = false;
      },
    });
  }

  loadSimilarProducts(productId: string) {
    this.similarProductsService
      .get(productId)
      .pipe(
        switchMap((value) => {
          const reqs = [];
          if (value?.data?.length) {
            const similarProducts = [
              ...new Map(
                value.data.map((item: any) => [
                  item["similar_product_id"],
                  item,
                ])
              ).values(),
            ];
            for (let i = 0; i < similarProducts.length; i++) {
              const element: any = similarProducts[i];
              const req = this.storeProductService
                .getById(element.similar_product_id)
                .pipe(map((value) => value.data));
              reqs.push(req);
            }
          }
          return forkJoin(reqs);
        }),
        switchMap((value: any) => {
          if (value?.length) {
            const priceReqs = [];
            for (let j = 0; j < value.length; j++) {
              const product = value[j];
              const userDetail = this.authService.userDetail.partner_function;
              const obj = {
                SalesOrg: userDetail.sales_organization,
                Dist_Channel: userDetail.distribution_channel,
                Division: userDetail.division,
                SoldTo: userDetail.bp_customer_number,
                Products: {
                  Material: product.product_id,
                },
              };
              const priceReq = this.storeProductService.getSalesPrice(obj).pipe(
                map((res) => {
                  if (res?.data) {
                    return {
                      ...product,
                      ...res.data,
                    };
                  }
                  return product;
                }),
                catchError((err) => {
                  return of(product);
                })
              );
              priceReqs.push(priceReq);
            }
            return forkJoin(priceReqs);
          }
          return of(value);
        })
      )
      .subscribe((data) => {
        this.similarProducts = data;
      });
  }

  groupBy(input: any[], key: string) {
    return input.reduce((acc, currentValue) => {
      let groupKey = currentValue[key];
      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(currentValue);
      return acc;
    }, {});
  }

  addToCart(product: any) {
    let addToCart = {
      Material: product.product_id,
      RequestedQuantity: this.addToCartForm?.value?.quantity || 1,
    };
    this.cartService.setMaterial(product.product_id);
    this.cartService.addToCart(addToCart);
  }

  toggleAccordian(data: any, event: any, index: number) {
    const element = event.target;
    element.classList.toggle("active");
    if (data[index]?.isActive) {
      data[index].isActive = false;
    } else {
      data[index].isActive = true;
    }

    const panel = element.parentElement.nextElementSibling;
    if (panel.classList.contains("panel-details")) {
      panel.classList.remove("panel-details");
    } else if (panel.style.maxHeight) {
      panel.style.maxHeight = null;
    } else {
      panel.style.maxHeight = panel.scrollHeight + "px";
    }
  }

  expand(url: string, content: any) {
    this.modalService
      .open(content, {
        fullscreen: true,
        modalDialogClass: "product-zoom-modal",
      })
      .result.then(
        (result) => {},
        (reason) => {}
      );
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
