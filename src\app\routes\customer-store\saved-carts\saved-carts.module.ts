import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { SavedCartsComponent } from './saved-carts.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { RestoreCartComponent } from './restore-cart/restore-cart.component';

@NgModule({
  declarations: [SavedCartsComponent, RestoreCartComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: '', component: SavedCartsComponent }]),
  ]
})
export class SavedCartsModule { }
