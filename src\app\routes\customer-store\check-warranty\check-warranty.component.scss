.order-status-sec {
  margin: 0;
  padding: 0;
  position: relative;

  .order-status-body {
    margin: 0 auto;
    padding: 0 25px 50px 25px;
    position: relative;
    max-width: 100%;

    .order-contact-list {
      margin: 0;
      padding: 18px 20px;
      background: #e5ecf3;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 12px;
      border: 1px solid rgba(208, 215, 216, 0.3882352941);
      box-shadow: var(--snjy-box-shadow);

      .order-c-box {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0 10px;
        flex: 1;

        &.address-box {
          flex: 0 0 42% !important;
        }

        .order-c-icon {
          margin: 0;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 44px;
          height: 44px;
          background: var(--snjy-font-color-primary);
          border-radius: 50px;
          box-shadow: 0 1px 3px rgba(97, 134, 177, 0.1882352941);
        }

        .order-c-details {
          margin: 0;
          padding: 6px 0 0 0;

          h4 {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-8);
            text-transform: uppercase;
            font-weight: var(--snjy-font-weight-bold);
            color: var(--snjy-button-color-primary);
            line-height: 13px;
          }

          small {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-8);
            text-transform: uppercase;
            font-weight: var(--snjy-font-weight-medium);
            color: #2e3237;
            line-height: 20px;
          }
        }
      }
    }
  }
}

.order-status-form {
  margin: 15px 0 0 0;
  padding: 30px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);

  h3 {
    margin: 0 0 15px 0;
    padding: 0;
    position: relative;
    font-size: var(--snjy-font-size-1-125);
    font-weight: var(--snjy-font-weight-bold);
    color: #00216c;
    line-height: 20px;
  }

  .form {
    display: flex;
    justify-content: space-between;
    gap: 0 2%;
    flex-wrap: wrap;

    &.warranty-form {
      flex: 1 !important;
    }

    .form-group {
      -webkit-box-flex: 0;
      flex: 1;
      margin: 0 0 15px 0;
      position: relative;

      &:before {
        position: absolute;
        content: "";
        font-family: "Material Icons Outlined";
        top: 39px;
        right: 10px;
        bottom: 0;
        margin: auto 0;
        font-size: var(--snjy-font-size-1-25);
        color: var(--snjy-color-dark-secondary);
        font-weight: var(--snjy-font-weight-normal);
        line-height: 23px;
        color: #b7b7b7;
      }

      textarea.form-control {
        height: 120px;
      }

      .form-control::placeholder {
        color: #a1a1a1;
        font-weight: var(--snjy-font-weight-semi-bold);
      }

      select.form-control {
        -webkit-appearance: none;
      }

      label {
        margin: 0 0 8px 0;
        padding: 0;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        color: var(--snjy-color-dark-secondary);
        line-height: 14px;
        display: flex;
        align-items: center;
        gap: 0 2px;

        .material-icons-outlined {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-1-25);
          color: #b3b3b3;
          width: fit-content;
          height: fit-content;
        }
      }

      .form-control {
        margin: 0;
        padding: 0 11px;
        height: 44px;
        background: #f0f7ff;
        border-radius: 8px;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        border: 1px solid #8699a961;
      }
    }

    .form-btn-sec {
      margin: 15px 0 0 0;
      flex: 0 0 100%;
      max-width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
      gap: 0 2%;

      .order-s-btn {
        border-radius: 10px;
        display: flex;
        font-size: var(--snjy-font-size-0-875);
        height: 48px;
        align-items: center;
        justify-content: center;
        line-height: 14px;
        min-width: 263px;
        padding-bottom: 0;
        padding-top: 0;
        cursor: pointer;
        color: var(--snjy-color-white);
        background: var(--snjy-button-gradient);
        position: relative;
        text-transform: uppercase;
        font-weight: var(--snjy-font-weight-medium);
        transition: all0 0.3s ease-in-out;
        border: none;
      }
    }
  }
}

.display-warranty {
  margin: 15px 0 0 0;
  padding: 30px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);
}