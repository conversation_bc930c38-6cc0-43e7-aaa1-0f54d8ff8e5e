.image-dimenssion {
  width: 120px;
}

.thumbnail {
  background-color: white;
  width: 116px;
  height: 116px;
  border-radius: 10px;

  >i {
    font-size: var(--snyj-font-size-5);
    margin: 30px;
  }

  img {
    height: auto;
    max-height: 100%;
    aspect-ratio: auto;
    width: auto;
    max-width: 100%;
  }
}

.remove-image {
  top: -10px;
  right: -10px;
  border-radius: 10em;
  padding: 2px 6px 3px;
  text-decoration: none;
  font: 700 21px/20px sans-serif;
  background: #555;
  border: 3px solid var(--snjy-color-white);
  color: var(--snjy-color-white);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5), inset 0 2px 4px rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  -webkit-transition: background 0.5s;
  transition: background 0.5s;
}

.remove-image:hover {
  background: #E54E4E;
  padding: 3px 7px 5px;
  top: -11px;
  right: -11px;
}

.remove-image:active {
  background: #E54E4E;
  top: -10px;
  right: -11px;
}

.last-cat {
  padding-top: 4px;
  display: block;
}

::ng-deep {
  .mat-mdc-form-field-subscript-wrapper {
    display: none !important;
  }

  .mat-mdc-form-field-infix {
    min-height: auto !important;
    padding: 6px 0 !important;
  }

  .mat-mdc-form-field {
    width: 100%;
  }

  .mdc-text-field--filled {
    background-color: white !important;
    border: 1px solid #ced4da;
  }
}

.cat-hierarchy {
  padding-top: 5px;
  font-weight: var(--snjy-font-weight-bold);
  margin-right: 15px;
}

.form-check {
  margin-top: 10px;

  input {
    cursor: pointer;
  }

  .form-check-label {
    cursor: pointer;
    padding: 2px 0 0 !important;

    &::before {
      content: '' !important;
    }
  }
}