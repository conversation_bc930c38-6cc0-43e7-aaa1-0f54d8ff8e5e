<div class="warranty-range">
    <div *ngFor="let warranty of warrantyPeriods" class="warranty-point" [style]="getWidth(warranty)">
        <div class="start">
            <p>Started</p>
            <p>{{ moment(warranty.startDate).format('MMM YYYY') }}</p>
        </div>
        <div class="end" *ngIf="!warranty.stilRuning">
            <p>Ended</p>
            <p>{{ moment(warranty.endDate).format('MMM YYYY') }}</p>
        </div>
        <div class="end" *ngIf="warranty.stilRuning">
            <p>Running</p>
        </div>
        <span class="text-capitalize">{{ warranty.name }}</span>
    </div>
</div>