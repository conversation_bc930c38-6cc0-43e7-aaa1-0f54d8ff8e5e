<div class="bck-table-details mt-2">
    <div class="d-flex justify-content-between">
        <span (click)="toogleDetails()" class="expand-title d-flex align-items-center">
            <i class="material-icons-outlined">{{cardOpen ? 'expand_more': 'chevron_right'}}</i>&nbsp;{{
            'form.details' | translate }}
        </span>
        <ng-container *ngIf="cardOpen">
            <button class="btn btn-primary" (click)="submitForm()" *ngIf="aciveTab != 'History' || submitted">
                {{ 'form.action.submit' | translate }}
            </button>
        </ng-container>
    </div>
    <div *ngIf="cardOpen">
        <asar-tabs (activeTab)="setActiveform($event)">
            <asar-tab [tabTitle]="'General'">
                <form [formGroup]="generalForm">
                    <div class="row">
                        <div class="col-md-6 form-group">
                            <label>Name</label>
                            <input class="form-control" type="text" formControlName="name" />
                            <span class="text-error" *ngIf="f.name?.touched && f.name.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                        <div class="col-md-6 form-group">
                            <label>Language</label>
                            <select class="form-select" formControlName="language">
                                <option value="English">
                                    English
                                </option>
                            </select>
                            <span class="text-error" *ngIf="f.language?.touched && f.language.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                        <div class="col-md-6 form-group">
                            <label>WebStore</label>
                            <input class="form-control" type="text" formControlName="web_store" />
                            <span class="text-error" *ngIf="f.web_store?.touched && f.web_store.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                        <div class="col-md-6 form-group">
                            <label>Status</label>
                            <div class="radio-container">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" value="Active" name="status"
                                        formControlName="status">
                                    <label class="form-check-label">Active</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" value="Inactive" name="status"
                                        formControlName="status">
                                    <label class="form-check-label">Inactive</label>
                                </div>
                            </div>
                            <span class="text-error" *ngIf="f.status?.touched && f.status.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                    </div>
                </form>
            </asar-tab>
            <asar-tab [tabTitle]="'Categories'">
                <form [formGroup]="categoriesForm">
                    <div class="row">
                        <div class="col-12 form-group">
                            <label>Categories</label>
                            <div class="d-flex align-items-start">
                                <select class="form-select h-100" formControlName="selectedCategories" multiple="true">
                                    <ng-container *ngFor="let option of categories">
                                        <option [value]="option.id" *ngIf="!option.parent_category_id">
                                            {{option.name}}
                                        </option>
                                    </ng-container>
                                </select>
                                <button class="btn btn-primary mx-2" (click)="addCategory()">
                                    <i class="material-icons-outlined">add</i>
                                </button>
                            </div>
                            <label>Selected Categories</label>
                            <table class="table table-striped table-hover" *ngIf="f.categories?.value">
                                <tbody>
                                    <tr *ngFor="let category of f.categories?.value">
                                        <td>{{getCategoryName(category)}}</td>
                                        <td>
                                            <button class="btn btn-primary float-end" (click)="removeCategory(category)">
                                                <i class="material-icons-outlined">delete</i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <span class="text-error" *ngIf="f.categories?.touched && f.categories.hasError('required')">
                                {{ 'validation.required' | translate }}
                            </span>
                        </div>
                    </div>
                </form>
            </asar-tab>
            <asar-tab [tabTitle]="'History'">
                <div class="row">
                    <div class="col-md-6 form-group">
                        <label> Created On</label>
                        <p *ngIf="model.created_at">{{moment(model.created_at, 'YYYY-MM-DD
                            HH:mm:SS').format('MM/DD/YYYY HH:mm:SS')}}</p>
                    </div>
                    <div class="col-md-6 form-group">
                        <label> Created By</label>
                        <p>{{model.created_by}}</p>
                    </div>
                    <div class="col-md-6 form-group">
                        <label> Updated On</label>
                        <p *ngIf="model.updated_at">{{moment(model.updated_at, 'YYYY-MM-DD
                            HH:mm:SS').format('MM/DD/YYYY HH:mm:SS')}}</p>
                    </div>
                    <div class="col-md-6 form-group">
                        <label> Updated By</label>
                        <p>{{model.updated_by}}</p>
                    </div>
                </div>
            </asar-tab>
        </asar-tabs>
    </div>
</div>