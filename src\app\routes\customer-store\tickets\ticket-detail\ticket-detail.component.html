<div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading...</span>
  </div>
</div>
<div class="all-main-title-sec product-details-main-title" *ngIf="!loading">
  <h1>Ticket Details</h1>
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>
        <a [routerLink]="['/store/customer-services']">
          <span class="material-icons-outlined">support_agent</span> Customer Service
        </a>
      </li>
      <li>
        <a [routerLink]="['/store/tickets']">
          <span class="material-icons-outlined">confirmation_number</span>
          Ticket Status
        </a>
      </li>
      <li>Ticket Details</li>
    </ul>
  </div>
</div>

<div class="ticket-id-sec" *ngIf="!loading">
  <div class="ticket-id-body">
    <div class="ticket-id-info">
      <div class="ticket-details">
        <ul>
          <li>
            <span class="material-icons-outlined">tag</span>
            Ticket ID
            <span>{{ ticket?.TICKET_ID }}</span>
          </li>
          <li>
            <span class="material-icons-outlined">pin</span>
            Subject
            <span>{{ ticket?.SUBJECT }}</span>
          </li>
          <li>
            <span class="material-icons-outlined">pin</span>
            Customer Name
            <span>{{ sellerDetails?.name }}</span>
          </li>
          <li>
            <span class="material-icons-outlined">person_outline</span>
            Customer#
            <span>{{ sellerDetails?.bp_customer_number }}</span>
          </li>
          <li>
            <span class="material-icons-outlined">person_outline</span>
            Date Created
            <span *ngIf="ticket?.CREATED_ON">
              {{ moment(ticket?.CREATED_ON).format("MM/DD/YYYY") }}
            </span>
          </li>
          <li>
            <span class="material-icons-outlined">
              confirmation_number
            </span>
            Status <span>{{ ticket?.STATUS_TEXT }}</span>
          </li>
          <li>
            <span class="material-icons-outlined">event_note</span>
            Category
            <span>{{ ticket?.CATEGORY }}</span>
          </li>
          <li>
            <span class="material-icons-outlined">event_note</span>
            Associated To
            <span>{{ ticket?.ASSIGNED_TO }}</span>
          </li>
        </ul>
      </div>
      <div class="ticket-details">
        <div class="t-messages">
          <h4>Message Trail</h4>
          <button type="button" class="add-message-btn" (click)="openAddMessageDialog()">
            <span class="material-icons-outlined">add_comment</span>
            Message
          </button>
        </div>
        <div class="t-messages-box">
          <div class="row w-100">
            <div class="col-md-6">
              <div class="t-messages-box-body mb-2" *ngIf="!ticket?.NOTES || !ticket?.NOTES?.length">
                <h3 class="mb-0">No messages</h3>
              </div>
              <ng-container *ngFor="
                  let note of ticket?.NOTES | SortDescByDate : 'CREATED_ON'
                ">
                <div class="t-messages-box-body mb-2">
                  <h3 class="mb-0">
                    {{ note?.CREATED_BY || auth?.display_name }}
                    <span>
                      on
                      {{
                      moment
                      .utc(note?.CREATED_ON)
                      .local()
                      .format("MM/DD/YYYY HH:mm:ss A")
                      }}
                    </span>
                  </h3>
                  <div class="m-details">
                    {{ note?.TEXT }}
                  </div>
                </div>
              </ng-container>
            </div>
            <div class="col-md-6">
              <div class="t-messages-box-table">
                <h3>Attachments</h3>
                <div class=" table-responsive">
                  <table class="table all-table">
                    <thead>
                      <tr>
                        <th>
                          <div class="invoice-table-box"><span class="material-icons-outlined">pin</span> #</div>
                        </th>
                        <th>
                          <div class="invoice-table-box"><span class="material-icons-outlined">article</span> Name</div>
                        </th>
                        <th>
                          <div class="invoice-table-box"><span class="material-icons-outlined">edit_note</span> Created
                            On
                          </div>
                        </th>
                        <th>
                          <div class="invoice-table-box"><span class="material-icons-outlined">person_add_alt</span>
                            Created By</div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngIf="
                        !ticket?.ATTACHMENTS || !ticket?.ATTACHMENTS?.length
                      ">
                        <td colspan="4">No Attachments</td>
                      </tr>
                      <ng-container *ngFor="
                        let attachment of ticket?.ATTACHMENTS
                          | SortDescByDate : 'CREATED_ON';
                        let i = index
                      ">
                        <tr>
                          <td class="text-truncate" data-label="#" scope="row">{{ i + 1 }}</td>
                          <td class="text-truncate" data-label="Name">
                            <a href="javascript:void(0);" class="text-truncate" (click)="
                              !attachment.downloading &&
                                downloadAttachment(
                                  ticket?.TICKET_ID,
                                  attachment
                                )
                            ">
                              {{ attachment?.NAME }}
                              <span class="fa fa-spinner fa-spin mx-2" *ngIf="attachment?.downloading"></span>
                            </a>
                          </td>
                          <td class="text-truncate" data-label="Created On">
                            {{
                            moment
                            .utc(attachment?.CREATED_ON)
                            .local()
                            .format("MM/DD/YYYY HH:mm:ss A")
                            }}
                          </td>
                          <td class="text-truncate" data-label="Created By">{{ attachment?.CREATED_BY }}</td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>