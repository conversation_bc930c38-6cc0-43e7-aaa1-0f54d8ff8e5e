<div class="d-flex flex-column flex-shrink-0 p-3 bg-light right-sidebar position-fixed top-0 end-0 h-100"
  *ngIf="isExapndable">
  <app-loader *ngIf="loading"></app-loader>
  <ng-container *ngIf="!loading && product">
    <a href="javascript:void(0);"
      class="d-flex align-items-center mb-3 mb-md-0 me-md-auto link-dark text-decoration-none">
      <span class="material-icons-outlined shopping-cart-icon">shopping_cart</span>
      <small class="fs-4 m-0 p-0">{{ product?.SalesOrderItemText }} <span class="d-block">was added to your cart</span></small>
    </a>
    <hr />
    <div class="item-box d-flex justify-content-start position-relative overflow-hidden">
      <div class="item-box-img m-0 p-0 position-relative"
        [ngStyle]="{ 'background-image': 'url(' + (product?.Material | getProductImage | async) + ')' }"></div>
      <div class="item-box-content m-0 p-0 position-relative w-100">
        <a [routerLink]="['/store/product-details', product?.Material]">
          <h4 class="desc text-truncate p-0 position-relative d-block" [title]="product?.SalesOrderItemText">{{ product?.SalesOrderItemText }}</h4>
          <div class="item-box-list d-flex flex-column">
            <small class="m-0 p-0">Material No: <span>{{ product?.Material }}</span><br /></small>
            <small class="m-0 p-0">Item Price: <span>{{ product?.formatted_base_price_each }}</span></small>
          </div>
          <small class="m-0 p-0">Unit: <span>EACH</span></small>
          <div class="quantity position-relative d-flex align-items-center justify-content-between overflow-hidden"> Qty: <span class="m-0 d-inline-flex align-items-center justify-content-center">{{ product?.RequestedQuantity }}</span></div>
          <div class="item-price p-0 position-relative"> {{ product?.formatted_base_price }}
          </div>
        </a>
      </div>
    </div>
    <button type="button" class="btn" (click)="reset()"> <span class="material-icons-outlined">local_mall</span>
      Continue Shopping </button>
    <button type="button" class="btn btn-light" (click)="goToCart()"> <span
        class="material-icons-outlined">done_all</span> View Cart and Checkout</button>
  </ng-container>
</div>