import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  CanActivateChild,
  Router,
  RouterStateSnapshot,
} from "@angular/router";

import { AppFeatureService } from "../services/app-feature/app-feature.service";

@Injectable({
  providedIn: "root",
})
export class TurnOnFeaturesGuard implements CanActivateChild {
  constructor(
    private router: Router,
    private appFeatureService: AppFeatureService
  ) {}
  async canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<any> {
    return await this.authenticate(childRoute);
  }

  private async authenticate(route: ActivatedRouteSnapshot): Promise<any> {
    const routeData: any = route?.data || null;
    const feature: any = routeData?.feature || null;
    let features = this.appFeatureService.enabledFetures;
    if (!features.length) {
      features = await this.appFeatureService.getTurnOnFetures();
    }
    if (feature !== null && !features.includes(feature)) {
      return this.router.parseUrl("/store/dashboard");
    }
    return true;
  }
}
