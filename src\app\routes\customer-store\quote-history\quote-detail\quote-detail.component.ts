import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { AppToastService } from "src/app/shared/services/toast.service";
import { Subject, forkJoin, takeUntil } from "rxjs";
import moment from "moment";

import { QuoteHistoryService } from "../quote-history.service";
import { AuthService } from "src/app/core/authentication/auth.service";
import { SettingsService } from "src/app/routes/backoffice/settings/settings.service";

@Component({
  selector: "app-quote-detail",
  templateUrl: "./quote-detail.component.html",
  styleUrls: ["./quote-detail.component.scss"],
})
export class QuoteDetailComponent implements OnInit, OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  public moment = moment;
  public loading = false;
  public quoteID: any = null;
  public quote: any = null;
  public sellerDetails: any = {};
  public statuses: any = [];

  constructor(
    private _snackBar: AppToastService,
    private activatedRoute: ActivatedRoute,
    public quoteHistoryService: QuoteHistoryService,
    public authService: AuthService,
    private settingsService: SettingsService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit() {
    this.activatedRoute.paramMap.subscribe((params) => {
      const id = params.get("quote_id");
      if (id) {
        this.quoteID = id;
        this.loading = true;
        this.getSettings();
      }
    });
  }

  getSettings() {
    this.settingsService
      .getSettings()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          this.getQuoteDetails(data?.sales_quote_type_code);
        },
        error: (e: any) => {
          console.error("Error while processing settings request.", e);
        },
      });
  }

  getQuoteDetails(docType: string) {
    // Get all quote status
    const status$ = this.quoteHistoryService.getAllStatuses();
    // Get quote detail
    const payload: any = {};
    payload.SD_DOC = this.quoteID;
    payload.DOC_TYPE = docType || "";
    const quote$ = this.quoteHistoryService.getQuoteDetails(payload);
    forkJoin([status$, quote$])
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (result: any) => {
          this.statuses = result[0]?.data || [];
          this.quote = result[1]?.data?.SALESQUOTE || null;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  getStatusName(code: string) {
    const status = this.statuses.find((o: any) => o.code === code);
    if (status) {
      return status.description;
    }
    return "";
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
