import { Component, Input } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Subject, takeUntil } from "rxjs";

import { UserRoleService } from "../user-role.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { RolesType } from "src/app/constants/api.constants";

@Component({
  selector: "app-user-role-permissions",
  templateUrl: "./user-role-permissions.component.html",
  styleUrls: ["./user-role-permissions.component.scss"],
})
export class UserRolePermissionsComponent {
  private ngUnsubscribe = new Subject<void>();
  @Input() data: any = null;
  public permissions: any[] = [];
  public saving: any = false;

  constructor(
    public activeModal: NgbActiveModal,
    private userRoleService: UserRoleService,
    private _snackBar: AppToastService
  ) {}

  ngOnInit(): void {
    this.getUserRolePermissions();
  }

  toggle(permission: any) {
    permission.toggle = !permission.toggle;
  }

  onCheckboxChange(item: any) {
    this.toggleNestedCheckboxes(item, item.is_enabled);
    this.updateParentCheckboxes();
  }

  toggleNestedCheckboxes(item: any, is_enabled: boolean): void {
    if (item.child && item.child.length > 0) {
      item.child.forEach((childItem: any) => {
        childItem.is_enabled = is_enabled;
        this.toggleNestedCheckboxes(childItem, is_enabled);
      });
    }
  }

  updateParentCheckboxes(): void {
    this.permissions.forEach((parentItem: any) => {
      this.updateParentCheckbox(parentItem);
    });
  }

  updateParentCheckbox(item: any): void {
    if (item.child && item.child.length > 0) {
      item.is_enabled = item.child.some(
        (childItem: any) => childItem.is_enabled
      );
      item.child.forEach((childItem: any) =>
        this.updateParentCheckbox(childItem)
      );
    }
  }

  getUserRolePermissions() {
    this.userRoleService
      .getUserRolePermissions(this.data.id)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          const permissions = res?.data || [];
          if (this.data.role_type === RolesType.STOREFRONT) {
            this.permissions = permissions.filter(
              (o: any) =>
                o.name.toLowerCase() == this.data.role_type.toLowerCase()
            );
          } else {
            this.permissions = permissions;
          }
        },
        error: () => {
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  getPermissionIds(data: any) {
    const ids: any = [];
    data.forEach((item: any) => {
      if (item.is_enabled) {
        ids.push(item.id);
      }
      if (item.child.length > 0) {
        const nestedIds = this.getPermissionIds(item.child);
        ids.push(...nestedIds);
      }
    });
    return ids;
  }

  savePermissions() {
    const ids = this.getPermissionIds(this.permissions || []);
    this.saving = true;
    this.userRoleService
      .turnOnUserRolePermissions(this.data.id, ids)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.saving = false;
          this._snackBar.open("User role permission saved successfully.");
          this.activeModal.dismiss();
        },
        error: () => {
          this.saving = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
