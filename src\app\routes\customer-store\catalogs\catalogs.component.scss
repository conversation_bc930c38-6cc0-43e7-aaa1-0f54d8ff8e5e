:host {
    position: relative;

    body {
        height: 100%;
    }
}

.content {
    margin: 0;
    padding: 0 !important;
    position: relative;
    display: flex;
    justify-content: flex-start;
    gap: 0;
    min-height: calc(100vh - 100px);
}

.header {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 0 25px 0;
    padding: 0 0 25px 0;
    border-bottom: 2px solid #e7edee;
    align-items: flex-end;

    .all-main-title-sec.cart-details-main-title {
        align-items: flex-start;
        justify-content: flex-start;
        text-align: left;
        padding: 0;

        h1 {
            text-align: left;
            padding: 0;
            display: flex;
            align-items: center;
            gap: 0 10px;
            justify-content: space-between;
        }

        .product-id {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-9);
            font-weight: var(--snjy-font-weight-semi-bold);
            color: var(--snjy-color-text-ternary);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 5px;
            line-height: 15px;

            span:not(.material-icons-outlined) {
                margin: 0;
                padding: 0;
                font-weight: var(--snjy-font-weight-medium);
                color: var(--snjy-button-color-primary);
            }

            .material-icons-outlined {
                font-weight: var(--snjy-font-weight-normal);
                color: var(--snjy-color-text-ternary);
                font-size: var(--snjy-font-size-1-125);
                height: fit-content;
                width: fit-content;
            }
        }
    }

    .toggle-filter {
        width: 36px;
        height: 36px;
        background-color: #d7e4e7;
        border-radius: 50px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .search-container {
        display: flex;
        position: relative;
        justify-content: flex-end;
        margin: 0;

        .material-icons-outlined {
            color: var(--snjy-color-dark-secondary);
            position: absolute;
            top: 6px;
            right: 6px;
            width: 36px;
            height: 36px;
            background-color: #d7e4e7;
            border-radius: 50px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .search {
            border: none;
            box-shadow: none;
            min-width: 300px;
            font-size: var(--snjy-font-size-0-875);
            line-height: 14px;
            padding: 0 16px;
            color: #2c2c2c;
            background: #f0f5f6;
            border-radius: 50px;
            font-weight: var(--snjy-font-weight-medium);
            height: 48px;
        }
    }
}

.products-container {
    gap: 32px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(290px, 1fr));
    justify-items: center;
}

.sidebar {
    margin: 0;
    padding: 1.5rem;
    position: relative;
    min-width: 22%;
    border-right: 1px solid #e7edee;
    z-index: 1;

    .facet-toggle-container {
        position: absolute;
        top: 5px;
        right: 5px;
        background: #f0f5f6;
        border-radius: 50%;
        height: 25px;
        width: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        .material-icons-outlined {
            font-size: var(--snjy-font-size-1);
        }
    }
}

.product {
    padding: 1.5rem;
    margin: 0;
    flex-grow: 1;
}

.footer-paginator {
    display: flex;
    justify-content: center;
    margin: 32px 0;

    .product-l-filter {
        display: none;
    }

}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 0 10px;

    .rpp-container {
        display: flex;
        align-items: center;

        .rpp {
            margin-right: 12px;
            font-size: var(--snjy-font-size-0-8);
            color: var(--snjy-color-dark-secondary);
            white-space: nowrap;
            font-weight: var(--snjy-font-weight-medium);
        }

        select {
            width: auto;
            font-size: var(--snjy-font-size-0-75);
            color: var(--snjy-color-dark-secondary);
        }
    }

    .pages {
        display: flex;
        align-items: center;
        gap: 0 8px;

        .material-icons-outlined {
            color: var(--snjy-color-dark-secondary);
            padding: 0 4px;
            font-size: var(--snjy-font-size-0-9);
            height: 28px !important;
            width: 28px !important;
            background: var(--snjy-font-color-primary);
            border: 1px solid #dfdfdf;
            border-radius: 4px;
            box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);
            position: relative;
            overflow: visible;
            text-align: center;
            line-height: 26px;
            cursor: pointer;
        }

        span:not(.material-icons-outlined) {
            font-size: var(--snjy-font-size-0-75);
            color: #AAAAAA;
            cursor: pointer;
            height: 28px !important;
            width: 28px !important;
            background: var(--snjy-font-color-primary);
            border: 1px solid #dfdfdf;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;

            &.selected {
                color: var(--snjy-font-color-primary);
                background: var(--snjy-button-color-primary);
                border: 1px solid var(--snjy-button-color-primary);
            }
        }
    }

    .product-l-filter {
        margin: 0;
        position: relative;
        text-align: right;

        .product-l-filter-box {
            margin: 0;
            padding: 0;
            position: relative;
            width: -moz-fit-content;
            width: 170px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0 6px;
            font-size: var(--snjy-font-size-0-8);
            color: var(--snjy-color-dark-secondary);
            white-space: nowrap;
            font-weight: var(--snjy-font-weight-medium);

            i {
                font-size: var(--snjy-font-size-1-125);
                color: var(--snjy-color-dark-secondary);
            }

            select.form-control {
                margin: 0;
                padding: 0;
                position: relative;
                font-size: var(--snjy-font-size-0-875);
                font-weight: var(--snjy-font-weight-medium);
                border: none;
                color: var(--snjy-button-color-primary);
                appearance: auto;
            }
        }
    }
}

@media only screen and (max-width:900px) {
    .sidebar {
        position: fixed;
        background-color: white;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        min-width: 300px;
        overflow: auto;
        height: calc(100% - 62px);
    }

    .header {
        align-items: flex-start;
        flex-direction: column;
        gap: 20px 0;

        .search-c-sec {
            width: 100%;
        }
    }
}

@media only screen and (max-width:576px) {
    .header .search-container .search {
        min-width: calc(100% - 30px - 1rem);
    }

    .pagination-container {
        .product-l-filter {
            margin: 0;
            position: relative;
            text-align: right;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            width: 100%;

            .product-l-filter-box {
                justify-content: space-between !important;
                width: 100% !important;

                .form-control {
                    background: none;
                    outline: none !important;
                    box-shadow: none !important;
                }
            }
        }
    }
}

.disable:hover {
    cursor: not-allowed;
}

.disable:active {
    pointer-events: none;
}

:host::ng-deep {
    .custom-loader {
        max-height: calc(100vh - 140px);
    }
}