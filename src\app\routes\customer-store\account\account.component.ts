import { Component } from "@angular/core";
import { Subject, takeUntil } from "rxjs";
import { ColDef } from "ag-grid-community";

import { CustomersService } from "../services/customers/customers.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { AuthService } from "src/app/core/authentication/auth.service";

@Component({
  selector: "app-account",
  templateUrl: "./account.component.html",
  styleUrls: ["./account.component.scss"],
})
export class AccountComponent {
  private ngUnsubscribe = new Subject<void>();
  public loading: any = false;
  public partner_function: any = null;
  public contacts: any[] = [];

  public columnDefs: ColDef[] = [
    {
      field: "id",
      headerName: "ID",
      sortable: false,
      filter: false,
      hide: true,
    },
    {
      field: "first_name",
      headerName: "First Name",
      sortable: true,
      filter: true,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      sortable: true,
      filter: true,
    },
    {
      field: "email",
      headerName: "Email Id",
      sortable: true,
      filter: true,
    },
    {
      field: "phone",
      headerName: "Phone",
      sortable: true,
      filter: true,
    },
    {
      field: "status",
      headerName: "Status",
      sortable: true,
      filter: true,
    },
  ];

  constructor(
    private authService: AuthService,
    private _snackBar: AppToastService,
    private custService: CustomersService
  ) {
    const auth = this.authService.getAuth();
    this.partner_function = auth?.partner_function || null;
  }

  ngOnInit(): void {
    this.getUsersByCustId();
  }

  getUsersByCustId() {
    this.custService
      .getUsersByCustId(this.partner_function?.customer_id)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.contacts = res?.data || [];
        },
        error: (e) => {
          this._snackBar.open("Error while processing seetings request.", {
            type: "Error",
          });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
