:host::ng-deep {
  .dropdown-toggle.btn.btn-light {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    text-transform: uppercase;
    font-size: var(--snjy-font-size-0-875);
    font-weight: var(--snjy-font-weight-bold);
  }
}


:host {
  min-width: 350px;
}

h3 {
  font-size: var(--snjy-font-size-1-25);
  font-weight: var(--snjy-font-weight-bold);
  color: var(--snjy-button-color-primary);
}

button:not(.dp-btn),
button:not(.dp-btn):disabled,
button:not(.dp-btn):hover {
  border-radius: 8px;
  padding: .5rem 1.5rem;
  font-family: var(--snjy-font-family);
  background-color: var(--snjy-button-color-primary);
  color: var(--snjy-color-white);
  font-size: var(--snjy-font-size-0-875);
  font-weight: var(--snjy-font-weight-medium);
}

button:not(.btn-outline-secondary):disabled {
  opacity: 0.8;
}

.btn-light:hover,
.btn-light {
  background-color: var(--snjy-font-color-primary) !important;
  color: var(--snjy-button-color-primary) !important;
  border: 1px solid var(--snjy-button-color-primary) !important;
}


:host ::ng-deep {

  .ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
  .ag-ltr .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
  .ag-ltr .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
  .ag-ltr .ag-cell-range-single-cell,
  .ag-ltr .ag-cell-range-single-cell.ag-cell-range-handle,
  .ag-rtl .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
  .ag-rtl .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
  .ag-rtl .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
  .ag-rtl .ag-cell-range-single-cell,
  .ag-rtl .ag-cell-range-single-cell.ag-cell-range-handle {
    border: none;
  }

  .ag-theme-alpine {
    --ag-header-height: 30px;
    --ag-header-padding: 16px;
    --ag-header-foreground-color: var(--snjy-color-main-background);
    --ag-header-background-color: #0077d7;
    --ag-header-borders: 1px solid rgba(208, 215, 216, 0.3882352941);
    --ag-header-box-shadow: var(--snjy-box-shadow);
    --ag-header-borders-radius: 12px --ag-row-background-color: #0077d7;
  }

  .ag-row {
    --ag-row-background-color: 'grey';
  }

  .ag-root-wrapper {
    border: none;
  }

  .ag-header {
    border-radius: 10px;
    margin-bottom: 10px;
  }

  .ag-row {
    padding: 0 1rem;
    overflow: hidden;
    border: none;
    cursor: pointer;
  }

  .ag-row-selected::before,
  .ag-row-hover::before {
    background-color: transparent;
    background-image: none;
  }

  .ag-cell {
    height: calc(100% - 10px);
    display: flex;
    align-items: center;
    background: #f5f5f5;
    margin-bottom: 10px;
    font-weight: var(--snjy-font-weight-medium);

    &:first-child {
      border-top-left-radius: 10px;
      color: var(--snjy-table-first-column-color);
      border-bottom-left-radius: 10px;
      padding: 0;
    }

    &:last-child {
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
    }
  }

  .grid-cell {
    line-height: 1rem;
    font-weight: var(--snjy-font-weight-medium);

    .material-icons-outlined {
      color: #b7b7b7;
      font-size: var(--snjy-font-size-1-125);
    }
  }

  .ag-paging-panel {
    border: none;
    height: 50px;
  }
}

@media (max-width: 576px) {
  .btn-container {
    flex-direction: column;
    gap: 1rem;

    button {
      width: 100%;
    }
  }
}