import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ReplenishmentHistoryComponent } from "./replenishment-history/replenishment-history.component";

const routes: Routes = [
  {
    path: "",
    component: ReplenishmentHistoryComponent,
  },
  {
    path: ":replenishment-order-id",
    loadChildren: () =>
      import(
        "./replenishment-order-details/replenishment-order-details.module"
      ).then((m) => m.ReplenishmentOrderDetailsModule),
    data: { permission: "P0016" },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReplenishmentRoutingModule {}
