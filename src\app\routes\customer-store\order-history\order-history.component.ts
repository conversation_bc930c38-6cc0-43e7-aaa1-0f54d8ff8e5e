import { Component, OnInit } from '@angular/core';
import { OrderHistoryService } from './order-history.service';
import { Router } from '@angular/router';
import moment from 'moment';
import { AuthService } from 'src/app/core/authentication/auth.service';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { NgbCalendar, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { Observable } from 'rxjs';
import { ColDef } from 'ag-grid-community';
import { CurrencyPipe } from '@angular/common';


@Component({
  selector: 'app-order-history',
  templateUrl: './order-history.component.html',
  styleUrls: ['./order-history.component.scss']
})
export class OrderHistoryComponent implements OnInit {
  statuses: any = [];
  statusByCode: any = {};
  orders: any = [];
  sellerDetails: any = {};
  loading = false;
  searchCriteria: any = {
    fromDate: '',
    toDate: '',
    sdDoc: '',
    purchaseOrder: '',
    docStauts: 'all',
    docType: '',
    channel: 'all'
  };

  public rowData$!: Observable<any[]>;

  constructor(
    private service: OrderHistoryService,
    private _snackBar: AppToastService,
    public router: Router,
    private calendarService: NgbCalendar,
    public authService: AuthService,
    private currencyPipe: CurrencyPipe
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function
    }
  }

  public columnDefs: ColDef[] = [
    {
      field: "SD_DOC",
      headerName: 'Order #',
      headerComponentParams: { menuIcon: 'pin' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">pin</i>${data.data.SD_DOC}</span>`;
      },
      comparator: (valueA, valueB, nodeA, nodeB, isDescending) => parseInt(valueA) - parseInt(valueB),
      sortable: true,
      resizable: true
    },
    {
      field: "PURCH_NO",
      headerName: ' P.O. #',
      headerComponentParams: { menuIcon: 'description' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">description</i>${data.data.PURCH_NO}</span>`;
      },
      resizable: true
    },
    {
      field: "DOC_DATE",
      headerName: 'Date Placed',
      sortable: true,
      headerComponentParams: { menuIcon: 'calendar_month' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">calendar_month</i>${this.formatDate(data.data.DOC_DATE)}</span>`;
      },
      resizable: true
    },
    {
      field: "DOC_STATUS",
      headerName: "Order Status",
      headerComponentParams: { menuIcon: 'article' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">article</i>${this.statusByCode[data.data.DOC_STATUS]}</span>`;
      },
      resizable: true
    },
    {
      field: "TOTAL_NET_AMOUNT",
      headerName: "Net Amount",
      headerComponentParams: { menuIcon: 'sell' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">sell</i>${this.currencyPipe.transform(data.data.TOTAL_NET_AMOUNT, data.data.TXN_CURRENCY)}</span>`;
      },
      resizable: true
    },
    {
      field: "TXN_CURRENCY",
      headerName: "Currency",
      headerComponentParams: { menuIcon: 'payments' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">payments</i>${data.data.TXN_CURRENCY}</span>`;
      },
      resizable: true
    },
    {
      field: "CHANNEL",
      headerName: "Channel",
      headerComponentParams: { menuIcon: 'checklist' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">checklist</i>${data.data.CHANNEL}</span>`;
      },
      resizable: true
    },
  ];


  ngOnInit(): void {
    this.getOrderTypes();
  }

  getOrderTypes() {
    this.service
      .getAllOrderType()
      .subscribe({
        next: (res: any) => {
          const data: any = res?.data || ["OR"];
          this.searchCriteria.docType = data.map((o: any) => o.code).join(";");
          this.getAllStatus();
        },
        error: (e: any) => {
          console.error("Error while processing order type GET request.", e);
        },
      });
  }

  getAllStatus() {
    this.loading = true;
    return this.service.getAllStatuses().subscribe({
      next: (value) => {
        this.statuses = [{ code: 'all', description: 'All' }, ...value.data];
        this.statuses.reduce((acc: { [x: string]: any; }, value: { code: string | number; description: any; }) => {
          acc[value.code] = value.description;
          return acc;
        }, this.statusByCode);
        this.getOrderHistory();
      },
      error: () => {
        this.loading = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      }
    })
  }

  search() {
    this.getOrderHistory();
  }

  clear() {
    this.searchCriteria.fromDate = "";
    this.searchCriteria.toDate = "";
    this.searchCriteria.sdDoc = "";
    this.searchCriteria.purchaseOrder = "";
    this.searchCriteria.docStauts = "all";
    this.searchCriteria.channel = 'all';
  }

  getOrderHistory() {
    this.loading = true;
    const obj: any = {
      ...this.getDateRange(),
      SD_DOC: this.searchCriteria.sdDoc,
      PURCHASE_ORDER: this.searchCriteria.purchaseOrder,
      SOLDTO: this.sellerDetails.customer_id,
      VKORG: this.sellerDetails.sales_organization,
      DOC_TYPE: this.searchCriteria.docType,
      COUNT: 100
    };
    if (this.searchCriteria.docStauts == "all") {
      const statuses = this.statuses.map((val: any) => val.code);
      statuses.splice(0, 1);
      obj.DOC_STATUS = statuses.join(';');
    } else {
      obj.DOC_STATUS = this.searchCriteria.docStauts;
    }
    if(this.searchCriteria.channel == "all") {
      obj.CHANNEL = 'Web Order;S4 Order';
    } else {
      obj.CHANNEL = this.searchCriteria.channel;
    }
    return this.service.getAll(obj).subscribe({
      next: (value) => {
        this.loading = false;
        this.orders = value.resultData;
      },
      error: () => {
        this.loading = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      }
    })
  }

  goToOrder(order: any) {
    this.router.navigate(["/store/order", order[0].SD_DOC, order[0].DOC_TYPE]);
  }

  getDateRange() {
    const fromDate = this.formatSearchDate(this.searchCriteria.fromDate);
    let toDate = this.formatSearchDate(this.searchCriteria.toDate);
    if (fromDate && !toDate) {
      toDate = this.formatSearchDate(this.calendarService.getToday());
    }
    return {
      DOCUMENT_DATE: fromDate,
      DOCUMENT_DATE_TO: toDate
    }
  }

  formatSearchDate(date: NgbDateStruct) {
    if (!date) return "";
    let newDate = new Date(date['year'], date['month'] - 1, date['day'])
    return moment(newDate).format("YYYYMMDD");
  }

  formatDate(input: string) {
    return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');
  }

  today() {
    return this.calendarService.getToday();
  }

  exportToExcel() {
    const fileName = 'orders-history.xlsx';
    const data: any = this.formatData(this.orders)
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'test');
    XLSX.writeFile(wb, fileName);
  }

  formatData(orders: any) {
    return orders.map(({ PURCH_NO, DOC_STATUS, DOC_DATE, SD_DOC, TOTAL_NET_AMOUNT, TXN_CURRENCY, CHANNEL }:
      {
        PURCH_NO: string, DOC_STATUS: string,
        DOC_DATE: string, SD_DOC: string,
        TOTAL_NET_AMOUNT: string, TXN_CURRENCY: string, CHANNEL: string
      }) =>
    ({
      'Order #': SD_DOC, 'P.O.#': PURCH_NO, 'Date Placed': this.formatDate(DOC_DATE),
      'Order Status': this.statusByCode[DOC_STATUS], 'Net Amount': TOTAL_NET_AMOUNT,
      'Currency': TXN_CURRENCY, "Channel": CHANNEL
    }));
  }
}