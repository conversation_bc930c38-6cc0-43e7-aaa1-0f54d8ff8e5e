:host {
    padding: 0 25px 25px;
}

::ng-deep {
    .angular-editor-textarea {
        border: none !important;
        resize: none !important;
        padding: 0 !important;
        margin-top: 0 !important;

        blockquote {
            border: none !important;
        }
    }
}

.desc-container {
    margin: 0;
    gap: 32px;
    display: grid;
    grid-template-columns: 440px minmax(250px, 1fr) minmax(300px, 330px);
    align-items: flex-start;
    justify-content: center;

    .add-to-cart-container {
        padding: 0;
        height: fit-content;
        overflow: hidden;
        border: 1px solid rgba(208, 215, 216, 0.3882352941);
        border-radius: 10px;
        box-shadow: var(--snjy-box-shadow);

        p {
            &:nth-child(1) {
                font-family: var(--snjy-font-family);
                font-weight: var(--snjy-font-weight-bold);
                font-size: var(--snjy-font-size-1);
                color: #101453;
            }

            &:nth-child(2) {
                font-family: var(--snjy-font-family);
                font-weight: var(--snjy-font-weight-semi-bold);
                font-size: var(--snjy-font-size-1-25);
                color: var(--snjy-color-text-secondary);
                margin-bottom: 1.5rem;
            }

        }

        >div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;

            &.offer-part {
                margin: 0;
                padding: 20px;
                background: var(--snjy-button-color-primary);
                border-radius: 0;
                align-items: flex-start;
                min-height: 153px;
                background-position: center;
                background-repeat: no-repeat;
                background-size: cover;

                .offer-price h4 {
                    margin: 0;
                    padding: 0;
                    font-size: var(--snjy-font-size-2);
                    font-weight: var(--snjy-font-weight-bold);
                    color: var(--snjy-color-white);
                }

                .offer-price small {
                    margin: 0;
                    padding: 0;
                    font-size: var(--snjy-font-size-0-875);
                    font-weight: var(--snjy-font-weight-normal);
                    color: var(--snjy-font-color-primary);
                }

                .offr-date {
                    margin: 0;
                    padding: 8px 26px;
                    background: rgb(255 255 255 / 32%);
                    border-radius: 7px;
                    font-size: var(--snjy-font-size-1-25);
                    font-weight: var(--snjy-font-weight-medium);
                    color: var(--snjy-color-white);
                }
            }

            &.add-to-cart-body {
                flex-direction: column;
                height: -moz-fit-content;
                height: fit-content;
                justify-content: flex-start !important;
                padding: 25px;
                margin: 0;
            }

            >span {
                font-family: var(--snjy-font-family);
                font-size: var(--snjy-font-size-0-875);
                color: var(--snjy-color-text-primary);
            }
        }

        .order-counter {
            margin: -62px 0 30px 0;
            padding: 16px;
            position: relative;
            background: var(--snjy-color-white);
            text-align: center;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.0901960784);

            &.order-counter h4 {
                margin: 0 0 21px 0;
                padding: 0;
                font-size: var(--snjy-font-size-0-8);
                color: #2d3148;
                font-weight: var(--snjy-font-weight-medium);
                text-transform: uppercase;
            }
        }

        button {
            width: 100%;
            border-radius: 7px;
            margin-bottom: 0.8rem;
            padding: 0;
            min-height: 50px;
            font-family: var(--snjy-font-family);
            background-color: var(--snjy-button-color-primary);
            color: var(--snjy-color-white);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0 5px;
            font-size: var(--snjy-font-size-0-875);
            line-height: 16px;
            font-weight: var(--snjy-font-weight-medium);

            &.btn-light {
                background-color: var(--snjy-font-color-primary);
                color: var(--snjy-button-color-primary);
                border: 1px solid var(--snjy-button-color-primary);
            }
        }

        .cart-quantity {
            background: var(--snjy-font-color-primary);
            border-radius: 7px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 180px;
            margin: 0 auto;
            min-height: 50px;
            border: 1px solid #f1f1f1;
            padding: 0 12px;

            .material-icons-outlined {
                color: var(--snjy-color-dark-secondary);
                cursor: pointer;
            }

            .qty-to-add {
                font-family: var(--snjy-font-family);
                font-size: var(--snjy-font-size-1);
                color: var(--snjy-font-color-seconday);
            }
        }
    }

    .images {
        gap: 10px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        position: relative;

        .img-container {
            position: relative;
            max-width: 100%;
            width: 100%;
            border-radius: 15px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 500px;
            border: 1px solid #efefef;

            img {
                width: max-content;
                height: 90%;
            }
        }

        >div {
            position: relative;
            max-width: 100%;
            width: 100%;
        }

        .material-icons-outlined:not(.expand) {
            height: 30px;
            width: 30px;
            color: #5F9EFE;
            position: absolute;
            bottom: 0;
            top: 0;
            margin: auto 0;
            border: 1px solid var(--snjy-color-white);
            border-radius: 100px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.0784313725);
            z-index: 99;

            &.left {
                left: 0;
            }

            &.right {
                right: 0;
            }


            &.has-left,
            &.has-right {
                cursor: pointer;
                opacity: 1;
            }
        }

        .expand {
            color: var(--snjy-button-color-primary);
            border: 1px solid #efefef;
            border-radius: 4px;
            padding: 5px;
            cursor: pointer;
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .carosol {
            display: flex;
            gap: 12px;
            margin: 0 42px;
            max-width: 100%;
            overflow: auto;

            &::-webkit-scrollbar {
                display: none;
            }

            -ms-overflow-style: none;
            scrollbar-width: none;

            img {
                opacity: .6;
                cursor: pointer;
                height: 100px;
                border-radius: 12px;

                &.active {
                    opacity: 1;
                }
            }


        }
    }

    .desc {
        padding: 1rem;

        .product-title-part {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            position: relative;
            z-index: 11;

            span:not(.material-icons-outlined) {
                font-family: var(--snjy-font-family);
                font-weight: var(--snjy-font-weight-bold);
                font-size: 26px;
                color: var(--snjy-color-text-primary);
            }

            .product-name {
                margin: 0 0 15px 0;
                padding: 0;
                position: relative;
                font-size: var(--snjy-font-size-1-25);
                font-weight: var(--snjy-font-weight-bold);
                color: var(--snjy-color-dark-secondary);

                .available-quantity {
                    background-color: #25bd65;
                    font-family: var(--snjy-font-family);
                    font-size: var(--snjy-font-size-0-75);
                    color: var(--snjy-font-color-primary);
                    padding: 5px 10px;
                    display: block;
                    width: fit-content;
                    border-radius: 50px;
                    margin: 0 0 10px 0;
                }
            }

            .pro-like-share ul {
                margin: 0 0 10px 0 !important;
                padding: 0;
                position: relative;
                gap: 0 10px !important;
                display: flex;
                justify-content: flex-end;
            }

            .pro-like-share ul li {
                flex-direction: column;
                width: 50px;
                height: 50px;
                border: 1px solid #bbb;
                align-items: center !important;
                justify-content: center !important;
                border-radius: 5px;
            }

            .pro-like-share ul li .material-icons-outlined {
                font-size: 20px !important;
                height: fit-content;
                width: fit-content;
            }

            .pro-like-share ul li span:not(.material-icons-outlined) {
                color: var(--snjy-color-text-ternary) !important;
                font-weight: var(--snjy-font-weight-medium) !important;
                font-size: 10px !important;
            }

            .request-a-quote-btn {
                width: 100%;
                border-radius: 6px;
                margin-bottom: 0;
                padding: 0 16px;
                min-height: 40px;
                font-family: var(--snjy-font-family);
                background-color: var(--snjy-button-color-primary);
                color: var(--snjy-color-white);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0 5px;
                font-size: var(--snjy-font-size-0-875);
                line-height: 14px;
                font-weight: var(--snjy-font-weight-medium);
                border: none;
            }
        }

        ul {
            margin: 0;
            padding: 0;
            position: relative;
            list-style: none;
        }

        ul li {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-875);
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-color-dark-secondary);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 5px;

            &.sku-id {
                color: var(--snjy-color-text-ternary);

                span {
                    color: var(--snjy-color-dark-secondary);
                }
            }

            span:not(.material-icons-outlined) {
                color: var(--snjy-color-text-ternary);
            }
        }

        ul li .material-icons-outlined {
            font-weight: var(--snjy-font-weight-normal);
            color: var(--snjy-button-color-primary);
            font-size: 22px;
            width: fit-content;
            height: fit-content;
        }

        .prod-sub-name {
            font-family: var(--snjy-font-family);
            font-size: var(--snjy-font-size-0-875);
            margin-bottom: 16px;
            color: #5F9EFE;
        }

        .prod-desc {
            font-family: var(--snjy-font-family);
            font-weight: var(--snjy-font-weight-normal);
            font-size: var(--snjy-font-size-0-9);
            color: var(--snjy-font-color-seconday);
            margin: 0 !important;
            display: block;
            text-align: justify;
        }

        .accordion {
            margin: 15px 0 0 0;

            .accordion-item {
                border: none;
                border-top: 1px solid #a7a7a7 !important;
            }

            .accordion-item:last-child {
                border-bottom: 1px solid #a7a7a7 !important;
            }

            button.accordion-button {
                margin: 0;
                padding: 15px 0;
                gap: 0 2%;
                background: no-repeat;
                font-size: var(--snjy-font-size-0-9);
                font-weight: var(--snjy-font-weight-medium);
                color: var(--snjy-color-dark-secondary);
                outline: none;
                box-shadow: none;

                .material-icons-outlined {
                    font-weight: var(--snjy-font-weight-normal);
                    color: var(--snjy-button-color-primary);
                    font-size: 22px;
                    width: fit-content;
                    height: fit-content;
                }
            }
        }
    }
}

.pdp-resources>ul {
    display: flex;
    gap: 0 16px;
    position: relative;
}

.pdp-resources>ul>li i {
    margin: 0 0 12px 0;
    width: 55px !important;
    height: 55px !important;
    position: relative;
    border: 1px solid #1a7ecc;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #ff8000 !important;
    border-radius: 8px;
    font-size: 25px !important;
}

hr {
    color: #dbb9b9;
}

.similar-products {
    gap: 32px;
    padding: 14px 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(290px, 1fr));
}

.simliar-text {
    font-family: var(--snjy-font-family);
    font-weight: var(--snjy-font-weight-bold);
    font-size: 22px;
    color: var(--snjy-color-secondary);
}

.view-more-text {
    font-family: var(--snjy-font-family);
    font-weight: var(--snjy-font-weight-bold);
    font-size: var(--snjy-font-size-0-9);
    color: #5F9EFE;
    cursor: pointer;

    .material-icons-outlined {
        margin-left: 1rem;
        color: var(--snjy-color-primary)
    }
}

.panel-details {
    max-height: fit-content;
}

::ng-deep {
    lib-ngx-image-zoom {
        .ngxImageZoomContainer {
            width: 100% !important;
            height: 500px !important;

            >img {
                width: 100% !important;
                height: 500px !important;
            }
        }
    }
}

.pdp-resources {
    li {
        padding: 10px !important;
    }

    a {
        color: var(--bs-link-color);
        ;
    }
}

.not-found {
    button {
        width: 100%;
        border-radius: 7px;
        margin-bottom: 0.8rem;
        padding: 0;
        min-height: 50px;
        font-family: var(--snjy-font-family);
        background-color: var(--snjy-button-color-primary);
        color: var(--snjy-color-white);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0 5px;
        font-size: var(--snjy-font-size-0-875);
        line-height: 16px;
        font-weight: var(--snjy-font-weight-medium);

        &.btn-light {
            background-color: var(--snjy-font-color-primary);
            color: var(--snjy-button-color-primary);
            border: 1px solid var(--snjy-button-color-primary);
        }
    }
}

:host::ng-deep {
    .similar-products .pro-details {
        padding: 15px 15px 18px 15px !important;
    }
}

@media (max-width: 1200px) {
    .desc-container {
        grid-template-columns: repeat(auto-fill, minmax(200px, 100%));
    }
}


@media (max-width: 576px) {
    ::ng-deep {
        lib-ngx-image-zoom {
            .ngxImageZoomContainer {
                width: 100% !important;
                height: 300px !important;

                >img {
                    width: 100% !important;
                    height: 300px !important;
                }
            }
        }
    }

    .img-container {
        height: 100% !important;
    }

    .product-title-part {
        flex-direction: column-reverse;
    }

    .desc-container .desc .product-title-part .pro-like-share ul li {
        flex-direction: row;
        width: 90px;
        height: 32px;
    }

    .accordion-body {
        padding: 0 !important;
    }

    .desc,
    .desc .panel-details,
    .desc .accordion-body {
        padding: 0 !important;
    }
}

@media (max-width: 480px) {
    .similar-products {
        grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
    }
}

.specification-container {
    gap: 32px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(290px, 1fr));
}

.card-header {
     background-color: var(--snjy-button-color-primary);
     color: white;
}