import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { DashboardGuard } from "./guard/dashboard.guard";

const routes: Routes = [
  {
    path: "dashboard",
    loadChildren: () =>
      import("./customer-dashboard/customer-dashboard.module").then(
        (m) => m.CustomerDashboardModule
      ),
  },
  {
    path: "catalogues",
    loadChildren: () =>
      import("./catalogs/catalogs.module").then((m) => m.CatalogsModule),
    data: { permission: ["P0017", "P0041"] },
  },
  {
    path: "invoices",
    loadChildren: () =>
      import("./invoices/invoices.module").then((m) => m.InvoicesModule),
    data: { feature: "F0002", permission: ["P0003", "P0045", "P0054"] },
  },
  {
    path: "quick-order",
    loadChildren: () =>
      import("./quick-order/quick-order.module").then(
        (m) => m.QuickOrderModule
      ),
    data: { feature: "F0003", permission: "P0004" },
  },
  {
    path: "cart",
    loadChildren: () => import("./cart/cart.module").then((m) => m.CartModule),
  },
  {
    path: "checkout",
    loadChildren: () =>
      import("./checkout/checkout.module").then((m) => m.CheckoutModule),
  },
  {
    path: "checkout-confirm",
    loadChildren: () =>
      import("./checkout-confirmation/checkout-confirmation.module").then(
        (m) => m.CheckoutConfirmaionModule
      ),
  },
  {
    path: "sales-order-scheduler-confirm",
    loadChildren: () =>
      import(
        "./sales-order-scheduler-confirmation/sales-order-scheduler-confirmation.module"
      ).then((m) => m.SalesOrderSchedulerConfirmationModule),
  },
  {
    path: "saved-cart",
    loadChildren: () =>
      import("./saved-carts/saved-carts.module").then(
        (m) => m.SavedCartsModule
      ),
    data: { feature: "F0007", permission: "P0014" },
  },
  {
    path: "import-cart",
    loadChildren: () =>
      import("./import-cart/import-cart.module").then(
        (m) => m.ImportCartModule
      ),
    data: { feature: "F0007", permission: "P0036" },
  },
  {
    path: "saved-cart-details/:id",
    loadChildren: () =>
      import("./saved-cart-detail/saved-cart-detail.module").then(
        (m) => m.SavedCartDetailModule
      ),
    data: { feature: "F0007", permission: "P0034" },
  },
  {
    path: "order-history",
    loadChildren: () =>
      import("./order-history/order-history.module").then(
        (m) => m.OrderHistoryModule
      ),
    data: { feature: "F0006", permission: ["P0011", "P0043", "P0048", "P0052"] },
  },
  {
    path: "order/:id/:type",
    loadChildren: () =>
      import("./order-details/order-details.module").then(
        (m) => m.OrderDetailsModule
      ),
    data: { feature: "F0006", permission: ["P0012", "P0044", "P0049", "P0053"] },
  },
  {
    path: "product-details/:productId",
    loadChildren: () =>
      import("./product-detail/product-detail.module").then(
        (m) => m.ProductDetailModule
      ),
    data: { permission: ["P0018", "P0042"] },
  },
  {
    path: "replenishment",
    loadChildren: () =>
      import("./replenishment/replenishment.module").then(
        (m) => m.ReplenishmentModule
      ),
    data: { feature: "F0008", permission: "P0015" },
  },
  {
    path: "customer-services",
    canActivateChild: [DashboardGuard],
    loadChildren: () =>
      import("./customer-services/customer-services.module").then(
        (m) => m.CustomerServicesModule
      ),
    data: { feature: "F0001", authority: "P0002" },
  },
  {
    path: "account",
    loadChildren: () =>
      import("./account/account.module").then((m) => m.AccountModule),
  },
  {
    path: "tickets",
    loadChildren: () =>
      import("./tickets/tickets.module").then((m) => m.TicketsModule),
    data: { feature: "F0012", permission: "P0023" },
  },
  {
    path: "tickets/create",
    loadChildren: () =>
      import("./tickets/ticket/ticket.module").then((m) => m.TicketModule),
    data: { feature: "F0013", permission: "P0025" },
  },
  {
    path: "tickets/:ticket_id",
    loadChildren: () =>
      import("./tickets/ticket-detail/ticket-detail.module").then(
        (m) => m.TicketDetailModule
      ),
    data: { permission: "P0024" },
  },
  {
    path: "register-product",
    loadChildren: () =>
      import("./register-product/register-product.module").then(
        (m) => m.RegisterProductModule
      ),
    data: { feature: "F0011", permission: "P0022" },
  },
  {
    path: "check-registered-product",
    loadChildren: () =>
      import("./check-registered-product/check-registered-product.module").then(
        (m) => m.CheckRegisteredProductModule
      ),
    data: { feature: "F0009", permission: "P0020" },
  },
  {
    path: "check-warranty",
    loadChildren: () =>
      import("./check-warranty/check-warranty.module").then(
        (m) => m.CheckWarrantyModule
      ),
    data: { feature: "F0014", permission: "P0026" },
  },
  {
    path: "knowledge-base",
    loadChildren: () =>
      import("./knowledge-base/knowledge-base.module").then(
        (m) => m.KnowledgeBaseModule
      ),
    data: { feature: "F0010", permission: "P0021" },
  },
  {
    path: "quote-history",
    loadChildren: () =>
      import("./quote-history/quote-history.module").then(
        (m) => m.QuoteHistoryModule
      ),
    data: { feature: "F0004", permission: ["P0005", "P0046"] },
  },
  {
    path: "quotes/:quote_id",
    loadChildren: () =>
      import("./quote-history/quote-detail/quote-detail.module").then(
        (m) => m.QuoteDetailModule
      ),
    data: { feature: "F0004", permission: ["P0006", "P0047"] },
  },
  {
    path: "product-return/:id/:type",
    loadChildren: () =>
      import("./product-return/product-return.module").then(
        (m) => m.ProductReturnModule
      ),
    data: { feature: "F0005", permission: "P0010" },
  },
  {
    path: "return-order-history",
    loadChildren: () =>
      import("./return-order-history/return-order-history.module").then(
        (m) => m.ReturnOrderHistoryModule
      ),
    data: { feature: "F0005", permission: "P0008" },
  },
  {
    path: "return-order/:returnOrderId/:refDocId",
    loadChildren: () =>
      import(
        "./return-order-history/return-order-details/return-order-detail.module"
      ).then((m) => m.ReturnOrderDetailModule),
    data: { feature: "F0005", permission: "P0009" },
  },
  {
    path: "sales",
    canActivateChild: [DashboardGuard],
    loadChildren: () =>
      import("./sales/sales.module").then((m) => m.SalesModule),
    data: { authority: "P0040" },
  },
  {
    path: "vendor",
    canActivateChild: [DashboardGuard],
    loadChildren: () =>
      import("./vendor/vendor.module").then((m) => m.VendorModule),
    data: { authority: "P0050" },
  },
  { path: "", redirectTo: "dashboard", pathMatch: "full" },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CustomerStoreRoutingModule {}
