import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { map, of } from "rxjs";

import Timezones from "../../../shared/data/timezones.json";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class SettingsService {
  private settings!: any;

  constructor(private http: HttpClient) { }

  getTimezones() {
    return Timezones;
  }

  getCurrencies() {
    return ["USD"];
  }

  getCountries() {
    return ["USA"];
  }

  getSettings() {
    if (this.settings) {
      return of(this.settings);
    }
    return this.http
      .get<any>(`${ApiConstant.SETTINGS}`, {})
      .pipe(map((res) => {
        this.settings = res.data;
        return res.data
      }));
  }

  saveSettings(data: any) {
    return this.http
      .post<any>(`${ApiConstant.SETTINGS}`, data)
      .pipe(map((res) => {
        this.settings = res.data;
        return res.data
      }));
  }
}
