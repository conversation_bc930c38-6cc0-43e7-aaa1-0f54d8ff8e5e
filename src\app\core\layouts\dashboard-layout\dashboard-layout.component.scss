@mixin sticky {
    position: -webkit-sticky;
    position: sticky;
}

.wrapper {
  display: flex;
  flex-direction: row;
  height: calc(100vh - 76px);

  &.panel-position-left {
    flex-direction: row;
  }

  &.panel-position-right {
    flex-direction: row-reverse;
  }

  .left-column {
    @include sticky();
    top: 0;
    transition: flex-basis 0.3s;
    overflow: hidden;

    &.open {
      flex: 0 0 280px;
      margin: 0 0 0 60px;
      
    }

    &.close {
      flex: 0 0 0px;
    }

    &.collapse {
      flex: 0 0 90px;
      margin: 0 0 0 60px;
    }
  }

  .right-column {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow-y: auto;

    .navigation-bar {
      @include sticky();
      top: 0;
      z-index: 100;
    }
  }
}