<div class="modal-header">
  <h4 class="modal-title" id="modal-basic-title">Add Message</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
  <form [formGroup]="form">
    <div class="form-group mb-3 required">
      <label>Message</label>
      <textarea formControlName="Description" class="form-control" placeholder="Message" rows="4"></textarea>
      <div *ngIf="submitted && f['Description'].errors" class="invalid-feedback d-block">
        <div *ngIf="
            submitted &&
            f['Description'].errors &&
            f['Description'].errors['required']
          ">
          Description is required
        </div>
      </div>
    </div>
    <div class="form-group mb-3 required">
      <label>Select a File</label>
      <input type="file" class="file-input" [accept]="allowedFileTypes" (change)="onFileSelected($event)" #fileInput
        multiple />
      <button type="button" (click)="fileInput.click()"
        class="upload-file-link d-flex flex-column gap-3 align-items-between">
        <p class="d-flex align-items-center gap-3" *ngIf="!attachments.length">
          <span class="material-icons-outlined"> cloud_upload</span>
          <span>Choose File</span>
        </p>
        <ng-container *ngFor="let attachment of attachments; let i = index">
          <p class="file-name-container">
            <span class="text-truncate">{{ attachment.Name }}</span>
            <span class="material-icons-outlined red" *ngIf="attachment.Name" (click)="removeFile($event, i)">
              cancel
            </span>
          </p>
        </ng-container>
      </button>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-light" class="btn btn-light me-2" (click)="activeModal.dismiss();">
    Cancel
  </button>
  <button type="button" class="btn btn-primary" (click)="addMessage()" [disabled]="saving">
    {{ saving ? "Saving..." : "Save" }}
  </button>
</div>