.grid-column-menu {
  width: 200px;

  .mat-menu-content {
    padding: 0;
  }
}

.grid-column-menu-body {
  padding: 8px 16px;
}

.grid-column-menu-header,
.grid-column-menu-footer {
  position: sticky;
  z-index: 1;
  padding: 8px 16px;
}

.grid-column-menu-header {
  top: 0;
}

.grid-column-menu-footer {
  bottom: 0;
}

.grid-column-menu-list {
  display: block;
  max-width: 100%;

  &.cdk-drop-list-dragging {
    .grid-column-menu-item:not(.cdk-drag-placeholder) {
      transition: transform 250ms cubic-bezier(0, 0, .2, 1);
    }
  }
}

.grid-column-menu-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 5px 0;
  gap: 10px;

  &.cdk-drag-disabled {
    .cdk-drag-handle {
      opacity: .35;
      cursor: no-drop;
    }
  }

  .cdk-drag-handle {
    cursor: move;
  }

  &.cdk-drag-placeholder {
    opacity: 0;
  }

  &.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, .2, 1);
  }
}

.grid-column-pin-button.mat-mdc-icon-button {
  width: 40px;
  height: 40px;
  padding: 8px;
}

.grid-column-pin-option.mat-menu-item {
  display: flex;
  align-items: center;
  height: 32px;
}

.grid-column-pin-option-placeholder {
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  vertical-align: middle;
}

.grid-column-pin-option-text {
  padding: 0 8px;
  vertical-align: middle;
}

.grid-column-drag-handle-icon:hover {
  cursor: move;
}