import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { LoginComponent } from "./login/login.component";
import { AuthGuard } from "src/app/core/authentication/auth.guard";

const routes: Routes = [
  {
    path: "login",
    canActivate: [AuthGuard],
    component: LoginComponent,
  },
  {
    path: "reset-password",
    canActivate: [AuthGuard],
    loadChildren: () =>
      import("./reset-password/reset-password.module").then(
        (m) => m.ResetPasswordModule
      ),
  },
  { path: "", redirectTo: "login", pathMatch: "full" },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SessionRoutingModule {}
