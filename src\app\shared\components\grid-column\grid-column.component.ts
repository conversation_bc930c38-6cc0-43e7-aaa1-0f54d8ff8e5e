import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewEncapsulation,
} from "@angular/core";
import {
  GridColumn,
  GridColumnPinOption,
  GridColumnPinValue,
} from "./grid-column.model";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";

@Component({
  selector: "app-grid-column",
  exportAs: "gridColumnMenuCustom",
  templateUrl: "./grid-column.component.html",
  styleUrls: ["./grid-column.component.scss"],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GridColumnComponent {
  _columns: GridColumn[] = [];
  @Input()
  get columns(): GridColumn[] {
    return this._columns;
  }
  set columns(value: GridColumn[]) {
    value.forEach((v) => (v.show = true));
    this._columns = value;
  }

  @Input() selectable = true;
  @Input() selectableChecked: "show" | "hide" = "show";
  @Input() sortable = true;
  @Input() pinnable = true;

  @Input()
  get buttonText() {
    const defaultText = `Columns ${
      this.selectableChecked === "show" ? "Shown" : "Hidden"
    }`;
    return this._buttonText ? this._buttonText : defaultText;
  }
  set buttonText(value: string) {
    this._buttonText = value;
  }
  private _buttonText = "";

  @Input() buttonType = "stroked";
  @Input() buttonClass = "btn btn-light";

  @Input() showHeader = false;
  @Input() headerText = "Columns Header";
  @Input() headerTemplate!: TemplateRef<any>;
  @Input() showFooter = false;
  @Input() footerText = "Columns Footer";
  @Input() footerTemplate!: TemplateRef<any>;

  @Output() columnChange = new EventEmitter<GridColumn[]>();
  @Output() columnPositionChange = new EventEmitter<number[]>();

  @Input()
  get pinOptions() {
    return this._pinOptions;
  }
  set pinOptions(value: GridColumnPinOption[]) {
    if (value.length > 0) {
      this._pinOptions = value;
    }
  }
  private _pinOptions: GridColumnPinOption[] = [
    { label: "Pin Left", value: "left" },
    { label: "Pin Right", value: "right" },
    { label: "No Pin", value: null },
  ];

  _handleDroped(e: CdkDragDrop<string[]>) {
    moveItemInArray(this.columns, e.previousIndex, e.currentIndex);
    this.columnPositionChange.emit([e.previousIndex, e.currentIndex]);
  }

  _handleChecked() {
    this.columnChange.emit(this.columns);
  }

  _handlePinSelect(col: GridColumn, val: GridColumnPinValue) {
    if (col.pinned != val) {
      col.pinned = val;
      this.columnChange.emit(this.columns);
    }
  }
}
