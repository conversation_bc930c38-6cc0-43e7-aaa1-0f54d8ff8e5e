<div class="table-responsive" [formGroup]="form">
    <table class="table table-striped">
        <thead>
            <tr>
                <th width="30"></th>
                <th>Code</th>
                <th>Name</th>
                <th>Enabled</th>
            </tr>
        </thead>
        <tbody>
            <ng-container formArrayName="list">
                <ng-container *ngFor="let f of features.controls; let i = index">
                    <ng-container [formGroupName]="i">
                        <tr>
                            <td width="30">
                                <ng-container *ngIf="f.get('list')?.value?.length">
                                    <a href="javascript:void(0);" class="text-primary d-flex" (click)="toggle(f)">
                                        <i class="material-icons-outlined" *ngIf="!f.get('toggle').value">add</i>
                                        <i class="material-icons-outlined" *ngIf="f.get('toggle').value">remove</i>
                                    </a>
                                </ng-container>
                            </td>
                            <td> {{ f.get("code")?.value }} </td>
                            <td> {{ f.get("name")?.value }} </td>
                            <td>
                                <input type="checkbox" class="m-1 shadow-none checkbox-input"
                                    formControlName="is_enabled" (change)="onParentCBChange(f);" />
                            </td>
                        </tr>
                        <tr *ngIf="f.get('list')?.value?.length && f.get('toggle').value">
                            <td width="30"></td>
                            <td colspan="4" formArrayName="list">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Code</th>
                                            <th>Name</th>
                                            <th>Enabled</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container *ngFor="let c of f.get('list').controls; let j = index">
                                            <tr [formGroupName]="j">
                                                <td> {{ c.get("code")?.value }} </td>
                                                <td> {{ c.get("name")?.value }} </td>
                                                <td>
                                                    <input type="checkbox" class="m-1 shadow-none checkbox-input"
                                                        formControlName="is_enabled" (change)="onChildCBChange(f);" />
                                                </td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </ng-container>
                </ng-container>
            </ng-container>
            <tr *ngIf="!appFeatures.length">
                <td colspan="4">No records found.</td>
            </tr>
        </tbody>
    </table>
    <div class="row m-0">
        <div class="col d-flex justify-content-end">
            <button class="btn btn-primary btn-save" [disabled]="saving" (click)="!saving && saveFeatures()">
                {{saving ? 'Saving...' : 'Save'}}
            </button>
        </div>
    </div>
</div>