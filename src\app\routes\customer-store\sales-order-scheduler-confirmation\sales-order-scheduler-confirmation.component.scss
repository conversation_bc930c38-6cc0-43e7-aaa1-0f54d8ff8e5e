:host {
    padding: 25px;
    overflow: auto;
  }
  
  .all-main-title-sec.thank-you-details-main-title {
    align-items: flex-start;
    justify-content: flex-start;
    text-align: left;
    padding: 0 0 25px 0;
  
    h1 {
      text-align: left;
      padding: 0;
      display: flex;
      align-items: center;
      gap: 0 10px;
      justify-content: space-between;
    }
  
    .product-id {
      margin: 0;
      padding: 0;
      font-size: var(--snjy-font-size-0-9);
      font-weight: var(--snjy-font-weight-medium);
      color: #24292a;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 0 5px;
      line-height: 15px;
  
      span:not(.material-icons-outlined) {
        margin: 0;
        padding: 0;
        font-weight: var(--snjy-font-weight-medium);
        color: var(--snjy-button-color-primary);
      }
    }
  }
  
  .order-id-sec {
    margin: 0;
    padding: 0;
    position: relative;
  
    .order-id-body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: space-between;
      gap: 0 3%;
  
      .order-id-info {
        margin: 0;
        padding: 0;
        flex: 4;
  
        .order-details {
          margin: 0 0 20px 0;
          padding: 24px;
          background: var(--snjy-color-white);
          border: 1px solid var(--snjy-border-color-secondary);
          border-radius: 10px;
          box-shadow: var(--snjy-box-shadow);
  
          h3 {
            margin: 0 0 15px 0;
            padding: 0;
            position: relative;
            font-size: var(--snjy-font-size-1-125);
            font-weight: var(--snjy-font-weight-bold);
            color: #00216c;
            line-height: 20px;
          }
  
          ul {
            padding: 24px 24px;
            position: relative;
            list-style: none;
            background: #f0f5f6;
            border-radius: 8px;
            display: flex;
            justify-content: flex-start;
            gap: 40px 4%;
            flex-wrap: wrap;
  
            li {
              margin: 0;
              padding: 0;
              color: #687491;
              font-weight: var(--snjy-font-weight-medium);
              font-size: var(--snjy-font-size-0-8);
              flex: 0 0 30%;
              display: flex;
              align-items: flex-start;
              flex-wrap: wrap;
              gap: 0 3px;
              line-height: 20px;
  
              .material-icons-outlined {
                margin: 0;
                padding: 0;
                width: fit-content;
                height: fit-content;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-size: var(--snjy-font-size-1-25);
                color: #687491;
              }
  
              span:not(.material-icons-outlined) {
                margin: 0;
                padding: 0 0 0 22px;
                display: block;
                color: #0077d7;
                font-weight: var(--snjy-font-weight-medium);
                font-size: var(--snjy-font-size-0-8);
                width: 100%;
                line-height: 17px;
              }
            }
          }
  
          .item-box {
            margin: 0 0 18px 0;
            padding: 16px;
            background: #eff4f5;
            display: flex;
            justify-content: flex-start;
            gap: 0 3%;
            position: relative;
            border-radius: 7px;
  
            .item-box-img {
              margin: 0;
              padding: 0;
              position: relative;
              flex: 0 0 26%;
              height: 144px;
              background-size: contain;
              background-repeat: no-repeat;
              background-position: center;
              border-radius: 7px;
              overflow: hidden;
              border: 1px solid rgba(27, 125, 203, 0.29);
            }
  
            .item-box-content {
              margin: 0;
              padding: 0;
              position: relative;
              width: 100%;
  
              h4 {
                margin: 15px 0 0 0;
                padding: 0;
                position: relative;
                font-size: var(--snjy-font-size-1);
                font-weight: var(--snjy-font-weight-medium);
                color: var(--snjy-color-dark-secondary);
                line-height: 20px;
                display: block;
              }
  
              small {
                margin: 0;
                padding: 0;
                font-size: var(--snjy-font-size-0-75);
                color: #687491;
                font-weight: var(--snjy-font-weight-medium);
              }
  
              .item-box-bottom-content {
                margin: 35px 0 0 0;
                padding: 0;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
  
                .quantity {
                  margin: 0;
                  padding: 0 0 0 7px;
                  position: relative;
                  background: var(--snjy-color-white);
                  height: 38px;
                  width: 148px;
                  display: inline-flex;
                  align-items: center;
                  justify-content: space-between;
                  border-radius: 5px;
                  border: 1px solid #ffd383;
                  font-size: var(--snjy-font-size-0-8);
                  font-weight: var(--snjy-font-weight-medium);
                  overflow: hidden;
  
                  span {
                    margin: 0;
                    padding: 0;
                    width: 40px;
                    height: 38px;
                    background: #ffd383;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    color: var(--snjy-color-dark-secondary);
                  }
                }
  
                .item-price {
                  margin: 0;
                  padding: 0;
                  position: relative;
                  font-size: var(--snjy-font-size-2);
                  font-weight: var(--snjy-font-weight-bold);
                  color: var(--snjy-color-dark-secondary);
                  line-height: 22px;
                  text-align: right;
  
                  span {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    font-size: var(--snjy-font-size-0-75);
                    font-weight: var(--snjy-font-weight-medium);
                    color: #687491;
                    display: block;
                  }
                }
  
              }
            }
          }
        }
  
        /*--SHIPPING METHOD--*/
        .thank-shipping-m {
          display: flex;
          justify-content: flex-start;
          gap: 0 3%;
  
          .thank-shipping-m-box {
            margin: 0;
            padding: 0;
            flex: 1;
            position: relative;
  
            h4 {
              margin: 0 0 4px 0;
              padding: 0;
              position: relative;
              font-size: var(--snjy-font-size-0-9);
              font-weight: var(--snjy-font-weight-bold);
              color: var(--snjy-button-color-primary);
              line-height: 17px;
            }
  
            small {
              margin: 0;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 0 10px;
              color: #687491;
              font-weight: var(--snjy-font-weight-medium);
              font-size: var(--snjy-font-size-0-8);
            }
  
            p {
              margin: 0;
              padding: 0;
              position: relative;
              font-size: var(--snjy-font-size-0-8);
              line-height: 18px;
              color: var(--snjy-color-text-ternary);
              font-weight: var(--snjy-font-weight-normal);
            }
  
          }
        }
  
      }
  
      .billing-info {
        margin: 0 0 20px 0;
        flex: 2;
        padding: 24px;
        background: var(--snjy-color-white);
        border: 1px solid var(--snjy-border-color-secondary);
        border-radius: 10px;
        box-shadow: var(--snjy-box-shadow);
        position: relative;
        height: fit-content;
  
        h3 {
          margin: 0 0 3px 0;
          padding: 0;
          position: relative;
          font-size: var(--snjy-font-size-1-125);
          font-weight: var(--snjy-font-weight-bold);
          color: #00216c;
          line-height: 20px;
        }
  
        small {
          margin: 0 0 5px 0;
          padding: 0;
          display: block;
          color: #687491;
          font-weight: var(--snjy-font-weight-medium);
          font-size: var(--snjy-font-size-0-8);
        }
  
        p.terms {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-8);
          color: #687491;
          font-weight: var(--snjy-font-weight-normal);
          line-height: 20px;
        }
  
  
        .billing-price {
          margin: 30px 0 0 0;
  
          button {
            width: 100%;
            border-radius: 8px;
            padding: 0;
            min-height: 50px;
            font-family: var(--snjy-font-family);
            background-color: var(--snjy-button-color-primary);
            color: var(--snjy-color-white);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0 5px;
            font-size: var(--snjy-font-size-0-875);
            line-height: 16px;
            font-weight: var(--snjy-font-weight-medium);
            margin: 20px 0 0 0;
          }
  
          ul {
            margin: 15px 0 0 0;
            padding: 7px 18px;
            list-style: none;
            border-radius: 8px;
            background: #f0f5f6;
  
            li {
              margin: 10px 0;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 0 10px;
              color: #687491;
              font-weight: var(--snjy-font-weight-medium);
              font-size: var(--snjy-font-size-0-8);
  
              span {
                color: #0077d7;
              }
            }
  
            .total-price {
              margin: 5px 0 !important;
              font-size: var(--snjy-font-size-1-25);
              font-weight: var(--snjy-font-weight-bolder);
              color: var(--snjy-color-dark-secondary);
  
              span {
                font-size: var(--snjy-font-size-1-25);
                color: var(--snjy-color-dark-secondary);
                font-weight: var(--snjy-font-weight-bold);
              }
            }
          }
        }
      }
    }
  }