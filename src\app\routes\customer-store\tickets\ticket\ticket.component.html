<section class="order-status-sec">
	<div class="order-status-body">
		<div class="all-main-title-sec">
			<h1>Create Service Request</h1>
			<div class="all-bedcrumbs">
				<ul>
					<li>
						<a [routerLink]="['/store/dashboard']">
							<span class="material-icons-outlined">home</span> Home
						</a>
					</li>
					<li>
						<a [routerLink]="['/store/customer-services']">
							<span class="material-icons-outlined">support_agent</span> Customer Service
						</a>
					</li>
					<li>Create Service Request</li>
				</ul>
			</div>
		</div>
		<div class="order-contact-list">
			<div class="order-c-box">
				<div class="order-c-icon"><img src="/assets/images/seller-icon.png" alt="" title="" /></div>
				<div class="order-c-details">
					<h4>Customer #</h4>
					<small>{{sellerDetails.bp_customer_number}}</small>
				</div>
			</div>
			<div class="order-c-box">
				<div class="order-c-icon"><img src="/assets/images/phone-icon.png" alt="" title="" /></div>
				<div class="order-c-details">
					<h4>Customer Name</h4>
					<small>{{sellerDetails.name}}</small>
				</div>
			</div>
			<div class="order-c-box address-box">
				<div class="order-c-icon"><img src="/assets/images/address-icon.png" alt="" title="" /></div>
				<div class="order-c-details">
					<h4>ADDRESS</h4>
					<small>{{sellerDetails.address}}</small>
				</div>
			</div>
		</div>
		<div class="order-status-form all-form-res">
			<form [formGroup]="form">
				<div class="form">
					<div class="form-group col-12">
						<label><span class="material-icons-outlined">subject</span> Subject</label>
						<input type="text" formControlName="Subject" class="form-control" placeholder="Subject"
							[ngClass]="{ 'is-invalid': submitted && f['Subject'].errors }" />
						<div *ngIf="submitted && f['Subject'].errors" class="invalid-feedback">
							<div *ngIf="submitted && f['Subject'].errors && f['Subject'].errors['required']">
								Subject is required
							</div>
						</div>
					</div>
					<div class="form-group col-12 mt-3">
						<label><span class="material-icons-outlined">chat</span> Message</label>
						<textarea class="form-control" placeholder="Message" rows="4" formControlName="Description"
							[ngClass]="{ 'is-invalid': submitted && f['Description'].errors }"></textarea>
						<div *ngIf="submitted && f['Description'].errors" class="invalid-feedback">
							<div *ngIf="submitted && f['Description'].errors && f['Description'].errors['required']">
								Description is required
							</div>
						</div>
					</div>
					<div class="form-group col-4 mt-3">
						<label><span class="material-icons-outlined">cloud_upload</span> Select a File</label>
						<input type="file" class="file-input" [accept]="allowedFileTypes"
							(change)="onFileSelected($event)" #fileInput multiple>
						<button type="button" (click)="fileInput.click()"
							class="upload-file-link d-flex flex-column gap-3 align-items-between">
							<p class="d-flex align-items-center gap-3" *ngIf="!attachments.length">
								<span class="material-icons-outlined">
									cloud_upload</span>
								<span>Choose File</span>
							</p>
							<ng-container *ngFor="let attachment of attachments;let i = index;">
								<p class="file-name-container">
									<span class="text-truncate">{{attachment.Name}}</span>
									<span class="material-icons-outlined red" *ngIf="attachment.Name"
										(click)="removeFile($event, i)">
										cancel
									</span>
								</p>
							</ng-container>
						</button>
					</div>
					<div class="form-group col-4 mt-3">
						<label><span class="material-icons-outlined">dataset_linked</span> Associated to
							(Contract/Order)</label>
						<input type="text" placeholder="Associated to" formControlName="SalesOrder" class="form-control"
							[ngbTypeahead]="search">
						<small *ngIf="loading" class="form-text text-muted">Searching...</small>
						<div class="invalid-feedback" *ngIf="searchFailed">Sorry, suggestions could not be loaded.</div>
						<div *ngIf="submitted && f['SalesOrder'].errors" class="invalid-feedback">
							<div *ngIf="submitted && f['SalesOrder'].errors && f['SalesOrder'].errors['required']">
								Associated to field is required
							</div>
						</div>
					</div>
					<div class="form-group col-3 mt-3">
						<label><span class="material-icons-outlined">all_inbox</span> Category (Case Type)</label>
						<select [ngClass]="{ 'is-invalid': submitted && f['Description'].errors }"
							class="form-control select-arrow-down" formControlName="Category">
							<option value="Account Administration">Account Administration</option>
							<option value="Change Request">Change Request</option>
							<option value="Complaint">Complaint</option>
							<option value="Inquiry">Inquiry</option>
							<option value="SAP Quartely Release Testing">SAP Quartely Release Testing</option>
							<option value="System Administration">System Administration</option>
							<option value="System Regression Testing">System Regression Testing</option>
						</select>
						<div *ngIf="submitted && f['Category'].errors" class="invalid-feedback">
							<div *ngIf="submitted && f['Category'].errors && f['Category'].errors['required']">
								Category is required
							</div>
						</div>
					</div>
					<div class="form-group col-4 mt-3">
						<label><span class="material-icons-outlined">inventory_2</span> Product/Module</label>
						<select class="form-control select-arrow-down">
							<option value="Employee Central">Employee Central</option>
							<option value="Integrations">Integrations</option>
							<option value="Platform (PLT)/SSO">Platform (PLT)/SSO</option>
							<option value="Profile">Profile</option>
							<option value="Recruiting Management (RM)">Recruiting Management (RM)</option>
							<option value="Recruiting Marketing (RMK)">Recruiting Marketing (RMK)</option>
							<option value="Reporting">Reporting</option>
						</select>
					</div>
					<div class="form-group col-4 mt-3">
						<label><span class="material-icons-outlined">report</span> Severity</label>
						<input type="text" placeholder="Severity" class="form-control" />
					</div>
					<div class="form-group col-3 mt-3">
						<label><span class="material-icons-outlined">emergency</span> Urgency</label>
						<input type="text" placeholder="Urgency" class="form-control" />
					</div>
					<div class="form-btn-sec">
						<button type="button" class="order-s-btn back-btn" (click)="goBack()">Back</button>
						<button type="button" class="btn order-s-btn" [disabled]="saving" (click)="onSubmit()">{{ saving
							?
							'Submitting...' : 'Submit' }}</button>
					</div>
				</div>
			</form>
		</div>
	</div>
</section>