import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { SharedModule } from 'src/app/shared/shared.module';
import { InvoiceDetailsComponent } from './invoice-details.component';

@NgModule({
  declarations: [
    InvoiceDetailsComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: '', component: InvoiceDetailsComponent }]),
  ]
})
export class InvoiceDetailsModule { }
