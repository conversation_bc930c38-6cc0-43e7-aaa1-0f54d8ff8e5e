import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { NavigationBarComponent } from "./components/navigation-bar/navigation-bar.component";
import { NavigationSidePanelComponent } from "./components/navigation-side-panel/navigation-side-panel.component";
import { TranslateModule } from "@ngx-translate/core";
import { GridColumnComponent } from "./components/grid-column/grid-column.component";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { ToObservablePipe } from "./pipes/to-observable.pipe";
import { RemoteGridBindingDirective } from "./directives/remote-grid-binding.directive";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { ProductViewComponent } from "./components/product-view/product-view.component";
import { TabComponent } from "./components/tab/tab.component";
import { TabsComponent } from "./components/tab/tabs.component";
import { ImgFallbackDirective } from "./directives/image-fallback.directive";
import { GroupByPipe } from "./pipes/groupby.pipe";
import { GetProductImagePipe } from "./pipes/get-product-image.pipe";
import { ItemCounterComponent } from "./components/item-conuter/item-counter.component";
import { ProductAddedSidebarComponent } from "./components/product-added-sidebar/product-added-sidebar.component";
import { GetCompanyLogoPipe } from "./pipes/get-company-logo.pipe";
import { SortDescByDatePipe } from "./pipes/sort-desc-by-date.pipe";
import { LoadingBarComponent } from "./components/loading-bar/loading-bar.component";
import {
  NgbCarouselModule,
  NgbDatepickerModule,
  NgbDropdownModule,
  NgbToastModule,
} from "@ng-bootstrap/ng-bootstrap";
import { ToastContainerComponent } from "./components/toast-container/toast-container.component";
import { LoaderComponent } from "./components/loader/loader.component";
import { WarrantyComponent } from "./components/warranty/warranty.component";
import { CustomHeader } from "./components/grid-header/grid-header.component";
import { GridComponent } from "./components/grid/grid.component";
import { AgGridModule } from "ag-grid-angular";
import { ManageUsersComponent } from "./components/manage-users/manage-users.component";
import { RegisterUserComponent } from "./components/manage-users/register-user/register-user.component";
import { UserDetailComponent } from "./components/manage-users/user-detail/user-detail.component";
import { NgSelectModule } from "@ng-select/ng-select";
import { ManageUserCustomerComponent } from "./components/manage-users/manage-user-customer/manage-user-customer.component";
import { BtnDelRendererComponent } from "./components/manage-users/renderer/btn-del-renderer/btn-del-renderer.component";
import { SelectCustomerComponent } from "./components/manage-users/select-customer/select-customer.component";
import { InputRadioBtnComponent } from "./components/manage-users/renderer/input-radio-btn/input-radio-btn.component";
import { SalesOrdersSchedulerComponent } from "./components/sales-orders-scheduler/sales-orders-scheduler.component";
import { CheckFeatureDirective } from "./directives/check-feature/check-feature.directive";
import { CheckPermissionDirective } from "./directives/check-permission/check-permission.directive";
import { BannerPreviewComponent } from "./components/banner-preview/banner-preview.component";

const MODULES: any[] = [
  CommonModule,
  RouterModule,
  FormsModule,
  ReactiveFormsModule,
  DragDropModule,
  TranslateModule,
];
@NgModule({
  declarations: [
    LoadingBarComponent,
    NavigationBarComponent,
    NavigationSidePanelComponent,
    GridColumnComponent,
    ItemCounterComponent,
    ToObservablePipe,
    RemoteGridBindingDirective,
    ImgFallbackDirective,
    ProductViewComponent,
    TabsComponent,
    TabComponent,
    GroupByPipe,
    GetProductImagePipe,
    GetCompanyLogoPipe,
    ProductAddedSidebarComponent,
    SortDescByDatePipe,
    ToastContainerComponent,
    LoaderComponent,
    WarrantyComponent,
    CustomHeader,
    GridComponent,
    ManageUsersComponent,
    RegisterUserComponent,
    UserDetailComponent,
    ManageUserCustomerComponent,
    BtnDelRendererComponent,
    SelectCustomerComponent,
    InputRadioBtnComponent,
    SalesOrdersSchedulerComponent,
    CheckFeatureDirective,
    CheckPermissionDirective,
    BannerPreviewComponent,
  ],
  imports: [
    ...MODULES,
    NgbDropdownModule,
    NgbToastModule,
    AgGridModule,
    NgSelectModule,
    NgbDatepickerModule,
    NgbCarouselModule,
  ],
  exports: [
    ...MODULES,
    LoaderComponent,
    ToastContainerComponent,
    NavigationBarComponent,
    NavigationSidePanelComponent,
    ItemCounterComponent,
    GridColumnComponent,
    RemoteGridBindingDirective,
    ImgFallbackDirective,
    ToObservablePipe,
    ProductViewComponent,
    TabsComponent,
    TabComponent,
    GroupByPipe,
    GetProductImagePipe,
    GetCompanyLogoPipe,
    ProductAddedSidebarComponent,
    SortDescByDatePipe,
    LoadingBarComponent,
    WarrantyComponent,
    GridComponent,
    ManageUsersComponent,
    SalesOrdersSchedulerComponent,
    CheckFeatureDirective,
    CheckPermissionDirective,
    BannerPreviewComponent,
  ],
})
export class SharedModule {}
