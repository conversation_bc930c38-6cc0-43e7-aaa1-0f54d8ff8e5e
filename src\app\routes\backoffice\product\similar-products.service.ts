import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiConstant } from 'src/app/constants/api.constants';

@Injectable({
  providedIn: 'root'
})
export class SimilarProductsService {

  private url = ApiConstant.SIMILAR_PRODUCTS;

  constructor(private http: HttpClient) { }

  get(productId: string) {
    return this.http.get<any>(this.url + '?productID=' + productId);
  }

  save(data: any) {
    return this.http.post<any>(this.url, data);
  }

  update(data: any, id: string) {
    return this.http.put<any>(`${this.url}/${id}`, data);
  }

  delete(id: string) {
    return this.http.delete<any>(`${this.url}/${id}`);
  }

}
