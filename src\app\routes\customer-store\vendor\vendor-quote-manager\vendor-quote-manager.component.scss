:host {
    padding: 0 25px 25px 25px;
  }
  
  .vendor-quote-manager-sec {
    margin: 0 0 15px 0;
    padding: 30px;
    background: var(--snjy-color-white);
    border-radius: 12px;
    border: 1px solid rgba(208, 215, 216, 0.3882352941);
    box-shadow: var(--snjy-box-shadow);
  
    .vendor-quote-manager-body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: space-between;
      position: relative;
      gap: 0;
      flex-wrap: wrap;
  
      .vendor-quote-manager-contact-list {
        margin: 0 0 15px 0;
        padding: 18px 20px;
        background: #e5ecf3;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 12px;
        width: 100%;
  
        .vendor-quote-manager-c-box {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0 10px;
          flex: 1;
  
          .vendor-quote-manager-c-icon {
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            background: var(--snjy-font-color-primary);
            border-radius: 50px;
            box-shadow: 0 1px 3px #6186b130;
          }
  
          .vendor-quote-manager-c-details {
            margin: 0;
            padding: 6px 0 0 0;
  
            h4 {
              margin: 0;
              padding: 0;
              font-size: var(--snjy-font-size-0-8);
              text-transform: uppercase;
              font-weight: var(--snjy-font-weight-bold);
              color: var(--snjy-button-color-primary);
              line-height: 13px;
            }
  
            small {
              margin: 0;
              padding: 0;
              font-size: var(--snjy-font-size-0-8);
              text-transform: uppercase;
              font-weight: var(--snjy-font-weight-medium);
              color: #2e3237;
              line-height: 20px;
            }
          }
        }
  
        .address-box {
          flex: 0 0 46% !important;
        }
      }
  
      .inv-form-sec {
        margin: 0;
        padding: 30px;
        position: relative;
        flex: 0 0 100%;
        background: var(--snjy-font-color-primary);
        border-radius: 12px;
        border: 1px solid #ddd;
  
        .form {
          display: flex;
          justify-content: space-between;
          gap: 0;
          flex-wrap: wrap;
          position: relative;
        }
  
        .form-group {
          margin: 0 0 14px 0;
          position: relative;
          flex: 0 0 24%;
  
          label {
            margin: 0 0 8px 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-875);
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-color-dark-secondary);
            line-height: 14px;
            display: flex;
            align-items: center;
            gap: 0 2px;
  
            .material-icons-outlined {
              margin: 0;
              padding: 0;
              font-size: var(--snjy-font-size-1-125);
              color: #a2adb9;
              width: fit-content;
              height: fit-content;
            }
          }
  
          .form-control {
            margin: 0;
            padding: 0 14px;
            height: 44px;
            background: #f0f7ff;
            font-size: var(--snjy-font-size-0-875);
            font-weight: var(--snjy-font-weight-medium);
            border: 1px solid #8699a961;
          }
  
        }
  
        .form-btn-sec {
          margin: 12px auto 0 auto;
          flex: 0 0 270px;
  
          .order-s-btn {
            border-radius: 10px;
            display: flex;
            font-size: var(--snjy-font-size-0-875);
            height: 46px;
            justify-content: center;
            align-items: center;
            line-height: 14px;
            min-width: 100%;
            padding-bottom: 0;
            padding-top: 0;
            cursor: pointer;
            color: var(--snjy-color-white);
            background: var(--snjy-button-gradient);
            position: relative;
            text-transform: uppercase;
            font-weight: var(--snjy-font-weight-medium);
            transition: all0 0.3s ease-in-out;
            border: none;
          }
        }
      }
    }
  }
  
  .vendor-quote-manager-table {
    margin: 0 0 30px 0;
    padding: 30px;
    background: var(--snjy-color-white);
    border-radius: 12px;
    border: 1px solid rgba(208, 215, 216, 0.3882352941);
    box-shadow: var(--snjy-box-shadow);
  }
  
  @media only screen and (max-width: 1024px) {
    .vendor-quote-manager-sec .vendor-quote-manager-body .vendor-quote-manager-contact-list .address-box {
      flex: none !important;
    }
  }
  
  :host::ng-deep {
    .download {
      color: red !important;
      font-size: var(--snjy-font-size-2) !important;
    }
  }