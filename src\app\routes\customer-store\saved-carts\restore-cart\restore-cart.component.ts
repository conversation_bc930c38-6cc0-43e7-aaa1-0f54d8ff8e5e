import { Component, Input } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { SavedCartsService } from '../saved-carts.service';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-restore-cart',
  templateUrl: './restore-cart.component.html',
  styleUrls: ['./restore-cart.component.scss']
})
export class RestoreCartComponent {

  @Input() data: any = {};

  form = this.fb.group({
    keepCopy: [false],
  });
  saving = false;

  get f() {
    return this.form.controls;
  }

  constructor(
    public fb: FormBuilder,
    private _snackBar: AppToastService,
    private service: SavedCartsService,
    public activeModal: NgbActiveModal
  ) {

  }
  onSubmit(): void {
    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    this.service.restore(this.data.id, !!this.form.value.keepCopy).subscribe({
      next: (data) => {
        this.onReset();
        this.activeModal.close(data);
        this.saving = false;
        this._snackBar.open('Cart restored successfully!');
      },
      error: (err) => {
        this.saving = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      },
    })
  }

  onReset(): void {
    this.form.reset();
  }

}
