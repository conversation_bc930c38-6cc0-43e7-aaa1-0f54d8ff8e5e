import { Component, OnInit, AfterViewInit } from "@angular/core";

import { SettingsService } from "../../backoffice/settings/settings.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { AuthService } from "src/app/core/authentication/auth.service";

declare global {
  interface Window {
    botpress: any;
  }
}
@Component({
  selector: "app-customer-dashboard",
  templateUrl: "./customer-dashboard.component.html",
  styleUrls: ["./customer-dashboard.component.scss"],
})
export class CustomerDashboardComponent implements OnInit {
  public customer_id: any = null;
  public settings: any = null;
  public links: any[] = [
    {
      routerLink: "/store/catalogues",
      label: "Product Catalog",
      img: "/assets/images/catalogs.svg",
      permission: "P0017",
    },
    {
      routerLink: "/store/quick-order",
      label: "Quick Order",
      img: "/assets/images/quick-order.svg",
      feature: "F0003",
      permission: "P0004",
    },
    {
      routerLink: "/store/saved-cart",
      label: "Saved Carts",
      img: "/assets/images/carts.svg",
      feature: "F0007",
      permission: "P0014",
    },
    {
      routerLink: "/store/order-history",
      label: "Orders",
      img: "/assets/images/order-status.svg",
      feature: "F0006",
      permission: "P0011",
    },
    {
      routerLink: "/store/invoices",
      label: "Invoices",
      img: "/assets/images/invoices.svg",
      feature: "F0002",
      permission: "P0003",
    },
    {
      routerLink: "/store/customer-services",
      label: "Customer Service",
      img: "/assets/images/customer-service.svg",
      feature: "F0001",
      permission: "P0002",
    },
    {
      routerLink: "/store/quote-history",
      label: "Quotes",
      img: "/assets/images/quote-history.svg",
      feature: "F0004",
      permission: "P0005",
    },
    {
      routerLink: "/store/return-order-history",
      label: "Returns",
      img: "/assets/images/return-order.svg",
      feature: "F0005",
      permission: "P0008",
    },
    {
      routerLink: "/store/replenishment",
      label: "Scheduled Orders",
      materialFontName: "cloud_sync",
      feature: "F0008",
      permission: "P0015",
    },
    {
      routerLink: null,
      label: "Order Guide",
      materialFontName: "list_alt",
      permission: "P0038",
    },
    {
      routerLink: null,
      label: "Approval Dashboard",
      materialFontName: "done",
      permission: "P0039",
    },
    {
      routerLink: "/backoffice/dashboard",
      label: "Administration",
      materialFontName: "admin_panel_settings",
      permission: "P0027",
    },
    {
      routerLink: "/store/sales",
      label: "Sales Portal",
      materialFontName: "storefront",
      permission: "P0040",
    },
    {
      routerLink: "/store/vendor",
      label: "Vendor Portal",
      materialFontName: "warehouse",
      permission: "P0050",
    },
  ];

  constructor(
    private _snackBar: AppToastService,
    private service: SettingsService,
    private authService: AuthService
  ) {
    const auth = this.authService.getAuth();
    this.customer_id = auth?.partner_function?.customer_id || null;
  }

  ngOnInit(): void {
    this.getSettings();
  }

  getSettings() {
    this.service.getSettings().subscribe({
      next: (data) => {
        this.settings = data;
      },
      error: (e) => {
        this._snackBar.open("Error while processing seetings request.", {
          type: "Error",
        });
      },
    });
  }

  ngAfterViewInit(): void {
    const script = document.createElement('script');
    script.src = 'https://cdn.botpress.cloud/webchat/v2.4/inject.js';
    script.async = true;
    script.onload = () => {
      window.botpress.init({
        'botId': '2332e3bf-ef53-4c17-ad8b-fca862e5898f',
        'configuration': {
          'botName': 'SNJYAi',
          'botAvatar': 'https://files.bpcontent.cloud/2025/06/05/16/20250605164945-Y50MMXJD.png',
          'fabImage': 'https://files.bpcontent.cloud/2025/06/05/16/20250605164945-Y50MMXJD.png',
          'website': {
            'title': 'https://asardigital.com',
            'link': 'https://asardigital.com'
          },
          'email': {
            'title': '',
            'link': ''
          },
          'phone': {},
          'termsOfService': {},
          'privacyPolicy': {},
          'color': '#5eb1ef',
          'variant': 'soft',
          'themeMode': 'light',
          'fontFamily': 'inter',
          'radius': 1
        },
        'clientId': 'c83c435c-7769-4874-b3b9-b21bbdc53d71',
        'selector': '#webchat',
      });
    };

    setTimeout(() => {
      const webchatIframe = document.querySelector('iframe[name="webchat"]') as HTMLIFrameElement;
      if (webchatIframe && webchatIframe.contentDocument) {

        const styleTag = webchatIframe.contentDocument.createElement('style');
        styleTag.innerHTML = `
          .bpContainer {
              background: linear-gradient(45deg, #0080ef, #0053f7);
              padding: 4px;
              border: none;
          }

          .bpHeaderContainer {
              background: no-repeat;
              border-radius: 0;
              box-shadow: none;
          }

          .bpHeaderContentContainer {
              padding: 12px 0 12px 12px;
              background: none !important;
          }

          .bpHeaderContentAvatarContainer {
              height: 48px;
              width: 48px;
              background: #FFF;
              align-items: center;
              justify-content: center;
              display: flex;
          }

          .bpHeaderContentTitle {
              color: #FFF;
          }

          .bpHeaderContentActionsIcons {
              color: #FFF;
              background: none !important;
              outline: none !important;
          }

          .bpMessageListMarqueeContainer {
              margin: auto;
          }

          .bpMessageListContainer {
              background: #FFF;
              border-radius: 16px 16px 0 0;
              padding: 0;
          }

          .bpMessageListMarqueeAvatarContainer {
              margin: 0;
              background: #e3e5f7;
          }

          .bpMessageListMarqueeTitle {
              color: #000000;
              font-weight: bold;
          }

          .bpMessageBlocksButton {
              padding: .4rem 1rem;
              border-radius: 30px;
              background: #FFF !important;
              color: #0053f7;
              border: 1px solid #0053f7 !important;
          }

          .bpMessageBlocksButton:hover {
              color: #000;
              border: 1px solid #000 !important;
          }

          .bpMessageBlocksBubble[data-direction=outgoing] {
              background: linear-gradient(45deg, #0080ef, #0053f7);
              color: #FFF !important;
          }

          .bpComposerContainer {
              margin: 0;
              background: #FFF;
              border-radius: 0 0 12px 12px;
              border: none;
              border-top: 1px solid #ebebeb !important;
          }

          .bpComposerInput {
              height: 52px !important;
              min-height: 40px !important;
              line-height: 40px;
              font-size: 15px;
          }

          .bpComposerVoiceButtonContainer {
              margin: var(--bpSpacing-2);
          }

          .bpComposerVoiceButtonIcon {
              padding: 9px;
              background: linear-gradient(45deg, #0080ef, #0053f7);
              height: var(--bpSpacing-9);
              width: var(--bpSpacing-9);
          }

          .bpComposerButtonContainer {
              margin: var(--bpSpacing-2);
              padding: var(--bpSpacing-1);
          }

          .bpComposerButtonIcon {
              padding: 9px;
              background: linear-gradient(45deg, #0080ef, #0053f7);
              height: var(--bpSpacing-9);
              width: var(--bpSpacing-9);
          }

          .bpModalContainer {
              padding: var(--bpSpacing-6);
          }
            
          .bpModalDialogNewConversationButton {
              padding: .4rem 1rem;
              border-radius: 30px;
              background: #0053f7 !important;
              color: #FFF;
              border: 1px solid #0053f7 !important;
              cursor: pointer;
          }

          .bpComposerPoweredBy {
              display: none !important;
          }
        `;
        webchatIframe.contentDocument.head.appendChild(styleTag);
      }
    }, 500);

    setTimeout(() => {
      const fabIframe = document.querySelector('iframe[name="fab"]') as HTMLIFrameElement;
      if (fabIframe && fabIframe.contentDocument) {

        const styleTag = fabIframe.contentDocument.createElement('style');
        styleTag.innerHTML = `
          .bpFabContainer {
              background: linear-gradient(45deg, #0080ef, #3500f5) !important;
          }
          .bpFabIcon {
              background-color: #ffffff;
          }
        `;
        fabIframe.contentDocument.head.appendChild(styleTag);
      }
    }, 500);

    document.body.appendChild(script);
  }


}
