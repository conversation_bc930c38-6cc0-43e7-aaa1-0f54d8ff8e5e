<section class="facet" *ngFor="let facet of facets; let i = index">
  <div class="my-3 section" *ngIf="facet?.visible">
    <h6 class="font-weight-bold"> Shop by {{ facet.name }}</h6>
    <form class="brand" *ngFor=" let child of facet.values | slice : 0 : (facet.showMore ? facet.values?.length : maxShow)" >
      <div *ngIf="child?.visible">
        <label class="tick">
          <small class="title-label"><ng-container *ngIf="child.values?.length"> {{ child.name }} </ng-container></small>
          <ng-container *ngIf="!child.values?.length"> 
            <input type="checkbox" [checked]="child.selected" (change)="select(child)" />
            <span class="material-icons-outlined checked"> check_box </span>
            <span class="material-icons-outlined unchecked"> check_box_outline_blank </span>
            <small>{{ child.name }} ({{child.product_count}})</small>
          </ng-container>
          <span></span>
        </label>
      </div>
      <ng-container *ngFor="let ch of child.values">
        <form class="child-brand" *ngIf="ch?.visible">
          <div>
            <label class="tick">
              <input type="checkbox" [checked]="ch.selected" (change)="select(ch)" />
              <span class="material-icons-outlined checked"> check_box </span>
              <span class="material-icons-outlined unchecked"> check_box_outline_blank </span>
              <small>{{ ch.name }} ({{ch.product_count}})</small>
              <span></span>
            </label>
          </div>
        </form>
      </ng-container>
      
    </form>
    <ng-container *ngIf="filterValues(facet.values).length > maxShow">
      <a (click)="facet.showMore = !facet.showMore">
        <span>Show <span [innerHtml]="facet.showMore ? 'less' : 'More'"> </span></span>
        <span>{{ facet.name }}</span>
      </a>
    </ng-container>
  </div>
</section>
