import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from "@angular/forms";
import { Subject, takeUntil } from "rxjs";

import { OrderDetailsService } from "../order-details/order-details.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { AuthService } from "src/app/core/authentication/auth.service";
import { ProductReturnService } from "./product-return.service";
import { SettingsService } from "../../backoffice/settings/settings.service";

@Component({
  selector: "app-product-return",
  templateUrl: "./product-return.component.html",
  styleUrls: ["./product-return.component.scss"],
})
export class ProductReturnComponent implements OnInit, OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  public loading: any = false;
  public submitted: any = false;
  public isInitiateReturn: any = false;
  public step: any = 1;
  public orderID: any = null;
  public orderType: any = null;
  public orderDetails: any = null;
  public sellerDetails: any = {};
  public form: FormGroup;
  public returnReason: any = [];

  constructor(
    public router: Router,
    public fb: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private service: OrderDetailsService,
    private _snackBar: AppToastService,
    public authService: AuthService,
    public productReturnService: ProductReturnService,
    private settingsService: SettingsService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit() {
    this.getReasonReturn();
    this.activatedRoute.paramMap
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((params) => {
        this.orderID = params.get("id");
        this.orderType = params.get("type");
        if (this.orderID && this.orderType) {
          this.loading = true;
          this.createForm();
          this.getSettings();
        }
      });
  }

  getReasonReturn() {
    this.productReturnService
      .getAllReturnReason()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.returnReason = res?.data || [];
        },
        error: (err) => {
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  createForm() {
    this.form = this.fb.group({
      CustomerReturnType: ["CBAR"],
      SoldToParty: [this.sellerDetails.customer_id],
      SDDocumentReason: ["009"],
      ReferenceSDDocument: [this.orderID],
      to_Item: this.fb.group({
        results: this.fb.array([], [this.customValidateArrayGroup()]),
      }),
    });
  }

  customValidateArrayGroup() {
    return function validate(
      formArr: AbstractControl
    ): ValidationErrors | null {
      const filtered = formArr.value.filter((val: any) => val.Selected);
      return filtered.length ? null : { hasError: true };
    };
  }

  insertProduct(product: any): FormGroup {
    const productGroup: any = this.fb.group({
      RequestedQuantity: [""],
      ReturnReason: [""],
      CustRetItmFollowUpActivity: ["0001"],
      ReturnsRefundType: [""],
      ReturnsRefundProcgMode: ["R"],
      ReturnsRefundExtent: [""],
      ReferenceSDDocument: [this.orderID],
      ReferenceSDDocumentItem: [product?.ITM_NUMBER],
      Product: [product],
      Selected: [false],
    });
    const RequestedQuantity: any = productGroup.get("RequestedQuantity");
    const ReturnsRefundType: any = productGroup.get("ReturnsRefundType");
    const ReturnReason: any = productGroup.get("ReturnReason");
    const Product: any = productGroup.get("Product");
    productGroup
      .get("Selected")
      .valueChanges.pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((value: any) => {
        if (value) {
          RequestedQuantity.setValidators([
            Validators.required,
            Validators.min(1),
            Validators.max(parseInt(Product.value.REQ_QTY)),
            Validators.pattern("^[0-9]*$"),
          ]);
          ReturnsRefundType.setValidators(Validators.required);
          ReturnReason.setValidators(Validators.required);
        } else {
          RequestedQuantity.clearValidators();
          ReturnsRefundType.clearValidators();
          ReturnReason.clearValidators();
        }
        ReturnsRefundType.updateValueAndValidity();
        RequestedQuantity.updateValueAndValidity();
        ReturnReason.updateValueAndValidity();
      });
    return productGroup;
  }

  get f(): any {
    return this.form.controls;
  }

  get results(): any {
    return this.f.to_Item.get("results");
  }

  updateToItem(ORDER_LINE_DETAIL: any) {
    ORDER_LINE_DETAIL.forEach((product: any) => {
      this.results.push(this.insertProduct(product));
    });
  }

  getSettings() {
    this.settingsService
      .getSettings()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (data: any) => {
          if (data?.cust_return_type_code) {
            this.form.patchValue({
              CustomerReturnType: data?.cust_return_type_code,
            });
          }
          this.getOrderDetails();
        },
        error: (e: any) => {
          console.error("Error while processing settings request.", e);
        },
      });
  }

  getOrderDetails() {
    this.service
      .get(this.orderID, this.orderType)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          if (res?.data?.SALESORDER) {
            this.orderDetails = res?.data?.SALESORDER || null;
            const ORDER_LINE_DETAIL =
              this.orderDetails?.ORDER_LINE_DETAIL || [];
            this.updateToItem(ORDER_LINE_DETAIL);
          } else {
            this._snackBar.open("No data received.");
          }
          this.loading = false;
        },
        error: (err) => {
          this.loading = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  next() {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    this.step++;
  }

  previous() {
    this.step--;
  }

  initiateReturn() {
    const payload: any = JSON.parse(JSON.stringify(this.form.value));
    payload.to_Item.results = payload.to_Item.results
      .filter((r: any) => r.Selected)
      .map((r: any) => {
        r.ReturnsRefundType = r.ReturnsRefundType === "1" ? "1" : "";
        delete r.Selected;
        delete r.Product;
        return r;
      });
    this.isInitiateReturn = true;
    this.productReturnService
      .create(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.isInitiateReturn = false;
          const ID: any = res?.data?.RETURNORDERCREATE?.CustomerReturn || "";
          this._snackBar.open(
            `Return Order Created successfully with ID ${ID}.`
          );
          this.router.navigate([
            `/store/order/${this.orderID}/${this.orderType}`,
          ]);
        },
        error: (err) => {
          this.isInitiateReturn = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
