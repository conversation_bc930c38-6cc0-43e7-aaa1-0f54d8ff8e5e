:host {
  padding: 25px;
  overflow: auto;
}

.all-main-title-sec.cart-details-main-title {
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  padding: 0 0 25px 0;

  h1 {
    text-align: left;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 0 10px;
    justify-content: space-between;
  }
}

.order-status-form {
  margin: 15px 0 0 0;
  padding: 30px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);

  .form {
    display: flex;
    justify-content: space-between;
    gap: 0 2%;
    flex-wrap: wrap;

    .form-group {
      flex: 0 0 49%;
      margin: 0 0 15px 0;
      position: relative;

      &:before {
        position: absolute;
        content: "";
        font-family: "Material Icons Outlined";
        top: 39px;
        right: 10px;
        bottom: 0;
        margin: auto 0;
        font-size: var(--snjy-font-size-1-25);
        color: var(--snjy-color-dark-secondary);
        font-weight: var(--snjy-font-weight-normal);
        line-height: 23px;
      }

      &:before {
        color: #b7b7b7;
      }

      label {
        margin: 0 0 8px 0;
        padding: 0;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        color: var(--snjy-color-dark-secondary);
        line-height: 14px;
        display: flex;
        align-items: center;
        gap: 0 2px;

        .material-icons-outlined {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-1-25);
          color: #b3b3b3;
          width: fit-content;
          height: fit-content;
        }
      }

      .form-control {
        margin: 0;
        padding: 0 11px;
        height: 44px;
        background: #f0f7ff;
        border-radius: 8px;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        border: 1px solid #8699a961;

        &::placeholder {
          color: #a1a1a1;
          font-weight: var(--snjy-font-weight-semi-bold);
        }

        &.select-arrow-down {
          background-position-x: 99% !important;
        }
      }

      select.form-control {
        -webkit-appearance: none;
      }
    }

    .form-btn-sec {
      margin: 15px 0 0 0;
      -webkit-box-flex: 0;
      flex: 0 0 100%;
      max-width: 100%;
      text-align: center;

      .order-s-btn {
        border-radius: 10px;
        display: flex;
        font-size: var(--snjy-font-size-0-875);
        height: 48px;
        align-items: center;
        justify-content: center;
        line-height: 14px;
        min-width: 270px;
        padding-bottom: 0;
        padding-top: 0;
        cursor: pointer;
        color: var(--snjy-color-white);
        background: var(--snjy-button-gradient);
        position: relative;
        margin: 0 auto;
        text-transform: uppercase;
        font-weight: var(--snjy-font-weight-medium);
        transition: all0 0.3s ease-in-out;
        border: none;
      }
    }
  }
}