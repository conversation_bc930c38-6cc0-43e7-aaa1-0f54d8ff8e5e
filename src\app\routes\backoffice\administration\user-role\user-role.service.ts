import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Observable, map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class UserRoleService {
  constructor(private http: HttpClient) {}

  getUserRoles(): Observable<any[]> {
    return this.http.get<any[]>(`${ApiConstant.USER_ROLES}`).pipe(
      map((res: any) => {
        const userRoles: any = res?.data || [];
        userRoles.forEach((role: any) => {
          role.roleHierarchy = [];

          const getHierarchyIds = (roleId: any) => {
            const childRoles = userRoles.filter(
              (item: any) => item.parent_role_id === roleId
            );
            for (const childRole of childRoles) {
              role.roleHierarchy.push(childRole.id);
              getHierarchyIds(childRole.id);
            }
          };

          getHierarchyIds(role.id);
        });
        return { ...res, data: userRoles };
      })
    );
  }

  getUserRoleById(userRoleId: number): Observable<any> {
    return this.http.get<any>(`${ApiConstant.USER_ROLES}/${userRoleId}`);
  }

  createUserRole(userRole: any): Observable<any> {
    return this.http.post<any>(`${ApiConstant.USER_ROLES}`, userRole);
  }

  updateUserRole(userRoleId: number, updatedUserRole: any): Observable<any> {
    return this.http.put<any>(
      `${ApiConstant.USER_ROLES}/${userRoleId}`,
      updatedUserRole
    );
  }

  deleteUserRole(userRoleId: number): Observable<any> {
    return this.http.delete<any>(`${ApiConstant.USER_ROLES}/${userRoleId}`);
  }

  getUserRolePermissions(userRoleId: number): Observable<any[]> {
    const buildHierarchy = (items: any[], parentId: number | null = null) => {
      const result: any[] = [];
      for (const item of items) {
        item.toggle = true;
        if (item.parent_permission_id === parentId) {
          item.child = buildHierarchy(items, item.id);
          result.push(item);
        }
      }
      return result;
    };
    return this.http
      .get<any[]>(`${ApiConstant.USER_ROLES}/${userRoleId}/permissions`)
      .pipe(
        map((res: any) => {
          const data: any = res?.data || [];
          res.data = buildHierarchy(data);
          return res;
        })
      );
  }

  turnOnUserRolePermissions(
    userRoleId: number,
    permission_ids: any
  ): Observable<any> {
    return this.http.put<any>(
      `${ApiConstant.USER_ROLES}/${userRoleId}/permissions`,
      { permission_ids }
    );
  }
}
