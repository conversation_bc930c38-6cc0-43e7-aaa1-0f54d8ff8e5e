import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

export interface CustomerListParam {
  perPage?: number;
  pageNo?: number;
  search?: string;
  searchBy?: string;
  [param: string]: any;
}

export interface CustomerSearchList {
  data: any[];
  total: number
}

@Injectable({
  providedIn: "root",
})
export class CustomerService {
  constructor(private http: HttpClient) { }

  getAll(data: any) {
    const params = new HttpParams().appendAll({ ...data, ...{ search: data.search, searchBy: data.searchBy } });
    return this.http.get<CustomerSearchList>(ApiConstant.PARTNERS, {
      params,
    });
  }

  getCustomerByID(custId: string) {
    return this.http.get(`${ApiConstant.CUSTOMERS}/${custId}`).pipe(map((res: any) => res.data));
  }

  getTexts(custId: string) {
    return this.http.get<CustomerSearchList>(`${ApiConstant.CUSTOMER_TEXT}?customerID=${custId}`).pipe(map(res => res.data));
  }

  getCompanies(custId: string) {
    return this.http.get<CustomerSearchList>(`${ApiConstant.CUSTOMER_COMPANIES}?customerID=${custId}`).pipe(map(res => res.data));
  }

  getPartnerFunction(custId: string) {
    return this.http.get<CustomerSearchList>(`${ApiConstant.CUSTOMER_PARTNER_FUNCTION}?customerID=${custId}`).pipe(map(res => res.data));
  }

  getSalesAreas(custId: string) {
    return this.http.get<CustomerSearchList>(`${ApiConstant.CUSTOMER_SALES_AREA}?customerID=${custId}`).pipe(map(res => res.data));
  }

}
