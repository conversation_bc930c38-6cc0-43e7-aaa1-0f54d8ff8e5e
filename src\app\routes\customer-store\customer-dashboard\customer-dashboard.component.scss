:host {
    flex-grow: 1;
}

.main-container {
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    height: 100%;
    overflow: auto;

    .dashboard-video-sec {
        display: flex;
        align-items: center;
        justify-content: center;
        height: calc(100vh - 40vh);
        position: relative;
        overflow: hidden;

        &:before {
            position: absolute;
            content: '';
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #00000054;
            z-index: 1;
        }

        video {
            margin: 0;
            padding: 0;
            position: absolute;
            left: 0;
            right: 0;
            width: 100%;
            height: auto;
        }
    }

    .d-company-name {
        margin: 0 auto 50px auto;
        padding: 0;
        max-width: 100%;
        width: 100%;

        .company-name {
            font-size: 3rem;
            color: var(--snjy-font-color-primary) !important;
        }

        .welcome-msg {
            font-size: 4rem;
            color: var(--snjy-font-color-primary) !important;
        }
    }

    .links-container {
        max-width: 1500px;
        width: 100%;

        .circle {
            background: var(--snjy-button-gradient);
            box-shadow: 0px 7px 11px rgba(0, 0, 0, 0.11);
            height: 75px;
            width: 75px;
            border-radius: 18px;
            margin: 0 auto;

            &:hover {

                i,
                img {
                    opacity: 1;
                }
            }

            img {
                opacity: .7;
                height: 30px;
                width: 30px;
            }

            i {
                color: white;
                font-size: 30px;
                opacity: .7;
            }

        }

        .desc {
            font-weight: var(--snjy-font-weight-bold);
            font-size: var(--snjy-font-size-0-75);
            color: var(--snjy-color-dark-secondary);
            text-align: center;
            text-transform: uppercase;
        }
    }

    .chatbot-container {
        border: none;
        position: absolute;
        bottom: 0;
        z-index: 99;
        right: 0;
    }

    button.chatbot:not(.dp-btn),
    button.chatbot:not(.dp-btn):disabled,
    button.chatbot:not(.dp-btn):hover {
        border-radius: 8px;
        padding: .5rem 1.5rem;
        font-family: var(--snjy-font-family);
        background-color: var(--snjy-button-color-primary);
        color: var(--snjy-color-white);
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        border: 0;
        height: 3rem;

        img {
            height: 100%;
            width: 100%;
        }
    }

    button.chatbot:not(.btn-outline-secondary):disabled {
        opacity: 0.8;
    }
}

::ng-deep {
    .bpWebchat {
        max-height: 560px !important;
        height: min(560px, 100% - 120px) !important;
        border-radius: 16px !important;
    }

    .bpFab {
        height: 3.6rem !important;
        width: 3.6rem !important;
    }
}


@media (max-width: 1400px) {
    .container {
        align-items: center !important;
    }

    .links-container {
        justify-content: center !important;
    }
}

@media (min-width: 1400px) {
    .container {
        position: absolute;
        left: 0;
        right: 0;
        transform: translateY(-35%);
        z-index: 99;
        max-width: 1500px;
    }
}

@media only screen and (max-width: 1400px) {
    .container {
        position: absolute;
        left: 0;
        right: 0;
        transform: translateY(-50%);
        z-index: 99;
    }

    .main-container {
        .links-container {
            .circle {
                height: 110px;
                width: 110px;

                img {
                    height: 40px;
                    width: 40px;
                }

            }

            .desc {
                font-size: var(--snjy-font-size-0-9);
            }
        }
    }
}

@media only screen and (max-width: 1200px) {
    .main-container {
        height: auto;

        .dashboard-video-sec {
            height: calc(100vh - 50vh);
        }

        .container {
            top: auto;
            position: relative;
            transform: none;
            z-index: 99;
            margin: -150px auto 0 auto;

            .links-container {
                a.d-flex {
                    margin: 0 0 25px 0;

                    .circle {
                        height: 170px;
                        width: 170px;

                        img {
                            height: 60px;
                            width: 60px;
                        }
                    }

                    .desc {
                        font-size: var(--snjy-font-size-0-9);
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1024px) {
    .main-container {
        height: auto;

        .dashboard-video-sec {
            height: 400px;
        }
    }
}

@media only screen and (max-width: 991px) {
    .main-container {
        height: auto;

        .container {
            .d-company-name {
                text-align: center;
            }

            .links-container {
                a.d-flex {
                    .circle {
                        height: 150px;
                        width: 150px;

                        img {
                            height: 60px;
                            width: 60px;
                        }
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 791px) {
    .main-container {
        height: auto;

        .dashboard-video-sec {
            height: 350px;

            video {
                margin: auto;
                left: auto;
                right: 0;
                top: 0;
                bottom: 0;
                width: 800px;
                height: 600px;
            }
        }

        .container {
            margin: -110px auto 0 auto;

            .d-company-name {
                margin: 0 auto 60px auto;

                .welcome-msg {
                    font-size: 3rem;
                }

                .company-name {
                    font-size: 2rem;
                }
            }

            .links-container {
                a.d-flex {
                    .circle {
                        height: 180px;
                        width: 180px;

                        img {
                            height: 60px;
                            width: 60px;
                        }
                    }

                    .desc {
                        font-size: var(--snjy-font-size-0-9);
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 576px) {
    .main-container {
        height: auto;

        .container {
            .d-company-name {
                margin: 0 auto 100px auto;

                .welcome-msg {
                    font-size: 2.5rem;
                }

                .company-name {
                    font-size: 1.5rem;
                }
            }

            .links-container {
                a.d-flex {
                    .circle {
                        height: 135px;
                        width: 135px;

                        img {
                            height: 40px;
                            width: 40px;
                        }
                    }

                    .desc {
                        font-size: var(--snjy-font-size-0-9);
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 412px) {
    .main-container {
        height: auto;

        .container {
            .d-company-name {
                margin: 0 auto 80px auto;

                .welcome-msg {
                    font-size: 2.2rem;
                }

                .company-name {
                    font-size: 1.2rem;
                }
            }

            .links-container {
                a.d-flex {
                    .circle {
                        height: 100px;
                        width: 100px;

                        img {
                            height: 35px;
                            width: 35px;
                        }
                    }

                    .desc {
                        font-size: var(--snjy-font-size-0-8);
                    }
                }
            }
        }
    }
}