:host {
    padding: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
}

h4 {
    font-family: var(--snjy-font-family-secondary);
    font-weight: var(--snjy-font-weight-bold);
    font-size: var(--snjy-font-size-2);
    color: var(--snjy-color-secondary);
}

label {
    font-family: var(--snjy-font-family-ternary);
    font-weight: var(--snjy-font-weight-semi-bold);
    font-size: var(--snjy-font-size-0-9);
    color: var(--snjy-color-secondary);
}

form {
    min-width: 450px;
}

input {
    font-family: var(--snjy-font-family-ternary);
    font-weight: var(--snjy-font-weight-normal);
    font-size: var(--snjy-font-size-1);
    color: var(--snjy-font-color-seconday);
    background: var(--snjy-color-main-background-ternary);
    border: 1px solid var(--snjy-border-color);
    padding: 0.5rem 1rem;
    outline: none;

    &:hover {
        border: 1px solid var(--snjy-border-color);
        outline: none;
        box-shadow: none;
    }
}

.hint {
    font-family: var(--snjy-font-family-ternary);
    font-weight: var(--snjy-font-weight-normal);
    font-size: var(--snjy-font-size-0-875);
    color: var(--snjy-color-dark-secondary);
}

button {
    width: 48%;
    border-radius: 60px;
    font-family: var(--snjy-font-family-secondary);
    font-weight: var(--snjy-font-weight-bold);
    font-size: var(--snjy-font-size-1-25);
    padding: 1rem 2rem;
    background-color: var(--snjy-color-secondary);
    color: var(--snjy-color-main-background);
}

.btn-light {
    background-color: var(--snjy-color-primary);
    color: var(--snjy-color-secondary);
}

@media (max-width: 500px) {
    form {
        min-width: auto;
    }

    button {
        padding: .5rem 1rem;
        font-size: var(--snjy-font-size-0-875);
    }
}