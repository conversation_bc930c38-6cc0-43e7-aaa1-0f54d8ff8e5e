<div class="p-4">
    <h3 class="mb-2">Save Cart</h3>
    <p class="note mb-3">Your cart will be moved to Saved Cart list.</p>
    <form [formGroup]="form">
        <div class="form-group mb-3">
            <label class="form-label">Name</label>
            <input class="form-control" type="text" formControlName="name" />
            <span class="text-error" *ngIf="f.name?.touched && f.name.hasError('required')">
                {{ 'validation.required' | translate }}
            </span>
        </div>
        <div class="form-group mb-3">
            <label class="form-label">Description</label>
            <textarea class="form-control" rows="3" formControlName="description"></textarea>
            <span class="text-error" *ngIf="f.description?.touched && f.description.hasError('required')">
                {{ 'validation.required' | translate }}
            </span>
        </div>
    </form>
    <div>
        <button type="button" class="btn  mb-2 d-block w-100" [disabled]="saving || !form.valid" (click)="onSubmit()">{{
            saving ? 'Saving' : 'Save' }}</button>
        <button type="button" class="btn btn-light d-block w-100" (click)="activeModal.dismiss()">Cancel</button>
    </div>
</div>