import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/authentication/auth.service';
import { FormGroup, Validators, FormBuilder, AbstractControl } from '@angular/forms';
import { TicketsService } from '../tickets.service';
import { Location } from '@angular/common';
import { Observable, OperatorFunction, catchError, debounceTime, distinctUntilChanged, map, of, startWith, switchMap, tap } from 'rxjs';
import { OrderHistoryService } from '../../order-history/order-history.service';
import moment from 'moment';
import { AppToastService } from 'src/app/shared/services/toast.service';

@Component({
  selector: 'app-ticket',
  templateUrl: './ticket.component.html',
  styleUrls: ['./ticket.component.scss']
})
export class TicketComponent implements OnInit {
  sellerDetails: any = {};
  loading = false;
  searchFailed = false;
  saving = false;
  submitted = false;
  statuses = '';
  attachments: any = [];
  filteredOptions: Observable<any>;
  allowedFileTypes = '.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.ppt,.pptx,.pps,.ppsx,.odt,.xls,.xlsx,.mp3,.mp4,.m4a,.ogg,.wav,.m4v,.mov,.wmv,.avi,.mpg,.ogv,.3gp,.3g3,.zip';
  allowedFileSize = 2 * 1024 * 1024;
  form: FormGroup = this.formBuilder.group(
    {
      Subject: ['', Validators.required],
      Description: ['', Validators.required],
      Category: ['Complaint', Validators.required],
      SalesOrder: ['']
    }
  );
  @ViewChild("fileInput") fileInput: any;

  constructor(
    private location: Location,
    private router: Router,
    private formBuilder: FormBuilder,
    private _snackBar: AppToastService,
    private service: TicketsService,
    public authService: AuthService,
    private orderService: OrderHistoryService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function
    };
  }

  ngOnInit(): void {
    this.getAllStatus();
  }

  getAllStatus() {
    return this.orderService.getAllStatuses().subscribe({
      next: (value) => {
        this.statuses = value.data.map((val: any) => val.code).join(';');
        // this.filteredOptions = this.f['SalesOrder'].valueChanges
        //   .pipe(
        //     startWith(''),
        //     debounceTime(400),
        //     distinctUntilChanged(),
        //     switchMap(val => {
        //       return this.filter(val || '')
        //     })
        //   );
      },
      error: () => {
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      }
    })
  }

  search: OperatorFunction<string, readonly string[]> = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => (this.loading = true)),
      switchMap((term) =>
        this.filter(term),
      ),
      tap(() => (this.loading = false)),
    );

  filter(val: string): Observable<any[]> {
    this.loading = true;
    return this.orderService.getAll({
      SD_DOC: val,
      PURCHASE_ORDER: '',
      SOLDTO: this.sellerDetails.customer_id,
      VKORG: this.sellerDetails.sales_organization,
      DOC_TYPE: 'OR;ZOR',
      COUNT: 10,
      DOC_STATUS: this.statuses,
      DOCUMENT_DATE: '',
      DOCUMENT_DATE_TO: this.formatSearchDate(new Date())
    }).pipe(
      tap(() => {
        this.loading = false;
      }),
      map(res => res.resultData.map((result: any) => result.SD_DOC)),
      catchError(() => {
        this.searchFailed = true;
        return of([]);
      }),
    );
  }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  formatSearchDate(date: Date) {
    if (!date) return '';
    return moment(date).format('YYYY-MM-DD');
  }

  onFileSelected(event: any) {
    this.attachments = [];
    if (event.target.files?.length > 3) {
      this._snackBar.open('Please select upto 3 files.', { type: 'Warning' });
      return;
    }
    const files: File[] = event.target.files;
    for (let i = 0; i < files.length; i++) {
      const file: File = files[i];
      if (file.size > this.allowedFileSize) {
        this._snackBar.open(`Maximum file size limit exceeded for ${file.name}. (2 MB)`, { type: 'Warning' });
        return;
      }
      this.setBase64(file);
    }
  }

  removeFile(event: any, index: number) {
    event && event.stopPropagation();
    this.attachments.splice(index, 1);
    this.fileInput.nativeElement.value = '';
  }

  setBase64(file: File) {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (reader.result) {
        this.attachments.push({
          Content: reader.result.toString().replace('data:', '').replace(/^.+,/, ''),
          Name: file.name
        });
      }
    };
    reader.onerror = (error) => {
      this._snackBar.open(`Error while reading your file ${file.name}, Please select again.`, { type: 'Error' });
      console.log('Error: ', error);
    };
  }

  onSubmit(): void {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    const value = this.form.value;
    let body = {
      ...value,
      CustomerId: this.sellerDetails.bp_customer_number,
      SalesOrg: this.sellerDetails.sales_organization,
      CustomerName: this.sellerDetails.name,
      TicketType: 'SRRQ'
    };
    if (this.attachments && this.attachments.length) {
      body.Attachments = this.attachments;
    }
    this.service.create(body).subscribe({
      complete: () => {
        this.onReset();
        this.saving = false;
        this.goToListing();
        this._snackBar.open('Thank you for reaching out to our customer support team. Your message has been shared with our Customer Service Agents, who will assist you shortly.');
      },
      error: (err) => {
        this.saving = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      },
    })
  }

  onReset(): void {
    this.submitted = false;
    this.form.reset();
  }

  goBack(): void {
    this.location.back();
  }

  goToListing(): void {
    this.router.navigate(['store', 'tickets']);
  }

}