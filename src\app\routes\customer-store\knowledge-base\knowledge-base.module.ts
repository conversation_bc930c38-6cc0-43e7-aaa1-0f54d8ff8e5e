import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { KnowledgeBaseComponent } from "./knowledge-base.component";
import { SharedModule } from "src/app/shared/shared.module";

@NgModule({
  declarations: [KnowledgeBaseComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: KnowledgeBaseComponent }]),
  ],
})
export class KnowledgeBaseModule {}
