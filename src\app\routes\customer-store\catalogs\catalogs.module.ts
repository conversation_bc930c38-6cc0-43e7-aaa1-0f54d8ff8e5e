import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { CatalogsComponent } from "./catalogs.component";
import { SharedModule } from "src/app/shared/shared.module";
import { FacetComponent } from "./facet/facet.component";

@NgModule({
  declarations: [CatalogsComponent, FacetComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: CatalogsComponent }]),
  ],
})
export class CatalogsModule {}
