<div class="mt-2">
  <div class="d-flex justify-content-between">
    <span (click)="toogleDetails()" class="expand-title d-flex align-items-center">
      <i class="material-icons-outlined">{{cardOpen ? 'expand_more': 'chevron_right'}}</i>&nbsp;{{
      'form.details' | translate }}
    </span>
    <div class="d-flex gap-1">
      <button class="btn btn-primary" (click)="submitForm()"
        *ngIf="cardOpen && !currentForm.disabled && aciveTab != 'Similar Products' && aciveTab != 'Classification' ">
        {{ "form.action.submit" | translate }}
      </button>
      <button class="btn btn-primary" (click)="refresh()" [disabled]="refreshing">
        <span class="material-icons-outlined">
          refresh
        </span>
      </button>
    </div>
  </div>
  <div *ngIf="cardOpen">
    <asar-tabs (activeTab)="setActiveform($event)">
      <asar-tab [tabTitle]="'General'">
        <form [formGroup]="GeneralForm">
          <div class="row">
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.code" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_id" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.name" | translate }}</label>
              <input class="form-control" type="text" formControlName="name" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.status" | translate }}</label>
              <select formControlName="status" class="form-select">
                <option value="ACTIVE">ACTIVE</option>
                <option value="INACTIVE">INACTIVE</option>
              </select>
            </div>
            <div class="col-md-12 form-group">
              <label>{{ "backoffice.product.summary" | translate }}</label>
              <angular-editor formControlName="product_summary" [config]="editorConfig"></angular-editor>
            </div>
            <div class="col-md-12 form-group mt-2">
              <label>{{ "backoffice.product.specification" | translate }}</label>
              <angular-editor formControlName="specification" [config]="editorConfig"></angular-editor>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 pb-4 pt-4 form-group">
              <h5>{{ "backoffice.product.description" | translate }}</h5>
            </div>
          </div>
          <div class="row">
            <div class="col-md-4 form-group" *ngFor="let desc of description$ | async;let i = index;">
              <label>{{ desc.language }}</label>
              <input class="form-control" type="text" [value]="desc.description" disabled />
            </div>
          </div>
        </form>
      </asar-tab>
      <asar-tab [tabTitle]="'Backend'">
        <form [formGroup]="BackendForm">
          <div class="row">
            <div class="col-md-12 form-group">
              <label>{{ "backoffice.product.code" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_id" />
            </div>
            <div class="col-md-12 form-group">
              <label>{{ "backoffice.product.description" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.stock" | translate }}</label>
              <input class="form-control" type="text" formControlName="stock" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.unit_measure" | translate }}</label>
              <input class="form-control" type="text" formControlName="unit_measure" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.salesOrgId" | translate }}</label>
              <input class="form-control" type="text" formControlName="sales_org_id" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.salesOrgDesc" | translate }}</label>
              <input class="form-control" type="text" formControlName="sales_org_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.distriChannelId" | translate }}</label>
              <input class="form-control" type="text" formControlName="distri_channel_id" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.distriChannelDesc" | translate }}</label>
              <input class="form-control" type="text" formControlName="distri_channel_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.divisionID" | translate }}</label>
              <input class="form-control" type="text" formControlName="division_id" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.divisionDescription" | translate }}</label>
              <input class="form-control" type="text" formControlName="division_desc" />
            </div>
            <!---->
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.type" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_type" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.typeDescription" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_type_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.oldID" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_old_id" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.oldIDDescription" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_old_id_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.group" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_group" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.groupDescription" | translate }}</label>
              <input class="form-control" type="text" formControlName="product_group_desc" />
            </div>

            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.baseUnit" | translate }}</label>
              <input class="form-control" type="text" formControlName="base_unit" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.baseUnitDescription" | translate }}</label>
              <input class="form-control" type="text" formControlName="base_unit_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.baseISOUnit" | translate }}</label>
              <input class="form-control" type="text" formControlName="base_iso_unit" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{
                "backoffice.product.baseISOUnitDescription" | translate
                }}</label>
              <input class="form-control" type="text" formControlName="base_iso_unit_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{
                "backoffice.product.itemCategoryGroup" | translate
                }}</label>
              <input class="form-control" type="text" formControlName="item_category_group" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{
                "backoffice.product.itemCategoryGroupDescription" | translate
                }}</label>
              <input class="form-control" type="text" formControlName="item_category_group_desc" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.isDeleted" | translate }}</label>
              <input class="form-control" type="text" formControlName="is_deleted" />
            </div>
          </div>
        </form>
      </asar-tab>
      <asar-tab tabTitle="Pricing">
        <form [formGroup]="priceForm">
          <div class="row">
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.currency_iso" | translate }}</label>
              <input class="form-control" type="text" formControlName="currency_iso" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{
                "backoffice.product.formatted_value" | translate
                }}</label>
              <input class="form-control" type="text" formControlName="formatted_value" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.max_quantity" | translate }}</label>
              <input class="form-control" type="text" formControlName="max_quantity" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.min_quantity" | translate }}</label>
              <input class="form-control" type="text" formControlName="min_quantity" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.price_type" | translate }}</label>
              <input class="form-control" type="text" formControlName="price_type" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.value" | translate }}</label>
              <input class="form-control" type="text" formControlName="value" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.max_price" | translate }}</label>
              <input class="form-control" type="text" formControlName="max_price" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ "backoffice.product.min_price" | translate }}</label>
              <input class="form-control" type="text" formControlName="min_price" />
            </div>
          </div>
        </form>
      </asar-tab>
      <asar-tab tabTitle="Category">
        <form [formGroup]="categoryForm">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>
                  {{ "backoffice.product.category_code" | translate }}
                </label>
                <select class="form-control" formControlName="product_categories_ids">
                  <option *ngFor="let c of categories" [ngValue]="c.id">{{ c.name }}</option>
                </select>
                <span class="text-error" *ngIf="
                    f.product_categories_ids?.touched &&
                    f.product_categories_ids.hasError('required')
                  ">
                  {{ "validation.required" | translate }}
                </span>
              </div>
              <div class="row" *ngIf="categoryForm.controls?.categoryHierarchy?.value?.length">
                <div class="col">
                  <strong>Category hierarchy:</strong>
                  <nav aria-label="breadcrumb mt-2">
                    <ol class="breadcrumb">
                      <li class="breadcrumb-item" *ngFor="let pch of categoryForm.controls?.categoryHierarchy?.value">
                        <span>{{pch}}</span>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <label>
                {{ "backoffice.product.catalog_code" | translate }}
              </label>
              <select class="form-control" formControlName="product_catalogs_ids">
                <option value=""></option>
                <option *ngFor="let c of catalogs" [ngValue]="c.id">{{ c.name }}</option>
              </select>
            </div>
          </div>
        </form>
      </asar-tab>
      <asar-tab tabTitle="Media">
        <form [formGroup]="mediaForm">
          <div class="row">
            <div class="col form-group">
              <label class="d-block">{{ "backoffice.product.docType" | translate }}</label>
              <select formControlName="doc_type" class="form-select" (change)="changeDocType($event)">
                <option value="IMAGE">Image</option>
                <option value="PDF">PDF</option>
                <option value="VIDEO">Video</option>
                <option value="SPECIFICATION">Specification</option>
              </select>
            </div>
            <div class="col form-group"
              *ngIf="currentForm.get('doc_type')?.value !== 'IMAGE' && currentForm.get('doc_type')?.value !== 'SPECIFICATION'">
              <label>{{ "backoffice.product.documentName" | translate }}</label>
              <input class="form-control" type="text" formControlName="media_name" />
              <span class="text-error" *ngIf="f.media_name?.touched && f.media_name.hasError('required')">
                {{ "validation.required" | translate }}
              </span>
            </div>
            <div class="col form-group"
              *ngIf="currentForm.get('doc_type')?.value === 'IMAGE' || currentForm.get('doc_type')?.value === 'SPECIFICATION'">
              <label class="d-block">{{ "backoffice.product.selectDimenssion" | translate }}</label>
              <select formControlName="media_type" class="form-select">
                <option value="">Select Dimenssion</option>
                <option value="96X96" *ngIf="currentForm.get('doc_type')?.value === 'IMAGE'">96X96</option>
                <option value="300X300" *ngIf="currentForm.get('doc_type')?.value === 'IMAGE'">300X300</option>
                <option value="1200X1200">1200X1200</option>
              </select>
              <span class="text-error" *ngIf="
                  f.media_type?.touched && f.media_type.hasError('required')
                ">
                {{ "validation.required" | translate }}
              </span>
            </div>
            <div class="col form-group">
              <label>{{ "backoffice.product.url" | translate }}</label>
              <input class="form-control" type="text" formControlName="image" />
              <span class="text-error" *ngIf="f.image?.touched && f.image.hasError('required')">
                {{ "validation.required" | translate }}
              </span>
            </div>
            <div class="col form-group" *ngIf="currentForm.get('doc_type')?.value === 'IMAGE'">
              <label>Cover Image</label>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked" checked
                  formControlName="is_cover_image">
                <label class="form-check-label" for="flexCheckChecked">
                  Mark as cover image
                </label>
              </div>
              <!-- <input class="form-control" type="text" formControlName="image" /> -->
            </div>
            <div class="col-md-12 form-group" *ngIf="(images$ | async )?.length">
              <div *ngFor="let img of images$ | async | groupBy : 'media_type'" class="d-flex gap-3 my-3">
                <ng-container *ngIf="img.key === 'IMAGE'">
                  <div class="image-dimenssion d-flex align-items-center fw-bold">{{ img.key }}</div>
                  <div *ngFor="let imgv of img.value">
                    <div
                      class="thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border">
                      <img [src]="trustUrl(imgv.url)" width="100">
                      <a class="remove-image d-none position-absolute" style="display: inline !important"
                        (click)="removeImg(imgv.id)">&#215;</a>
                    </div>
                    <p *ngIf="imgv.is_cover_image" class="text-center">Cover Image</p>
                  </div>
                </ng-container>
                <ng-container *ngIf="img.key === 'SPECIFICATION'">
                  <div class="image-dimenssion d-flex align-items-center fw-bold">{{ img.key }}</div>
                  <div *ngFor="let imgv of img.value">
                    <div
                      class="thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border">
                      <img [src]="trustUrl(imgv.url)" width="100">
                      <a class="remove-image d-none position-absolute" style="display: inline !important"
                        (click)="removeImg(imgv.id)">&#215;</a>
                    </div>
                  </div>
                </ng-container>
                <ng-container *ngIf="img.key === 'VIDEO'">
                  <div class="image-dimenssion d-flex align-items-center fw-bold">{{ img.key }}</div>
                  <div *ngFor="let imgv of img.value">
                    <div
                      class="thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border">
                      <i class="material-icons-outlined">play_circle</i>
                      <a class="remove-image d-none position-absolute" style="display: inline !important"
                        (click)="removeImg(imgv.id)">&#215;</a>
                    </div>
                    <p class="text-center">{{imgv.media_name}}</p>
                  </div>
                </ng-container>
                <ng-container *ngIf="img.key === 'PDF'">
                  <div class="image-dimenssion d-flex align-items-center fw-bold">{{ img.key }}</div>
                  <div *ngFor="let imgv of img.value">
                    <div
                      class="thumbnail d-flex align-items-center justify-content-center p-2 flex-column position-relative border">
                      <i class="material-icons-outlined">
                        picture_as_pdf
                      </i>
                      <a class="remove-image d-none position-absolute" style="display: inline !important "
                        (click)="removeImg(imgv.id)">&#215;</a>
                    </div>
                    <p class="text-center">{{imgv.media_name}}</p>
                  </div>
                </ng-container>
              </div>
            </div>
          </div>
          <!--<div class="row"> 
            <div class="col-md-6 form-group">
              <label class="d-block">{{ "backoffice.product.photo_sm" | translate }}</label>
              <input formControlName="photo_sm" type="file"  (change)="handleFileInput($event, 'photo_sm')"/>
              <span
              class="text-error d-block"
              *ngIf="
                f.photo_sm?.touched &&
                f.photo_sm.hasError('dimenssionMismatched')
              "
            >
              {{ "validation.size_96" | translate }}
            </span>
            </div>
            <div class="col-md-6 form-group">
              <label>{{ "backoffice.product.photo_sm_url" | translate }}</label>
              <input class="form-control" type="text" formControlName="photo_sm_url" />
            </div>
            <div class="col-md-6 form-group">
              <label class="d-block">{{ "backoffice.product.photo_md" | translate }}</label>
              <input formControlName="photo_md" type="file" (change)="handleFileInput($event, 'photo_md')"/>
              <span
              class="text-error d-block"
              *ngIf="
                f.photo_md?.touched &&
                f.photo_md.hasError('dimenssionMismatched')
              "
            >
              {{ "validation.size_300" | translate }}
            </span>
            </div>
            <div class="col-md-6 form-group">
              <label>{{ "backoffice.product.photo_md_url" | translate }}</label>
              <input class="form-control" type="text" formControlName="photo_md_url" />
            </div>
            <div class="col-md-6 form-group">
              <label class="d-block">{{ "backoffice.product.photo_lg" | translate }}</label>
              <input formControlName="photo_lg" type="file" (change)="handleFileInput($event, 'photo_lg')"/>
              <span
              class="text-error d-block"
              *ngIf="
                f.photo_lg?.touched &&
                f.photo_lg.hasError('dimenssionMismatched')
              "
            >
              {{ "validation.size_1200" | translate }}
            </span>
            </div>
            <div class="col-md-6 form-group">
              <label>{{ "backoffice.product.photo_lg_url" | translate }}</label>
              <input class="form-control" type="text" formControlName="photo_lg_url" />
            </div>
          </div>-->
        </form>
      </asar-tab>
      <asar-tab tabTitle="Classification">
        <ng-container &ngIf="!loadingProductClassifications">
          <ng-container *ngFor="let item of classification | keyvalue">
            <h6 class="mb-3"><b>Class: </b> {{item.key}}</h6>
            <div class="row">
              <ng-container *ngFor="let val of $any(item).value; let j = index">
                <div class="col-6 form-group">
                  <label class="text-capitalize">{{ val?.class_charc_type_descr }}</label>
                  <input class="form-control" type="text" [(ngModel)]="val.formatted_charc_value" disabled />
                </div>
              </ng-container>
            </div>
          </ng-container>
        </ng-container>
        <div *ngIf="loadingProductClassifications">Loading...</div>
      </asar-tab>
      <asar-tab tabTitle="Plant">
        <div class="accordion accordion-flush" *ngIf="plants$ | async as plants">
          <div class="accordion-item" *ngFor="let plant of plants;let i = index;">
            <h2 class="accordion-header">
              <button class="accordion-button collapsed" (click)="toggleAccordian(plants,$event, i)" type="button">
                {{ 'backoffice.product.plant' | translate }} #{{i+1}}
              </button>
            </h2>
            <div id="flush-collapseOne" class="panel">
              <div class="accordion-body">
                <div class="row">
                  <div class="col-md-4 form-group">
                    <label> {{ 'backoffice.product.plant' | translate }}</label>
                    <input class="form-control" type="text" [value]="plant.plant" disabled />
                  </div>
                  <div class="col-md-4 form-group">
                    <label> {{ 'backoffice.product.baseUnit' | translate }}</label>
                    <input class="form-control" type="text" [value]="plant.base_unit" disabled />
                  </div>
                  <div class="col-md-4 form-group">
                    <label> {{ 'backoffice.product.baseISOUnit' | translate }}</label>
                    <input class="form-control" type="text" [value]="plant.base_iso_unit" disabled />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </asar-tab>
      <asar-tab tabTitle="Similar Products">
        <p class="p-2  fw-bold">Product: {{model.product_id}}</p>
        <ng-container &ngIf="!loadingSimilarProducts">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th>Similar Product Id</th>
                  <th>Relationship</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <input type="text" [(ngModel)]="addSimilarProductDetails.similar_product_id"
                      placeholder="Similar Product Id" class="form-control" />
                  </td>
                  <td>
                    <select class="form-select" [(ngModel)]="addSimilarProductDetails.relationship_type_id">
                      <option *ngFor="let option of relationshipTypes.data" [value]="option.code">
                        {{option.description}}
                      </option>
                    </select>
                  </td>
                  <td>
                    <button class="btn btn-primary ms-2" (click)="addSimilarProduct()"
                      [disabled]="!addSimilarProductDetails.similar_product_id || !addSimilarProductDetails.relationship_type_id || savingSimilarProducts">
                      <i class="material-icons-outlined">add</i>
                    </button>
                  </td>
                </tr>
                <ng-container *ngIf="!similarProducts.length">
                  <tr>
                    <td colspan="3">No records found.</td>
                  </tr>
                </ng-container>
                <ng-container *ngIf="similarProducts.length">
                  <tr *ngFor="let item of similarProducts;let i = index;">
                    <ng-container *ngIf="item.editing">
                      <td>
                        <input type="text" [(ngModel)]="editSimilarProductDetails.similar_product_id"
                          class="form-control" />
                      </td>
                      <td>
                        <select class="form-select" [(ngModel)]="editSimilarProductDetails.relationship_type_id">
                          <option *ngFor="let option of relationshipTypes.data" [value]="option.code">
                            {{option.description}}
                          </option>
                        </select>
                      </td>
                    </ng-container>
                    <ng-container *ngIf="!item.editing">
                      <td>
                        <span>{{item.similar_product_id}}</span>
                      </td>
                      <td>
                        <select class="form-select" [(ngModel)]="item.relationship_type_id" disabled>
                          <option *ngFor="let option of relationshipTypes.data" [value]="option.code">
                            {{option.description}}
                          </option>
                        </select>
                      </td>
                    </ng-container>
                    <td>
                      <button class="btn btn-primary ms-2" (click)="editSimilarProduct(item)" *ngIf="!item.editing">
                        <i class="material-icons-outlined">edit</i>
                      </button>
                      <button class="btn btn-primary ms-2" (click)="updateSimilarProduct(item)" *ngIf="item.editing">
                        <i class="material-icons-outlined">done</i>
                      </button>
                      <button class="btn btn-primary ms-2" (click)="item.editing = false;" *ngIf="item.editing">
                        <i class="material-icons-outlined">close</i>
                      </button>
                      <button class="btn btn-primary ms-2" (click)="$event.stopPropagation();removeSimilarProduct(item)"
                        *ngIf="!item.editing">
                        <i class="material-icons-outlined">delete</i>
                      </button>
                    </td>
                  </tr>
                </ng-container>
              </tbody>
            </table>
          </div>
        </ng-container>
        <div *ngIf="loadingSimilarProducts">Loading...</div>
      </asar-tab>
    </asar-tabs>
  </div>
</div>