import { Component, OnInit } from "@angular/core";
import { ConfigService } from "./config.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-config",
  templateUrl: "./config.component.html",
  styleUrls: ["./config.component.scss"],
})
export class ConfigComponent implements OnInit {
  tabs: Array<any> = this.service.getTabsConfig();
  loading = false;
  model: any;
  tab: any;
  categories = [];
  catalogs = [];
  flatternTabs: Array<any> = [];

  constructor(
    private service: ConfigService,
    private _snackBar: AppToastService
  ) { }

  ngOnInit(): void {
    this.flatternTabs = this.getFlatternTabs();
    this.fetchData();
  }

  fetchData(): void {
    this.loading = true;
    const filterTabs = this.flatternTabs.filter((tab: any) => {
      return tab?.id === "catalogs" || tab?.id === "categories";
    });
    this.service.getAll(filterTabs).subscribe({
      next: (res: any) => {
        for (let i = 0; i < filterTabs.length; i++) {
          const element = filterTabs[i];
          if (res[element.id]?.data) {
            element.data = res[element.id].data;
            this.setOptions(element);
          } else {
            element.hasErrors = true;
          }
        }
        this.loading = false;
      },
      error: (err) => {
        this.loading = false;
        this._snackBar.open("Error while processing your request.");
      },
    });
  }

  getFlatternTabs() {
    let tab = [];
    for (let i = 0; i < this.tabs.length; i++) {
      const tabDetails = this.tabs[i];
      if (tabDetails.subTabs?.length) {
        for (let j = 0; j < tabDetails.subTabs.length; j++) {
          tab.push(tabDetails.subTabs[j]);
        }
      } else {
        tab.push(tabDetails);
      }
    }
    return tab;
  }

  fetchDataByTab(tabDetails: any): void {
    this.loading = true;
    this.service.get(tabDetails.url).subscribe({
      next: (res) => {
        this.loading = false;
        tabDetails.data = res.data;
        this.setOptions(tabDetails);
        this.setSettings(tabDetails);
      },
      error: (err) => {
        this.loading = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  onTabChange(event: any, tab?: any) {
    this.model = undefined;
    const tabs = tab?.subTabs || [];
    const subTab = tabs.find((o: any) => o.title === event);
    if (subTab && subTab?.id !== "catalogs" && subTab?.id !== "categories") {
      this.fetchDataByTab(subTab);
    }
  }

  isDisabled(tab: any) {
    for (let i = 0; i < tab.columns.length; i++) {
      const element = tab.columns[i];
      if (!element.itemValue && element.required) {
        return true;
      }
    }
    return tab.saving;
  }

  setSettings(tab?: any) {
    if (tab) {
      tab.columns = tab.columns.map((col: any) => {
        if (tab.id === "stock") {
          col.itemValue = tab?.data?.low_stock_qty || 100;
        }
        if (tab.id === "approval-amount") {
          col.itemValue = tab?.data?.min_quote_price || 10000;
        }
        if (tab.id === "quote-transaction-type") {
          if (col.value === "code") {
            col.itemValue = tab?.data?.sales_quote_type_code || "";
          } else if (col.value === "description") {
            col.itemValue = tab?.data?.sales_quote_type_descr || "";
          }
        }
        if (tab.id === "quote-description") {
          if (col.value === "code") {
            col.itemValue = tab?.data?.quote_text_code || "";
          } else if (col.value === "description") {
            col.itemValue = tab?.data?.quote_text_descr || "";
          }
        }
        if (tab.id === "special-instructions") {
          if (col.value === "code") {
            col.itemValue = tab?.data?.text_code || "";
          } else if (col.value === "description") {
            col.itemValue = tab?.data?.text_descr || "";
          }
        }
        if (tab.id === "return-transaction-type") {
          if (col.value === "code") {
            col.itemValue = tab?.data?.cust_return_type_code || "";
          } else if (col.value === "description") {
            col.itemValue = tab?.data?.cust_return_type_descr || "";
          }
        }
        if (tab.id === 'footer') {
          if (col.value === 'home') {
            col.itemValue = tab?.data?.home_url || ""
          } else if (col.value === 'terms_of_use') {
            col.itemValue = tab?.data?.terms_url || ""
          } else if (col.value === 'contact_us') {
            col.itemValue = tab?.data?.contact_url || ""
          } else if (col.value === 'privacy_policy') {
            col.itemValue = tab?.data?.privacy_policy_url || ""
          }
        }
        if (tab.id === 'conditions') {
          if (col.value === 'base_price_code') {
            col.itemValue = tab?.data?.base_price_code || ""
          } else if (col.value === 'tax_code') {
            col.itemValue = tab?.data?.tax_code || ""
          } else if (col.value === 'shipping_code') {
            col.itemValue = tab?.data?.shipping_code || ""
          } else if (col.value === 'discount_code') {
            col.itemValue = tab?.data?.discount_code || ""
          }
        }
        return col;
      });
    }
  }

  saveSettings(tab: any, col?: any) {
    const obj: any = {
      id: tab?.data?.id || null,
    };
    if (tab.id == "approval-amount") {
      obj.min_quote_price = tab?.columns.find(
        (col: any) => col.value === "min_quote_price"
      )?.itemValue || 1;
      if (obj.min_quote_price < 1) {
        this._snackBar.open(
          "You must enter a positive number. Please try again.", { type: 'Warning' }
        );
        return;
      }
    }
    if (tab.id == "stock") {
      obj.low_stock_qty = tab?.columns.find(
        (col: any) => col.value === "low_stock_qty"
      )?.itemValue || 1;
      if (obj.low_stock_qty < 1) {
        this._snackBar.open(
          "You must enter a positive number. Please try again.", { type: 'Warning' }
        );
        return;
      }
    }
    if (tab.id == "quote-transaction-type") {
      obj.sales_quote_type_code = tab?.columns.find(
        (col: any) => col.value === "code"
      )?.itemValue || "";
      obj.sales_quote_type_descr = tab?.columns.find(
        (col: any) => col.value === "description"
      )?.itemValue || "";
    }
    if (tab.id == "quote-description") {
      obj.quote_text_code = tab?.columns.find(
        (col: any) => col.value === "code"
      )?.itemValue || "";
      obj.quote_text_descr = tab?.columns.find(
        (col: any) => col.value === "description"
      )?.itemValue || "";
    }
    if (tab.id == "special-instructions") {
      obj.text_code = tab?.columns.find(
        (col: any) => col.value === "code"
      )?.itemValue || "";
      obj.text_descr = tab?.columns.find(
        (col: any) => col.value === "description"
      )?.itemValue || "";
    }
    if (tab.id == "return-transaction-type") {
      obj.cust_return_type_code = tab?.columns.find(
        (col: any) => col.value === "code"
      )?.itemValue || "";
      obj.cust_return_type_descr = tab?.columns.find(
        (col: any) => col.value === "description"
      )?.itemValue || "";
    }
    if (tab.id == 'footer') {
      if (col.value === 'home') {
        obj.home_url = col.itemValue || "";
      }
      else if (col.value === 'contact_us') {
        obj.contact_url = col.itemValue || "";
      }
      else if (col.value === 'privacy_policy') {
        obj.privacy_policy_url = col.itemValue || "";
      } else if (col.value === 'terms_of_use') {
        obj.terms_url = col.itemValue || "";
      }
    }

    if (tab.id == 'conditions') {
      if (col.value === 'base_price_code') {
        obj.base_price_code = col.itemValue || "";
      }
      else if (col.value === 'tax_code') {
        obj.tax_code = col.itemValue || "";
      }
      else if (col.value === 'shipping_code') {
        obj.shipping_code = col.itemValue || "";
      } else if (col.value === 'discount_code') {
        obj.discount_code = col.itemValue || "";
      }
    }

    tab.saving = true;
    this.service.save(tab.url, obj).subscribe({
      next: (res) => {
        tab.saving = false;
        this._snackBar.open("Record saved successfully.");
      },
      error: (err) => {
        tab.saving = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  addRow(tab: any) {
    tab.saving = true;
    const obj: any = {};
    for (let i = 0; i < tab.columns.length; i++) {
      const column = tab.columns[i];
      obj[column.value] = column.itemValue;
    }
    this.service.save(tab.url, obj).subscribe({
      next: (res) => {
        tab.saving = false;
        for (let i = 0; i < tab.columns.length; i++) {
          tab.columns[i].itemValue = "";
        }
        if (tab?.id === "order-types") {
          this.fetchDataByTab(tab);
        } else {
          if (res.data) {
            tab.data.push(res.data);
            setTimeout(() => {
              this.setOptions(tab);
            }, 1000);
          } else {
            this.fetchDataByTab(tab);
          }
        }
        this._snackBar.open("Record saved successfully.");
      },
      error: (res) => {
        tab.saving = false;
        if (res?.error?.message.includes("foreign key constraint violation")) {
          this._snackBar.open("Does not exist.", { type: 'Error' });
        } else if (res?.error?.message.includes("unique constraint violated")) {
          this._snackBar.open("Already exist in records", { type: 'Error' });
        } else {
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        }
      },
    });
  }

  update(item: any, tab: any) {
    const obj: any = {};
    for (let i = 0; i < tab.columns.length; i++) {
      const column = tab.columns[i];
      obj[column.value] = item[column.newValue];
    }
    this.service.update(tab.url, obj, item.id).subscribe({
      next: (res) => {
        item.editing = false;
        if (tab?.id === "order-types") {
          this.fetchDataByTab(tab);
        } else {
          for (let i = 0; i < tab.columns.length; i++) {
            const column = tab.columns[i];
            item[column.value] = item[column.newValue];
            delete item[column.newValue];
          }
        }
        this._snackBar.open("Record updated successfully.");
      },
      error: (err) => {
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  edit(item: any, tab: any) {
    item.editing = true;
    for (let i = 0; i < tab.columns.length; i++) {
      const column = tab.columns[i];
      item[column.newValue] = item[column.value];
    }
  }

  cancelEditing(item: any, tab: any) {
    item.editing = false;
    for (let i = 0; i < tab.columns.length; i++) {
      const column = tab.columns[i];
      delete item[column.newValue];
    }
  }

  remove(item: any, tab: any) {
    if (tab?.id === "order-types" && item.is_creation) {
      this._snackBar.open(`Cannot remove the order type ${item.code} as it is used for order creation.`, { type: 'Warning' });
      return;
    }
    this.service.delete(tab.url, item.id).subscribe({
      next: (res) => {
        this.model = undefined;
        this.fetchDataByTab(tab);
        this._snackBar.open("Record deleted successfully.");
      },
      error: (err) => {
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  setOptions(tab: any) {
    if (tab?.id === "categories") {
      const categories = tab?.data.map((val: any) => ({
        value: val.id,
        id: val.id,
        name: val.name,
        parent_category_id: val.parent_category_id,
      }));
      this.categories = this.addParentCategoryHierarchy(categories);
      const dd = tab?.columns.filter(
        (val: any) => val.value == "parent_category_id"
      );
      if (dd.length) {
        dd[0].options = [...this.categories];
      }
    }
    if (tab?.id === "catalogs") {
      this.catalogs = tab?.data.map((val: any) => ({
        value: val.id,
        id: val.id,
        name: val.name,
      }));
    }
  }

  addParentCategoryHierarchy(categories: any) {
    const categoryMap = new Map();
    const resultArray: any = [];

    // Step 1: Create a map of categories using their IDs as keys
    categories.forEach((category: any) => {
      categoryMap.set(category.id, { ...category, parent_category_hierarchy: [] });
    });

    // Step 2: Traverse the categories and add their parent hierarchy
    categories.forEach((category: any) => {
      let parentId = category.parent_category_id;
      const currentCategory = categoryMap.get(category.id);

      while (parentId !== null) {
        const parentCategory = categoryMap.get(parentId);
        if (parentCategory) {
          currentCategory.parent_category_hierarchy.unshift(parentCategory.name);
          parentId = parentCategory.parent_category_id;
        } else {
          break;
        }
      }

      if (currentCategory.parent_category_hierarchy.indexOf(currentCategory.name) === -1) {
        currentCategory.parent_category_hierarchy.push(currentCategory.name);
      }

      resultArray.push(currentCategory);
    });

    return resultArray;
  }

  selectRow(item: any, tab: any) {
    this.model = item;
    this.tab = tab;
  }

  dataUpdated(data: any) {
    this.service.get(`${data.url}/${data.id}`).subscribe({
      next: (res) => {
        for (let i = 0; i < this.tab.data.length; i++) {
          let element = this.tab.data[i];
          if (element.id == data.id) {
            Object.assign(this.tab.data[i], res.data);
            break;
          }
        }
      },
      error: (err) => {
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  trackByFn(index: number, data: any) {
    return data.id;
  }

  trackByFn1(index: number, data: any) {
    return data.value;
  }

}
