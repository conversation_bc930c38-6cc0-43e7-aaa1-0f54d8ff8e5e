<div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<ng-container *ngIf="!loading">
    <div class="all-main-title-sec thank-you-details-main-title">
        <h1>Thank you for your scheduled replenishment order!</h1>
        <div class="product-id">
            <span class="material-icons-outlined">confirmation_number</span>
            Confirmation Number is <span>{{ salesOrderScheduler?.id }}</span>
        </div>
        <div class="product-id flex-wrap">
            <span class="material-icons-outlined">email</span>
            A copy of your order details has been sent to <span>{{ salesOrderScheduler?.user?.email }}</span>
        </div>
    </div>

    <div class="order-id-sec">
        <div class="order-id-body flex-wrap">
            <div class="order-id-info">
                <div class="order-details">
                    <h3>Order Summary</h3>
                    <ul>
                        <li>
                            <span class="material-icons-outlined">pin</span>
                            Confirmation Number <span>{{ salesOrderScheduler?.id }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">pin</span>
                            Customer Number <span>{{ order?.SoldToParty }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">person</span>
                            Customer Name <span>{{ customer?.customer_name }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">confirmation_number</span>
                            P.O Number <span>{{ order?.PurchaseOrderByCustomer }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">event_note</span>
                            Schedule Type
                            <span>{{ salesOrderScheduler?.schedule_type }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">event_note</span>
                            Start Date
                            <span>{{ moment(salesOrderScheduler?.start_date).format("MM/DD/YYYY") }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">event_note</span>
                            End Date
                            <span>{{ moment(salesOrderScheduler?.end_date).format("MM/DD/YYYY") }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">event_note</span>
                            Next Order Date
                            <span>{{ salesOrderScheduler?.next_order_date }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">event_note</span>
                            Frequency
                            <span>{{ salesOrderScheduler?.frequency_format }}</span>
                        </li>
                        <li>
                            <span class="material-icons-outlined">payment</span>
                            Total <span>{{ order?.total || 0 | currency : order?.TransactionCurrency }}</span>
                        </li>
                    </ul>
                </div>

                <div class="order-details thank-shipping-m flex-wrap">
                    <div class="thank-shipping-m-box">
                        <h4>Shipping Details</h4>
                        <small>{{ salesOrderScheduler?.shippingAddress?.name }}</small>
                        <p>{{ salesOrderScheduler?.shippingAddress?.address }}</p>
                    </div>
                    <div class="thank-shipping-m-box">
                        <h4>Shipping Method</h4>
                        <p>Standard Shipment</p>
                    </div>
                </div>

                <div class="order-details">
                    <h3>Items to be shipped</h3>
                    <div class="item-box" *ngFor="let product of order?.to_Item?.A_SalesOrderItemSimulationType">
                        <div class="item-box-img"
                            [ngStyle]="{'background-image': 'url('+ (product.Material | getProductImage | async)+ ')'}">
                        </div>
                        <div class="item-box-content">
                            <h4>{{ product?.SalesOrderItemText }}</h4>
                            <small>{{ product?.Material }}</small>
                            <div class="item-box-bottom-content">
                                <div class="quantity">Quantity: <span>{{product.RequestedQuantity}}</span></div>
                                <div class="item-price">
                                    {{ product?.formatted_base_price }}
                                    <span>{{ product?.formatted_base_price_each }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="billing-info">
                <h3>Billing Information</h3>
                <small>Payment Details</small>
                <p class="terms">
                    Order placed by: <span>{{ salesOrderScheduler?.user?.display_name }}</span>
                </p>
                <p class="terms">
                    P.O No: <span>{{ order?.PurchaseOrderByCustomer }}</span>
                </p>
                <div class="billing-price">
                    <ul>
                        <li>
                            Subtotal <span>{{ order?.formatted_sub_total }}</span>
                        </li>
                        <li>
                            Shipping<span>{{ order?.formatted_shipping }}</span>
                        </li>
                        <li>
                            Tax <span>{{ order?.formatted_sales_tax }}</span>
                        </li>
                    </ul>
                    <ul>
                        <li class="total-price">
                            Total <span>{{ order?.formatted_total }}</span>
                        </li>
                    </ul>
                    <button type="button" class="btn" [routerLink]="['../catalogues']"><span
                            class="material-icons-outlined">local_mall</span> Continue Shopping </button>
                </div>
            </div>
        </div>
    </div>
</ng-container>