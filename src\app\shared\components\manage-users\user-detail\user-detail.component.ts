import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidatorFn,
  Validators,
} from "@angular/forms";
import { Subject, Observable, takeUntil, concat, of, map } from "rxjs";
import {
  catchError,
  distinctUntilChanged,
  switchMap,
  tap,
} from "rxjs/operators";

import { AppToastService } from "src/app/shared/services/toast.service";
import { ManageUserService } from "../manage-user-service/manage-user.service";
import { AuthService } from "src/app/core/authentication/auth.service";

@Component({
  selector: "app-user-detail",
  templateUrl: "./user-detail.component.html",
  styleUrls: ["./user-detail.component.scss"],
})
export class UserDetailComponent implements OnInit, OnChanges {
  private ngUnsubscribe = new Subject<void>();
  @Input() model: any = {};
  @Output() onUpdate = new EventEmitter<any>();
  @Input() userRoleType: any;

  public form = new FormGroup({});
  public cardOpen = true;
  public submitted = false;
  public aciveTab = "General";
  public currentForm: any;
  public refreshing = false;
  public userRoles: any[] = [];
  public roleHierarchy: any[] = [];

  private dateRangeValidator: ValidatorFn = () => {
    let invalid = false;
    const from = this.generalForm && this.generalForm.controls.valid_from.value;
    const to = this.generalForm && this.generalForm.controls.valid_to.value;
    if (from && to) {
      invalid = new Date(from).valueOf() > new Date(to).valueOf();
    }
    return invalid ? { invalidRange: { from, to } } : null;
  };

  // General form
  public generalForm = this.fb.group({
    id: [""],
    first_name: ["", Validators.required],
    last_name: ["", Validators.required],
    email: ["", [Validators.required, Validators.email]],
    contact_address: ["", Validators.required],
    valid_from: [""],
    valid_to: [""],
    status: ["", Validators.required],
    role: ["", Validators.required],
    customers: [[]],
  });

  // password form
  passwordForm = this.fb.group(
    {
      id: [""],
      password: ["", Validators.required],
      confirmPassword: ["", Validators.required],
    },
    {
      validators: this.password.bind(this),
    }
  );

  formMap: Record<string, any> = {
    General: this.generalForm,
    "Customer Details": this.generalForm,
    Password: this.passwordForm,
  };

  private defaultOptions: any = [];
  public customers$: Observable<any[]>;
  public customerLoading = false;
  public customerInput$ = new Subject<string>();

  constructor(
    public fb: FormBuilder,
    private manageUserService: ManageUserService,
    private _snackBar: AppToastService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.defaultOptions =
      this.userRoleType === "STOREFRONT" ? [] : [{ customer_id: "ALL" }];
    this.loadCustomers();
    this.getUserRoles();
    this.currentForm = this.generalForm;

    this.generalForm
      .get("valid_from")
      ?.valueChanges.pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(() => {
        const val = this.generalForm.getRawValue();
        if (!val.valid_from) {
          this.generalForm.controls.valid_to.clearValidators();
          this.generalForm.controls.valid_to.updateValueAndValidity();
        } else {
          this.generalForm.controls.valid_to.setValidators([
            Validators.required,
            this.dateRangeValidator,
          ]);
          this.generalForm.controls.valid_to.updateValueAndValidity();
        }
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    this.updateData();
    this.submitted = false;
  }

  updateData() {
    const data = {
      ...this.model,
      customer_id: this.model.customer.customer_id,
    };
    const role = data?.roles[0]?.id || "";
    this.generalForm.patchValue({ ...data, role });
    this.passwordForm.patchValue(data);
  }

  get f() {
    return this.currentForm.controls;
  }

  setActiveform(name: string) {
    this.aciveTab = name;
    this.generalForm.patchValue({ customers: <any>[] });
    this.currentForm = this.formMap[name];
  }

  trackByFn(item: any) {
    return item.customer_id;
  }

  changeUserRole() {
    setTimeout(() => {
      this.generalForm.patchValue({ role: <any>parseInt(this.f.role.value) });
    }, 0);
  }

  private loadCustomers() {
    this.customers$ = concat(
      of(this.defaultOptions), // default items
      this.customerInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.customerLoading = true)),
        switchMap((term: any) => {
          if (term && term.length < 2) {
            this.customerLoading = false;
            return of(this.defaultOptions);
          }
          return this.manageUserService.getCustomers({ search: term }).pipe(
            map((res: any) => {
              let data = res.data || [];
              if (this.defaultOptions[0]) {
                data.unshift(this.defaultOptions[0]);
              }
              return res.data;
            }),
            catchError(() => of(this.defaultOptions)), // empty list on error
            tap(() => (this.customerLoading = false))
          );
        })
      )
    );
  }

  onAddCustOption($event: any) {
    if ($event.customer_id === "ALL") {
      this.generalForm.patchValue({ customers: this.defaultOptions });
    } else {
      const selectedCust = this.f.customers.value;
      const index = selectedCust.findIndex((o: any) => o.customer_id === "ALL");
      if (index > -1) {
        selectedCust.splice(index, 1);
        this.generalForm.patchValue({ customers: selectedCust });
      }
    }
  }

  getUserRoles() {
    this.manageUserService
      .getUserRoles()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          const userRoles = res?.data || [];
          const loggedInUserRole = userRoles.find(
            (d: any) => d.id === this.authService?.userDetail?.user_role_id
          );
          this.roleHierarchy = loggedInUserRole?.roleHierarchy || [];
          this.userRoles = userRoles.filter((o: any) =>
            this.userRoleType.includes(o.role_type)
          );
        },
        error: (err) => {
          this._snackBar.open("Error while processing get user role request.", {
            type: "Error",
          });
        },
      });
  }

  update(data: any) {
    const payload = { ...data };
    delete payload.id;
    this.manageUserService
      .updateUser(data.id, payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (value: any) => {
          this.submitted = false;
          if (this.aciveTab === "Customer Details") {
            this.model = { ...{}, ...this.model };
            this.generalForm.patchValue({ customers: <any>[] });
          }
          this.onUpdate.emit(value.data);
          this._snackBar.open("Changes saved successfully!");
        },
        error: (err) => {
          this.submitted = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  password(formGroup: FormGroup) {
    const password = formGroup.get("password")?.value;
    const confirmPassword = formGroup.get("confirmPassword")?.value;
    return password === confirmPassword ? null : { passwordNotMatch: true };
  }

  updatePassword(data: any) {
    this.manageUserService
      .updateUserPassword(data.id, {
        password: this.authService.encryptString(data.password),
      })
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        complete: () => {
          this.submitted = false;
          this.currentForm.patchValue({
            password: "",
            confirmPassword: "",
          });
          this._markFormUntouched(this.currentForm);
          this._snackBar.open("Changes saved successfully!");
        },
        error: (err: any) => {
          this.submitted = false;
          this._markFormUntouched(this.currentForm);
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  submitForm() {
    this._markAsTouched(this.currentForm);
    if (this.currentForm.valid) {
      this.submitted = true;
      const reqPayload = { ...this.currentForm?.value };
      reqPayload.roles = [this.currentForm?.value?.role];
      reqPayload.address = this.currentForm?.value?.contact_address;
      delete reqPayload.role;
      delete reqPayload.contact_address;
      if (this.aciveTab === "General") {
        delete reqPayload.customers;
        this.update(reqPayload);
      } else if (this.aciveTab === "Customer Details") {
        const isArr = Array.isArray(reqPayload.customers);
        if (isArr && reqPayload.customers.length) {
          reqPayload.customers = this.currentForm?.value?.customers.map(
            (o: any) => o.customer_id
          );
          this.update(reqPayload);
        } else if (!isArr && reqPayload.customers) {
          this.manageUserService
            .deleteAllCustomersByUserId(reqPayload.id)
            .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe({
              next: () => {
                reqPayload.cust_is_selected = true;
                reqPayload.customers = [reqPayload.customers.customer_id];
                this.update(reqPayload);
              },
              error: (err: any) => {
                this._snackBar.open(
                  err.error?.message ||
                    "Error while processing remove user's all customer request.",
                  { type: "Error" }
                );
              },
            });
        }
      } else if (this.aciveTab === "Password") {
        this.updatePassword(this.currentForm.value);
      }
    }
  }

  removeUserAllCustomer() {
    if (this.aciveTab === "Customer Details") {
      const reqPayload = { ...this.currentForm?.value };
      this.manageUserService
        .deleteAllCustomersByUserId(reqPayload.id)
        .pipe(takeUntil(this.ngUnsubscribe))
        .subscribe({
          next: () => {
            this.model = { ...{}, ...this.model };
            this.generalForm.patchValue({ customers: <any>[] });
            if (reqPayload?.email === this.authService?.userDetail?.email) {
              const authData = { ...this.authService?.userDetail };
              delete authData.partner_function;
              this.authService.setAuth(authData);
            }
          },
          error: (err: any) => {
            this._snackBar.open(
              err.error?.message ||
                "Error while processing remove user's all customer request.",
              { type: "Error" }
            );
          },
        });
    }
  }

  private _markAsTouched(group: FormGroup | FormArray) {
    group.markAsTouched({ onlySelf: true });

    Object.keys(group.controls).map((field) => {
      const control = group.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this._markAsTouched(control);
      }
    });
  }

  private _markFormUntouched(form: FormGroup): void {
    Object.keys(form.controls).forEach((control) => {
      form.controls[control].markAsUntouched();
    });
  }

  refresh() {
    if (!this.model?.id) {
      return;
    }
    this.refreshing = true;
    this.manageUserService.getUserById(this.model.id).subscribe({
      next: (value) => {
        this.refreshing = false;
        this.model = value.data;
        this.onUpdate.emit(this.model);
        this.updateData();
      },
      error: () => {
        this.refreshing = false;
      },
    });
  }

  toogleDetails() {
    this.cardOpen = !this.cardOpen;
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
