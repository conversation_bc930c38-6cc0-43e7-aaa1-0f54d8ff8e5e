import { Injectable, NgZone } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { Router } from "@angular/router";
import { ApiConstant, AppConstant } from "src/app/constants/api.constants";
import * as crypto from "crypto-js";
import {
  BehaviorSubject,
  catchError,
  fromEvent,
  lastValueFrom,
  map,
} from "rxjs";

@Injectable({
  providedIn: "root",
})
export class AuthService {
  public userSubject: BehaviorSubject<any>;
  public permissions: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  private sessionChannel = new BroadcastChannel("session");
  private timer: any;
  private logoutTriggered = false;

  constructor(
    private http: HttpClient,
    public router: Router,
    private ngZone: NgZone
  ) {
    const user: any = this.getAuth();
    this.userSubject = new BehaviorSubject<any>(user || "");
    this.bindUserActivityEvents();
  }

  bindUserActivityEvents() {
    const events = ["click", "keydown"];
    for (let i = 0; i < events.length; i++) {
      const element = events[i];
      fromEvent(document, element).subscribe((data) => {
        this.setInavtivityTimer();
        this.sessionChannel.postMessage({
          type: "activityFound",
        });
      });
    }
    this.sessionChannel.onmessage = (event) => {
      if (event?.data?.type == "activityFound") {
        this.ngZone.run(() => {
          this.setInavtivityTimer();
        });
      }
      if (event?.data?.type == "logout") {
        this.logoutTriggered = true;
        this.doLogout();
      }
    };
    this.setInavtivityTimer();
    this.sessionChannel.postMessage({
      type: "activityFound",
    });
  }

  setInavtivityTimer() {
    clearTimeout(this.timer);
    if (!this.isLoggedIn) {
      return;
    }
    this.timer = setTimeout(() => {
      this.doLogout();
    }, AppConstant.SESSION_TIMEOUT);
  }

  login(username: string, password: string) {
    return this.http
      .post<any>(ApiConstant.SINGIN, {
        email: (username || "").toLowerCase(),
        password,
      })
      .pipe(
        map((res) => {
          if (res.status === "success") {
            const user = res.data;
            this.setAuth(user);
          }
          return res.status;
        })
      );
  }

  encryptString(str: string): string {
    return crypto.SHA256(str).toString();
  }

  getToken() {
    return this.userSubject.value?.token || null;
  }

  get userDetail() {
    return this.userSubject.value;
  }

  get isLoggedIn(): boolean {
    return !!this.userSubject.value;
  }

  get isCustomerSelected(): boolean {
    return !!this.userSubject?.value?.partner_function;
  }

  updateAuth(user: any) {
    const auth: any = this.getAuth();
    if (user?.cart) {
      auth.cart = user?.cart;
    }
    if (user?.partner_function) {
      auth.partner_function = user?.partner_function;
    }
    this.setAuth(auth);
  }

  get role(): string {
    return this.userSubject?.value?.role || "";
  }

  doLogout() {
    this.http.put<any>(ApiConstant.SINGOUT, {}).subscribe((res) => {
      this.resetAuth();
      window.location.href = "#/auth/login";
      window.location.reload();
    });
  }

  resetAuth() {
    this.removeAuthToken();
    !this.logoutTriggered &&
      this.sessionChannel.postMessage({
        type: "logout",
      });
    this.userSubject.next(null);
  }

  getAuth() {
    const authtoken: any = this.getAuthToken();
    if (authtoken) {
      const data: any = atob(authtoken);
      if (this.isJsonString(data)) {
        return JSON.parse(data);
      }
    }
    return null;
  }

  setAuth(auth: any) {
    localStorage.setItem("authtoken", btoa(JSON.stringify(auth)));
    this.userSubject.next(auth);
  }

  setAuthToken(authtoken: any) {
    localStorage.setItem("authtoken", authtoken);
  }

  getAuthToken() {
    return localStorage.getItem("authtoken");
  }

  removeAuthToken() {
    localStorage.removeItem("authtoken");
  }

  isJsonString(str: any) {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  }

  async getUserPermissions() {
    return await lastValueFrom(
      this.http
        .get<any>(`${ApiConstant.USER_PERMISSIONS}`)
        .pipe(
          map((res) => {
            if (res.status === "success") {
              const data = res?.data || [];
              this.permissions.next(data);
              return data;
            }
            return res;
          })
        )
        .pipe(
          catchError((error) => {
            this.permissions.next([]);
            return error;
          })
        )
    );
  }

  get getPermissions(): any[] {
    return this.permissions?.value || [];
  }
}
