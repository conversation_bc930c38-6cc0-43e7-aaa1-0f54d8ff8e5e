.ticket-status-sec {
  margin: 0;
  padding: 0;
  position: relative;

  .ticket-status-body {
    margin: 0 auto;
    padding: 0 25px 50px 25px;
    position: relative;
    max-width: 100%;
  }
}

.knowlwdge-base-form {
  margin: 0 auto;
  padding: 0;
  max-width: 600px;

  form {
    margin: 0;
    padding: 0;
    position: relative;

    label.knowlwdge-base-search {
      margin: 0;
      padding: 0;
      position: relative;
      width: 100%;

      span {
        margin: auto;
        padding: 0;
        position: absolute;
        top: 0;
        bottom: 0;
        right: 16px;
        height: fit-content;
        width: fit-content;
        color: #758193;
      }

      input {
        margin: 0;
        padding: 0 11px;
        height: 44px;
        background: #f0f7ff;
        border-radius: 283px;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        border: 1px solid rgba(134, 153, 169, 0.3803921569);
        width: 100%;
      }
    }
  }
}

.knowlwdge-base-sec {
  margin: 50px auto 0 auto;
  padding: 0 40px;
  max-width: 1000px;
  display: flex;
  justify-content: space-between;
  gap: 0 5%;

  .knowlwdge-base-box {
    margin: 15px 0 0 0;
    padding: 18px;
    background: var(--snjy-color-white);
    border-radius: 12px;
    border: 1px solid rgba(208, 215, 216, 0.3882352941);
    box-shadow: var(--snjy-box-shadow);
    text-align: center;
    flex: 1;

    span {
      width: 60px;
      height: 60px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: #0077d7;
      border-radius: 100px;
      margin: 0 0 20px 0;
      color: var(--snjy-color-white);
      font-size: 26px;
      font-weight: var(--snjy-font-weight-normal);
    }

    h3 {
      margin: 0 0 15px 0;
      padding: 0 5px;
      position: relative;
      line-height: 22px;
      font-weight: var(--snjy-font-weight-bold);
      font-size: var(--snjy-font-size-0-9);
      color: var(--snjy-color-dark-secondary);
      text-align: center;
      text-transform: uppercase;
    }

    p {
      margin: 0;
      padding: 0;
      font-size: var(--snjy-font-size-0-9);
      font-weight: var(--snjy-font-weight-semi-bold);
      color: var(--snjy-color-text-ternary);
      line-height: 20px;
    }
  }
}

@media only screen and (max-width: 576px) {
.all-main-title-sec {
  align-items: center !important;
}
.ticket-status-sec .ticket-status-body .knowlwdge-base-sec {
  flex-direction: column;
  margin: 20px auto 0 auto;
  padding: 0 !important;
}
}



