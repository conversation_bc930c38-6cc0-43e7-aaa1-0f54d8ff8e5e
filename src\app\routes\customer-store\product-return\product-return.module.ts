import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { SharedModule } from "src/app/shared/shared.module";
import { ProductReturnComponent } from "./product-return.component";

const Routes = [{ path: "", component: ProductReturnComponent }];

@NgModule({
  declarations: [ProductReturnComponent],
  imports: [CommonModule, SharedModule, RouterModule.forChild(Routes)],
})
export class ProductReturnModule {}
