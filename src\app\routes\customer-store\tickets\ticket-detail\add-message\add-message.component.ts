import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from "@angular/core";
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from "@angular/forms";
import { AppToastService } from "src/app/shared/services/toast.service";
import { Subject, takeUntil } from "rxjs";
import { TicketsService } from "../../tickets.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
  selector: "app-add-message",
  templateUrl: "./add-message.component.html",
  styleUrls: ["./add-message.component.scss"],
})
export class AddMessageComponent implements OnInit, OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  public submitted: any = false;
  public allowedFileTypes =
    ".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.ppt,.pptx,.pps,.ppsx,.odt,.xls,.xlsx,.mp3,.mp4,.m4a,.ogg,.wav,.m4v,.mov,.wmv,.avi,.mpg,.ogv,.3gp,.3g3,.zip";
  public attachments: any = [];
  public allowedFileSize = 2 * 1024 * 1024;
  public saving = false;
  public form: FormGroup = this.formBuilder.group({
    Description: ["", Validators.required],
  });
  @ViewChild("fileInput") fileInput: any;
  @Input() data: any = {};

  constructor(
    private formBuilder: FormBuilder,
    private _snackBar: AppToastService,
    public activeModal: NgbActiveModal,
    private ticketsService: TicketsService
  ) { }

  ngOnInit() { }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onFileSelected(event: any) {
    this.attachments = [];
    if (event.target.files?.length > 3) {
      this._snackBar.open("Please select upto 3 files.", { type: 'Warning' });
      return;
    }
    const files: File[] = event.target.files;
    for (let i = 0; i < files.length; i++) {
      const file: File = files[i];
      if (file.size > this.allowedFileSize) {
        this._snackBar.open(
          `Maximum file size limit exceeded for ${file.name}. (2 MB)`, { type: 'Warning' }
        );
        return;
      }
      this.setBase64(file);
    }
  }

  setBase64(file: File) {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (reader.result) {
        this.attachments.push({
          Content: reader.result
            .toString()
            .replace("data:", "")
            .replace(/^.+,/, ""),
          Name: file.name,
        });
      }
    };
    reader.onerror = (error) => {
      this._snackBar.open(
        `Error while reading your file ${file.name}, Please select again.`, { type: 'Error' }
      );
      console.log("Error: ", error);
    };
  }

  removeFile(event: any, index: number) {
    event && event.stopPropagation();
    this.attachments.splice(index, 1);
    this.fileInput.nativeElement.value = "";
  }

  addMessage() {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    this.saving = true;
    const value = this.form.value;
    let body = {
      ...value,
    };
    if (this.attachments && this.attachments.length) {
      body.Attachments = this.attachments;
    }

    this.ticketsService
      .update(this.data.TICKET_ID, body)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.saving = false;
          this.activeModal.close(res);
        },
        error: () => {
          this.saving = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
