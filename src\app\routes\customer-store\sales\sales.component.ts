import { Component } from "@angular/core";
import { AuthService } from "src/app/core/authentication/auth.service";

@Component({
  selector: "app-sales",
  templateUrl: "./sales.component.html",
  styleUrls: ["./sales.component.scss"],
})
export class SalesComponent {
  public customer_id: any = null;
  public settings: any = { company_name: "Sales Portal" };

  constructor(private authService: AuthService) {
    const auth = this.authService.getAuth();
    this.customer_id = auth?.partner_function?.customer_id || null;
  }
}
