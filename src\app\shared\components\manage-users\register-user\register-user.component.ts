import { Component, Input } from "@angular/core";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Subject, Observable, takeUntil, concat, of, map } from "rxjs";
import {
  catchError,
  distinctUntilChanged,
  switchMap,
  tap,
} from "rxjs/operators";

import { AppToastService } from "src/app/shared/services/toast.service";
import { ManageUserService } from "../manage-user-service/manage-user.service";
import { US_STATES } from "src/app/constants/us-states";
import { AuthService } from "src/app/core/authentication/auth.service";
import { RolesType } from "src/app/constants/api.constants";

@Component({
  selector: "app-register-user",
  templateUrl: "./register-user.component.html",
  styleUrls: ["./register-user.component.scss"],
})
export class RegisterUserComponent {
  private ngUnsubscribe = new Subject<void>();
  @Input() userRoleType: any;
  public form: FormGroup = this.formBuilder.group({
    customers: [[], Validators.required],
    role: ["", Validators.required],
    firstName: ["", [Validators.required]],
    lastName: ["", [Validators.required]],
    email: ["", [Validators.required, Validators.email]],
    addressLine1: [""],
    addressLine2: [""],
    city: [""],
    state: [""],
    zipcode: [""],
    country: ["USA"],
    acceptTerms: [false, Validators.requiredTrue],
    cust_is_selected: [false],
  });
  public submitted = false;
  public saving = false;
  public states: Array<any> = US_STATES;
  public userRoles: any[] = [];
  public roleHierarchy: any[] = [];
  public loggedInUserRole: any = null;

  private defaultOptions: any = [];
  public customers$: Observable<any[]>;
  public customerLoading = false;
  public customerInput$ = new Subject<string>();

  constructor(
    private formBuilder: FormBuilder,
    private manageUserService: ManageUserService,
    private _snackBar: AppToastService,
    public activeModal: NgbActiveModal,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    this.defaultOptions =
      this.userRoleType === RolesType.STOREFRONT
        ? []
        : [{ customer_id: "ALL" }];
    this.form.patchValue({
      cust_is_selected: this.userRoleType === RolesType.STOREFRONT,
    });
    this.getUserRoles();
    this.loadCustomers();
  }

  trackByFn(item: any) {
    return item.customer_id;
  }

  private loadCustomers() {
    this.customers$ = concat(
      of(this.defaultOptions), // default items
      this.customerInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.customerLoading = true)),
        switchMap((term: any) => {
          if (term && term.length < 2) {
            this.customerLoading = false;
            return of(this.defaultOptions);
          }
          return this.manageUserService.getCustomers({ search: term }).pipe(
            map((res: any) => {
              let data = res.data || [];
              if (this.defaultOptions[0]) {
                data.unshift(this.defaultOptions[0]);
              }
              return res.data;
            }),
            catchError(() => of(this.defaultOptions)), // empty list on error
            tap(() => (this.customerLoading = false))
          );
        })
      )
    );
  }

  onAddCustOption($event: any) {
    if ($event.customer_id === "ALL") {
      this.form.patchValue({ customers: this.defaultOptions });
    } else {
      const selectedCust = this.f.customers.value;
      const index = selectedCust.findIndex((o: any) => o.customer_id === "ALL");
      if (index > -1) {
        selectedCust.splice(index, 1);
        this.form.patchValue({ customers: selectedCust });
      }
    }
  }

  get f(): any {
    return this.form.controls;
  }

  getUserRoles() {
    this.manageUserService
      .getUserRoles()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          const data = res?.data || [];
          this.loggedInUserRole = data.find(
            (d: any) => d.id === this.auth?.userDetail?.user_role_id
          );
          this.roleHierarchy = this.loggedInUserRole?.roleHierarchy || [];
          this.userRoles = data.filter((o: any) =>
            this.userRoleType.includes(o.role_type)
          );
        },
        error: (err) => {
          this._snackBar.open("Error while processing get user role request.", {
            type: "Error",
          });
        },
      });
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    const value = this.form.value;
    const customers = Array.isArray(value.customers)
      ? value.customers
      : [value.customers];
      if (customers.length === 1 && customers.find((c: any)=> c.customer_id !== "ALL")) {
        value.cust_is_selected = true;
      }
    this.manageUserService
      .createUser({
        roles: [value.role],
        customers: customers.map((o: any) => o.customer_id),
        cust_is_selected: value.cust_is_selected,
        first_name: value.firstName,
        last_name: value.lastName,
        email: value.email,
        address: `${value.addressLine1 ? value.addressLine1 + ", " : ""}${
          value.addressLine2 ? value.addressLine2 + ", " : ""
        }${value.city ? value.city + ", " : ""}${
          value.state ? value.state + ", " : ""
        }${value.zipcode ? value.zipcode + ", " : ""}${
          value.country ? value.country + "." : ""
        }`,
      })
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        complete: () => {
          this.onReset();
          this.activeModal.close();
          this.saving = false;
          const msg =
            this.userRoleType === RolesType.STOREFRONT ? "Contact" : "User";
          this._snackBar.open(`${msg} registered successfully!`);
        },
        error: (res) => {
          this.saving = false;
          const msg: any = res?.error?.message || null;
          if (msg) {
            if (
              msg &&
              msg.includes("unique constraint violated") &&
              msg.includes("constraint='EMAIL'")
            ) {
              this._snackBar.open("Give email address already in use.", {
                type: "Error",
              });
            } else {
              this._snackBar.open(res?.error?.message, {
                type: "Error",
              });
            }
          } else {
            this._snackBar.open("Error while processing your request.", {
              type: "Error",
            });
          }
        },
      });
  }

  onReset(): void {
    this.submitted = false;
    this.form.reset();
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
