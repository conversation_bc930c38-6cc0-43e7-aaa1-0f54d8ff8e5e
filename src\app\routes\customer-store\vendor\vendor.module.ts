import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { SharedModule } from "src/app/shared/shared.module";
import { VendorComponent } from "../vendor/vendor.component";
import { VendorAccountComponent } from "./vendor-account/vendor-account.component";
import { VendorReportsComponent } from './vendor-reports/vendor-reports.component';
import { VendorQuoteManagerComponent } from './vendor-quote-manager/vendor-quote-manager.component';
import { PlaceVendorBidComponent } from './vendor-quote-manager/place-vendor-bid/place-vendor-bid.component';

const routes: Routes = [
  { path: "", component: VendorComponent },
  { path: "account", component: VendorAccountComponent },
  { path: "reports", component: VendorReportsComponent },
  { path: "quote-manager", component: VendorQuoteManagerComponent },
];

@NgModule({
  declarations: [VendorComponent, VendorAccountComponent, VendorReportsComponent, VendorQuoteManagerComponent, PlaceVendorBidComponent],
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
})
export class VendorModule {}
