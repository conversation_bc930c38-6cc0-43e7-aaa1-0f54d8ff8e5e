import { Component, OnInit } from '@angular/core';
import { SavedCartsService } from '../saved-carts/saved-carts.service';
import { ActivatedRoute, Router } from '@angular/router';
import moment from 'moment';
import { RestoreCartComponent } from '../saved-carts/restore-cart/restore-cart.component';
import { CartService } from '../services/cart.service';
import { AppToastService } from 'src/app/shared/services/toast.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-saved-cart-detail',
  templateUrl: './saved-cart-detail.component.html',
  styleUrls: ['./saved-cart-detail.component.scss']
})
export class SavedCartDetailComponent implements OnInit {

  loading = false;
  cartDetails: any = {};
  moment = moment;
  objectKeys = Object.keys;

  constructor(
    private service: SavedCartsService,
    private _snackBar: AppToastService,
    private activatedRoute: ActivatedRoute,
    private cartService: CartService,
    private router: Router,
    public dialog: NgbModal
  ) { }

  ngOnInit() {
    this.activatedRoute.paramMap.subscribe(params => {
      const id = params.get('id');
      id && this.getCartDetail(id);
    });
  }

  getCartDetail(customerID: string) {
    this.service.getCartDetailById(customerID)
      .subscribe({
        next: (value: any) => {
          this.cartDetails = value.data;
          if (this.cartDetails.payload) {
            this.cartDetails.payload = JSON.parse(this.cartDetails.payload);
          }
        },
        error: (err) => {
          this._snackBar.open('Error while processing get customer request.', { type: 'Error' });
        },
      })
  }

  restore(data: any) {
    const dialogRef = this.dialog.open(RestoreCartComponent);
    dialogRef.componentInstance.data = data;
    dialogRef.result.then(result => {
      this.cartService.setSOSReq(result?.data?.so_simulation_req_data);
      this.cartService.salesOrderSimulation();
    });
  }

  delete(data: any) {
    this.service.delete(data.id).subscribe({
      next: (value) => {
        this._snackBar.open('Cart deleted.');
        this.router.navigate([`store/saved-cart`]);
      },
      error: (err) => {
        this.loading = false;
        this._snackBar.open('Error while processing your request.', { type: 'Error' });
      }
    });
  }

}
