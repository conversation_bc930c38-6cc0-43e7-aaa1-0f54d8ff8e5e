import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, Input } from "@angular/core";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { SidePanelService } from "../../../core/";
import { SidePanelState } from "../../../core/";
import { AuthService } from "src/app/core/authentication/auth.service";

@Component({
  selector: "app-navigation-bar",
  templateUrl: "./navigation-bar.component.html",
  styleUrls: ["./navigation-bar.component.scss"],
})
export class NavigationBarComponent implements OnInit {
  private _subscriptionsSubject$: Subject<void>;
  public currentPanelState!: SidePanelState;
  public userName: any;

  constructor(
    private _sidePanelService: SidePanelService,
    private authService: AuthService
  ) {
    this._subscriptionsSubject$ = new Subject<void>();
  }

  ngOnInit(): void {
    this.userName = this.authService?.userDetail?.display_name || "";
    this._sidePanelService.panelStateChanges
      .pipe(takeUntil(this._subscriptionsSubject$))
      .subscribe((state: SidePanelState) => (this.currentPanelState = state));
  }

  logout() {
    this.authService.doLogout();
  }

  ngOnDestroy(): void {
    this._subscriptionsSubject$.next();
    this._subscriptionsSubject$.complete();
  }
}
