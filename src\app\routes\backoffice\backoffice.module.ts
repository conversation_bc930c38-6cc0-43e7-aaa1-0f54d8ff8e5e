import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardComponent } from './dashboard/dashboard.component';
import { CustomerComponent } from './customer/customer.component';
import { ProductComponent } from './product/product.component';
import { BackOfficeRoutingModule } from './backoffice-routing.module';
import { AgGridModule } from 'ag-grid-angular';
import { SharedModule } from 'src/app/shared/shared.module';
import { ProductDetailsComponent } from './product/product-details/product-details.component';
import { BackOfficeLayoutComponent } from './layout/backoffice-layout.component';
import { CoreModule } from '../../core/core.module';
import { CustomerDetailsComponent } from './customer/customer-details/customer-details.component';
import { AngularEditorModule } from '@kolkov/angular-editor';



@NgModule({
  declarations: [BackOfficeLayoutComponent,DashboardComponent,CustomerComponent,ProductComponent, ProductDetailsComponent, CustomerDetailsComponent],
  imports: [
    CoreModule,
    AgGridModule,
    SharedModule,
    AngularEditorModule,
    BackOfficeRoutingModule
  ]
})
export class BackofficeModule { }
