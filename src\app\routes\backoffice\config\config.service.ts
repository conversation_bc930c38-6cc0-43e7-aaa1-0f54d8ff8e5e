import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, forkJoin, of } from 'rxjs';
import { ApiConstant } from 'src/app/constants/api.constants';

@Injectable({
  providedIn: 'root'
})
export class ConfigService {

  constructor(private http: HttpClient) { }

  getTabsConfig() {
    return [
      {
        title: 'Invoices',
        subTabs: [
          {
            hasErrors: false,
            id: 'invoice-statuses',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Invoice Statuses",
            data: [],
            loading: false,
            url: ApiConstant.GET_INVOICE_STATUSES,
            columns: [
              { name: 'Status Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Status Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'invoice-types',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Transaction Types",
            data: [],
            loading: false,
            url: ApiConstant.GET_INVOICE_TYPES,
            columns: [
              { name: 'Status Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Status Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'invoice-form-types',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Invoice Form Types",
            data: [],
            loading: false,
            url: ApiConstant.GET_INVOICE_FORM_TYPES,
            columns: [
              { name: 'Status Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Status Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
        ]
      },
      {
        title: 'Orders',
        subTabs: [
          {
            hasErrors: false,
            id: 'order-statuses',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: 'Sale Order Statuses',
            data: [],
            loading: false,
            url: ApiConstant.GET_ORDER_STATUSES,
            columns: [
              { name: 'Status Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Status Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'order-types',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Transaction Types",
            data: [],
            loading: false,
            url: ApiConstant.GET_ORDER_TYPES,
            columns: [
              { name: 'Status Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Status Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' },
              { name: 'Use For Creation', value: 'is_creation', required: false, newValue: 'newIsCreation', itemValue: false, type: 'checkbox' }
            ]
          },
          {
            hasErrors: false,
            id: 'special-instructions',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: 'Special Instructions',
            data: [],
            loading: false,
            type: 'SingleValue',
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '', length: 4 },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '', length: 25 }
            ]
          },
        ]
      },
      {
        title: 'General Settings',
        subTabs: [
          {
            hasErrors: false,
            id: 'relationship-types',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Recommendation Types",
            data: [],
            loading: false,
            url: ApiConstant.RELATIONSHIP_TYPES,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'catalogs',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Catalogs",
            data: [],
            loading: false,
            url: ApiConstant.GET_CATALOGS,
            columns: [
              { name: 'Name', value: 'name', required: true, newValue: 'newName', itemValue: '' },
              { name: 'WebStore', value: 'web_store', required: false, newValue: 'newweb_store', itemValue: '' },
              {
                name: 'Language', value: 'language', required: true, newValue: 'newLanguage', itemValue: '', type: 'dd', options: [
                  {
                    name: 'English',
                    value: 'English'
                  }
                ], multiple: false
              },
              //{ name: 'Categories', value: 'categories', required: false, newValue: 'newCategories', itemValue: '', type: 'dd', options: [], multiple: true },
              {
                name: 'Status', value: 'status', required: true, newValue: 'newStatus', itemValue: '', type: 'radio',
                options: [
                  {
                    name: 'Active',
                    value: 'Active'
                  },
                  {
                    name: 'Inactive',
                    value: 'Inactive'
                  },
                ]
              },
            ]
          },
          {
            hasErrors: false,
            id: 'categories',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Categories",
            data: [],
            loading: false,
            url: ApiConstant.GET_CATEGORIES,
            columns: [
              { name: 'Name', value: 'name', required: true, newValue: 'newName', itemValue: '' },
              { name: 'Parent Category', value: 'parent_category_id', required: false, newValue: 'newparent_category_id', itemValue: '', type: 'dd', options: [], multiple: false },
            ]
          },
          {
            hasErrors: false,
            id: 'stock',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Stock",
            data: [],
            type: 'SingleValue',
            loading: false,
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Low Stock Quantity', value: 'low_stock_qty', required: true, newValue: 'lowStockQuantity', itemValue: 1, type: 'number' },
            ]
          },
          {
            hasErrors: false,
            id: 'footer',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Footer",
            data: [],
            type: 'multiValue',
            loading: false,
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Home', value: 'home', newValue: 'newHome', type: 'string', itemValue: '' },
              { name: 'Terms of Use', value: 'terms_of_use', newValue: 'newTermsOfUse', type: 'string', itemValue: '' },
              { name: 'Privacy Policy', value: 'privacy_policy', newValue: 'newPrivacyPolicy', type: 'string', itemValue: '' },
              { name: 'Contact Us', value: 'contact_us', newValue: 'newContactUs', type: 'string', itemValue: '' },
            ]
          },
          {
            hasErrors: false,
            id: 'notification',
            saving: false,
            updation: true,
            deletion: false,
            creation: false,
            title: "Notifications",
            data: [],
            loading: false,
            url: ApiConstant.NOTIFICATION,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '', readonly: true },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '', readonly: true },
              { name: 'Allow Notification', value: 'is_send', required: false, newValue: 'newIsSend', itemValue: false, type: 'checkbox' }
            ]
          },
          {
            hasErrors: false,
            id: 'conditions',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Conditions",
            data: [],
            type: 'multiValue',
            loading: false,
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Base Price', value: 'base_price_code', newValue: 'newbase_price_code', type: 'string', itemValue: '', maxlength: 4 },
              { name: 'Tax', value: 'tax_code', newValue: 'newtax_code', type: 'string', itemValue: '', maxlength: 4 },
              { name: 'Shipping', value: 'shipping_code', newValue: 'newshipping_code', type: 'string', itemValue: '', maxlength: 4 },
              { name: 'Discount', value: 'discount_code', newValue: 'newdiscount_code', type: 'string', itemValue: '', maxlength: 4 },
            ]
          },
        ]
      },
      {
        title: 'Service',
        subTabs: [
          {
            hasErrors: false,
            id: 'ticket-statuses',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: 'Ticket Status',
            data: [],
            loading: false,
            url: ApiConstant.TICKET_STATUSES,
            columns: [
              { name: 'Status Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Status Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          }
        ]
      },
      {
        title: 'Quote',
        subTabs: [
          {
            hasErrors: false,
            id: 'quote-statuses',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: 'Quote Status',
            data: [],
            loading: false,
            url: ApiConstant.QUOTE,
            columns: [
              { name: 'Status Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Status Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'approval-amount',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Approval Amount",
            data: [],
            loading: false,
            type: 'SingleValue',
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Approval amount', value: 'min_quote_price', required: true, newValue: 'minQuotePrice', itemValue: 1, type: 'number' },
            ]
          },
          {
            hasErrors: false,
            id: 'quote-transaction-type',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: 'Transaction Type',
            data: [],
            loading: false,
            type: 'SingleValue',
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'quote-description',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: 'Quote Description',
            data: [],
            loading: false,
            type: 'SingleValue',
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '', length: 4 },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '', length: 25 }
            ]
          }
        ]
      },
      {
        title: 'Returns',
        subTabs: [
          {
            hasErrors: false,
            id: 'return-status',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Return Status",
            data: [],
            loading: false,
            url: ApiConstant.RETURN_STATUS,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'refund-progress',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Refund Progress",
            data: [],
            loading: false,
            url: ApiConstant.REFUND_PROGRESS,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '', length: '1' },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'return-transaction-type',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: 'Transaction Type',
            data: [],
            loading: false,
            type: 'SingleValue',
            url: ApiConstant.SETTINGS,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
          {
            hasErrors: false,
            id: 'return-reason',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Return Reason",
            data: [],
            loading: false,
            url: ApiConstant.RETURN_REASON,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
        ]
      },
      {
        title: 'Customer',
        subTabs: [
          {
            hasErrors: false,
            id: 'cust-text-type',
            saving: false,
            updation: true,
            deletion: true,
            creation: true,
            title: "Text Type",
            data: [],
            loading: false,
            url: ApiConstant.CUSTOMER_TEXT_DESCR,
            columns: [
              { name: 'Code', value: 'code', required: true, newValue: 'newCode', itemValue: '' },
              { name: 'Description', value: 'description', required: true, newValue: 'newDescription', itemValue: '' }
            ]
          },
        ]
      },
    ];
  }

  getAll(tabs: Array<any>) {
    const obj: any = {};
    for (let i = 0; i < tabs.length; i++) {
      const tabDetails = tabs[i];
      obj[tabDetails.id] = this.get(tabDetails.url).pipe(catchError(error => of(error)));
    }
    return forkJoin(obj);
  }

  get(url: string) {
    return this.http.get<any>(url);
  }

  save(url: string, data: any) {
    return this.http.post<any>(url, data);
  }

  update(url: string, data: any, id: string) {
    return this.http.put<any>(`${url}/${id}`, data);
  }

  delete(url: string, id: string) {
    return this.http.delete<any>(`${url}/${id}`);
  }
}
