import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiConstant } from 'src/app/constants/api.constants';
import { AuthService } from 'src/app/core/authentication/auth.service';

@Injectable({
  providedIn: 'root'
})
export class SavedCartsService {

  constructor(
    private http: HttpClient,
    private auth: AuthService
  ) { }

  get() {
    return this.http.get(ApiConstant.CARTS);
  }

  save(data: any) {
    return this.http.post(`${ApiConstant.CARTS}/${this.getCartID()}`, data);
  }

  delete(id: string) {
    return this.http.delete(`${ApiConstant.CARTS}/${id}`);
  }

  restore(id: string, keep_copy: boolean) {
    return this.http.put(`${ApiConstant.CARTS}/${id}`, { keep_copy });
  }

  getCartID() {
    return this.auth.getAuth()?.cart?.id || null;
  }

  getCartDetailById(id: string) {
    return this.http.get<any>(`${ApiConstant.CARTS}/${id}`);
  }

  import(data: any) {
    return this.http.post(`${ApiConstant.CARTS}/upload/csv`, data);
  }

}
