import { NgModule } from "@angular/core";
import { CommonModule, CurrencyPipe } from "@angular/common";
import { RouterModule } from "@angular/router";

import { CartComponent, QuantityInputComponent } from "./cart.component";
import { SharedModule } from "src/app/shared/shared.module";
import { SaveCartComponent } from "./save-cart/save-cart.component";
import { RequestQuoteComponent } from "./request-quote/request-quote.component";
import { ReactiveFormsModule } from "@angular/forms";

@NgModule({
  declarations: [
    CartComponent,
    QuantityInputComponent,
    SaveCartComponent,
    RequestQuoteComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    RouterModule.forChild([{ path: "", component: CartComponent }]),
  ],
  providers: [CurrencyPipe],
})
export class CartModule {}
