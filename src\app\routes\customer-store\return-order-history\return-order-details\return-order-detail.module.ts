import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { SharedModule } from "src/app/shared/shared.module";
import { ReturnOrderDetailsComponent } from "./return-order-details.component";

@NgModule({
  declarations: [ReturnOrderDetailsComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: ReturnOrderDetailsComponent }]),
  ],
})
export class ReturnOrderDetailModule { }
