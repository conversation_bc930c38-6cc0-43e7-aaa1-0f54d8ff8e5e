import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AgGridModule } from "ag-grid-angular";

import { AdministrationRoutingModule } from "./administration-routing.module";
import { AdministrationComponent } from "./administration/administration.component";
import { ProductsComponent } from "./products/products.component";
import { SharedModule } from "src/app/shared/shared.module";
import { UserRoleComponent } from "./user-role/user-role.component";
import { AppFeaturesComponent } from "./app-features/app-features.component";
import { UserRolePermissionsComponent } from "./user-role/user-role-permissions/user-role-permissions.component";
import { NestedTableComponent } from "./user-role/user-role-permissions/nested-table/nested-table.component";
import { CustomerGroupComponent } from "./customer-group/customer-group.component";
import { BannerComponent } from "./banner/banner.component";
import { BannerModalComponent } from "./banner/banner-modal/banner-modal.component";
import { AngularEditorModule } from "@kolkov/angular-editor";
import { ActionComponent } from "./banner/banner-modal/action/action.component";
import { NgbNavModule } from "@ng-bootstrap/ng-bootstrap";

@NgModule({
  declarations: [
    AdministrationComponent,
    ProductsComponent,
    UserRoleComponent,
    AppFeaturesComponent,
    UserRolePermissionsComponent,
    NestedTableComponent,
    CustomerGroupComponent,
    BannerComponent,
    BannerModalComponent,
    ActionComponent,
  ],
  imports: [
    CommonModule,
    AgGridModule,
    SharedModule,
    AdministrationRoutingModule,
    AngularEditorModule,
    NgbNavModule,
  ],
})
export class AdministrationModule {}
