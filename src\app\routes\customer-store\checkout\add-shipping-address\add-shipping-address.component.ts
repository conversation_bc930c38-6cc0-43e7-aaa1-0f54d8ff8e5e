import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@angular/core";
import { FormBuilder, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { Subject, takeUntil } from "rxjs";

import { AuthService } from "src/app/core/authentication/auth.service";
import { BusinessPartnersService } from "../../services/business-partners/business-partners.service";
import { US_STATES } from "src/app/constants/us-states";
import { GET_CITIES } from "src/app/constants/us-cities";
import { SalesOrderService } from "../../services/sales-order/sales-order.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-add-shipping-address",
  templateUrl: "./add-shipping-address.component.html",
  styleUrls: ["./add-shipping-address.component.scss"],
})
export class AddShippingAddressComponent implements OnInit, On<PERSON><PERSON>roy {
  private ngUnsubscribe = new Subject<void>();
  public form: any;
  public states: any = US_STATES;
  public cities: any = [];
  public submitted: boolean = false;
  public saving: boolean = false;
  public titles: any = [{ code: "0003", description: "Company" }];

  constructor(
    public router: Router,
    public fb: FormBuilder,
    private _snackBar: AppToastService,
    private auth: AuthService,
    private bpService: BusinessPartnersService,
    private salesOrderService: SalesOrderService
  ) {}

  ngOnInit(): void {
    this.createForm();
    this.updateForm();
  }

  createForm() {
    this.form = this.fb.group({
      SoldToParty: [""],
      AcademicTitle: [""],
      BusinessPartnerCategory: [""],
      OrganizationBPName1: ["", Validators.required],
      OrganizationBPName2: [""],
      OrganizationBPName3: [""],
      OrganizationBPName4: [""],
      to_BusinessPartnerAddress: this.fb.group({
        Country: ["US"],
        HouseNumber: [""],
        StreetName: ["", Validators.required],
        District: [""],
        POBox: [""],
        Region: ["", Validators.required],
        PostalCode: [
          "",
          [
            Validators.required,
            Validators.pattern(/^[0-9]{5}(?:-[0-9]{4})?$/gi),
          ],
        ],
        CityName: ["", Validators.required],
        Language: ["EN"],
        to_PhoneNumber: this.fb.group({
          PhoneNumber: [""],
        }),
        to_EmailAddress: this.fb.group({
          EmailAddress: ["", Validators.email],
        }),
      }),
      to_BusinessPartnerRole: this.fb.array([
        this.fb.group({
          BusinessPartnerRole: ["FLCU01"],
        }),
        this.fb.group({
          BusinessPartnerRole: ["CRM002"],
        }),
      ]),
      to_Customer: this.fb.group({
        to_CustomerSalesArea: this.fb.group({
          SalesOrganization: [""],
          DistributionChannel: [""],
          Division: [""],
          Currency: ["USD"],
        }),
      }),
    });
  }

  get f(): any {
    return this.form.controls;
  }

  get fToCustomer(): any {
    return this.f.to_Customer.controls;
  }

  get fToBPAddress(): any {
    return this.f.to_BusinessPartnerAddress.controls;
  }

  get fbpTOEmail(): any {
    return this.fToBPAddress.to_EmailAddress.controls;
  }

  getBPDetailByID(bpID: string) {
    this.bpService
      .getBusinessPartnerByID(bpID)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.form.patchValue({ SoldToParty: res?.data?.bp_id || "" });
          this.form.patchValue({
            BusinessPartnerCategory: res?.data?.bp_category || "",
          });
          this.form.patchValue({
            OrganizationBPName3: res?.data?.org_bp_name3 || "",
          });
          this.form.patchValue({
            OrganizationBPName4: res?.data?.org_bp_name4 || "",
          });
        },
        error: (e) => {
          console.error("Error while processing business partner request.", e);
        },
      });
  }

  updateForm() {
    const loggedInUser = this.auth.getAuth();

    // Update customer info
    this.fToCustomer.to_CustomerSalesArea.patchValue({
      SalesOrganization:
        loggedInUser?.partner_function?.sales_organization || "",
    });
    this.fToCustomer.to_CustomerSalesArea.patchValue({
      DistributionChannel:
        loggedInUser?.partner_function?.distribution_channel || "",
    });
    this.fToCustomer.to_CustomerSalesArea.patchValue({
      Division: loggedInUser?.partner_function?.division || "",
    });

    // Update business partner detail
    this.getBPDetailByID(loggedInUser?.partner_function?.bp_customer_number);
  }

  getCities($event: any) {
    this.cities = GET_CITIES($event.target.value);
  }

  addShippingAddress() {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    this.salesOrderService
      .addShippingAddress(this.form.value)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.submitted = false;
          this.saving = false;
          this._snackBar.open("Shipping address created successfully.");
          this.router.navigate([`/store/checkout`]);
        },
        error: (e: any) => {
          this.submitted = false;
          this.saving = false;
          this._snackBar.open("Error while creating shipping address.", {
            type: "Error",
          });
        },
      });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
