:host {
    padding: 32px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    overflow: auto;
    justify-content: center;
}

.items-container {
    flex-grow: 1;

    .title {
        font-family: var(--snjy-font-family-secondary);
        font-weight: var(--snjy-font-weight-bold);
        font-size: var(--snjy-font-size-3);
        color: var(--snjy-color-text-primary);
        margin-right: 12px;
    }

    .items-to-be-shipped {
        font-family: var(--snjy-font-family-ternary);
        font-weight: var(--snjy-font-weight-medium);
        font-size: var(--snjy-font-size-1);
        color: var(--snjy-color-text-primary);
        margin-top: 1.5rem;
        margin-bottom: .5rem;
    }

    .items {
        padding: 1rem 0;
        border-bottom: 1px solid #EBEBEB;

        img {
            height: 160px;
            width: auto;
        }

        .desc {
            p {
                font-family: var(--snjy-font-family-ternary);
                color: var(--snjy-color-text-secondary);

                &:nth-child(1) {
                    font-weight: var(--snjy-font-weight-medium);
                    font-size: var(--snjy-font-size-0-875);
                }

                &:nth-child(2) {
                    font-weight: var(--snjy-font-weight-normal);
                    font-size: 10px;
                    color: #5F9EFE;
                }

                &.quantity {
                    font-weight: var(--snjy-font-weight-medium);
                    font-size: var(--snjy-font-size-0-75);
                    margin-bottom: 8px;
                }
            }

            input {
                font-family: var(--snjy-font-family-ternary);
                width: 50px;
                text-align: center;
                font-size: var(--snjy-font-size-0-875);
                color: var(--snjy-font-color-seconday);
                border: 0.5px solid #CCCCCC;
            }

        }

        .cost {
            p {
                font-family: var(--snjy-font-family-ternary);
                text-align: right;

                &:nth-child(1) {
                    font-weight: var(--snjy-font-weight-medium);
                    font-size: var(--snjy-font-size-1);
                    color: var(--snjy-color-text-primary);
                }

                &:nth-child(2) {
                    font-weight: var(--snjy-font-weight-semi-bold);
                    font-size: 10px;
                    color: var(--snjy-color-text-secondary);
                }
            }
        }

        mat-icon {
            color: #E74C3C;
            cursor: pointer;
        }
    }


    .details-container {
        background-color: var(--snjy-color-main-background-ternary);

        p {
            font-family: var(--snjy-font-family-ternary);
            color: var(--snjy-color-text-secondary);
            font-size: var(--snjy-font-size-0-75);

            &.title {
                font-weight: var(--snjy-font-weight-medium);
                color: var(--snjy-color-text-primary);
            }
        }

        .shipping-address-container {
            max-width: 200px;

            mat-icon {
                color: var(--snjy-color-primary);
            }

            p {
                font-family: var(--snjy-font-family-ternary);
                font-weight: var(--snjy-font-weight-normal);
                font-size: var(--snjy-font-size-0-75);
                color: var(--snjy-color-text-secondary);

                &:nth-child(1) {
                    font-weight: var(--snjy-font-weight-medium);
                    font-size: var(--snjy-font-size-0-875);
                    color: var(--snjy-color-text-primary);
                }
            }
        }
    }
}

.checkout-container {
    max-width: 400px;

    .order-summary {
        border: 1px solid #EBEBEB;

        >div:nth-child(1) {
            border-bottom: 2px solid #F9C010;
        }

        .title {
            font-family: var(--snjy-font-family-secondary);
            font-weight: var(--snjy-font-weight-bold);
            font-size: var(--snjy-font-size-1-25);
            color: var(--snjy-color-text-primary);
        }

        .items-delevered {
            font-family: var(--snjy-font-family-ternary);
            font-weight: var(--snjy-font-weight-medium);
            font-size: var(--snjy-font-size-0-875);
            color: var(--snjy-color-text-secondary);
            margin-bottom: .8rem;
        }

        .details {
            span:nth-child(1) {
                font-family: var(--snjy-font-family-ternary);
                font-weight: var(--snjy-font-weight-normal);
                font-size: var(--snjy-font-size-0-75);
                color: var(--snjy-color-text-secondary);
            }

            span:nth-child(2) {
                font-family: var(--snjy-font-family-ternary);
                font-weight: var(--snjy-font-weight-semi-bold);
                font-size: var(--snjy-font-size-0-75);
                text-align: right;
                color: var(--snjy-color-text-primary);
            }
        }

        .total {
            font-family: var(--snjy-font-family-ternary);
            font-weight: var(--snjy-font-weight-bold);
            font-size: var(--snjy-font-size-0-75);
            color: var(--snjy-color-text-primary);
        }

        .total-note {
            font-family: var(--snjy-font-family-ternary);
            font-weight: var(--snjy-font-weight-normal);
            font-size: 10px;
            color: var(--snjy-color-text-secondary);
        }

        .total-value {
            font-family: var(--snjy-font-family-ternary);
            font-weight: var(--snjy-font-weight-medium);
            font-size: var(--snjy-font-size-1);
            text-align: right;
            color: var(--snjy-color-text-primary);
        }

        button {
            width: 100%;
            margin-bottom: 1rem;
            padding: .8rem;
            border-radius: 60px;
            font-family: var(--snjy-font-family-secondary);
            font-weight: var(--snjy-font-weight-bold);
            font-size: var(--snjy-font-size-0-875);
            color: var(--snjy-color-text-secondary);
            border: 1px solid var(--snjy-color-text-secondary);
            line-height: 16px;
        }

        .terms {
            font-family: var(--snjy-font-family-ternary);
            font-weight: var(--snjy-font-weight-normal);
            font-size: var(--snjy-font-size-0-75);
            color: var(--snjy-color-dark-secondary);
        }
    }

}

@media (max-width: 480px) {

    .item-detail,
    .availability,
    .desc {
        text-align: center;
        justify-content: center !important;

        input {
            margin: auto;
        }
    }

    :host {
        padding: 16px;
    }
}