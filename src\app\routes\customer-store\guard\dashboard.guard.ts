import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  CanActivateChild,
  Router,
  RouterStateSnapshot,
} from "@angular/router";
import { RolesType } from "src/app/constants/api.constants";

import { AuthService } from "src/app/core/authentication/auth.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Injectable({
  providedIn: "root",
})
export class DashboardGuard implements CanActivateChild {
  constructor(
    private router: Router,
    private auth: AuthService,
    private _snackBar: AppToastService
  ) {}

  async canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<any> {
    return await this.authenticate(childRoute, state.url);
  }

  private async authenticate(
    route: ActivatedRouteSnapshot,
    url: string
  ): Promise<any> {
    const routeData: any = route?.data || null;
    const authority: any = routeData?.authority || null;
    let permissions = this.auth.getPermissions;

    if (authority !== null && !permissions.some((p) => authority.includes(p))) {
      switch (this.auth.role) {
        case RolesType.SALES:
        case RolesType.CUST_SERVICE:
        case RolesType.VENDOR:
          return true;
        default:
          this.errMsg();
          if (
            url.startsWith("/store/sales") ||
            url.startsWith("/store/customer-services") ||
            url.startsWith("/store/vendor")
          ) {
            return this.router.parseUrl("/store/dashboard");
          }
      }
    }
    return true;
  }

  private errMsg() {
    this._snackBar.open(
      "Your don't have access permissions. Please contact snjya support team for assistance.",
      {
        type: "Error",
      }
    );
  }
}
