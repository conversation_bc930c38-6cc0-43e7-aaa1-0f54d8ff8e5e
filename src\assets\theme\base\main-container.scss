.all-main-title-sec {
    margin: 0;
    padding: 30px 0 22px 0;
    opacity: revert;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 20px 0;

    &.product-details-main-title {
        align-items: flex-start;
        justify-content: flex-start;
        text-align: left;
    }

    h1 {
        margin: 0 0;
        padding: 0;
        position: relative;
        font-size: var(--snjy-font-size-3);
        font-weight: var(--snjy-font-weight-bold);
        color: var(--snjy-button-color-primary);
        line-height: 23px;
    }

    .all-bedcrumbs {
        margin: 0;
        padding: 0;
        position: relative;

        ul {
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            list-style: none;
            gap: 0 4px;

            li {
                margin: 0;
                padding: 0 0 0 30px;
                font-size: var(--snjy-font-size-0-875);
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                color: var(--snjy-color-dark-secondary);
                font-weight: var(--snjy-font-weight-medium);

                &:before {
                    position: absolute;
                    content: '\e5c8';
                    font-family: 'Material Icons Outlined';
                    top: 0;
                    left: 5px;
                    bottom: 0;
                    margin: auto 0;
                    font-size: var(--snjy-font-size-1-125);
                    color: var(--snjy-color-dark-secondary);
                    font-weight: var(--snjy-font-weight-normal);
                    line-height: 23px;
                }

                &:first-child {
                    padding: 0;

                    &:before {
                        display: none;
                    }
                }

                a {
                    margin: 0;
                    padding: 0;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    line-height: 20px;
                    gap: 0 2px;
                    color: var(--snjy-color-text-ternary);
                    font-weight: var(--snjy-font-weight-normal);

                    span:not(.material-icons-outlined) {
                        font-size: 22px;
                        font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 40;
                    }
                }
            }
        }
    }
}

@media only screen and (max-width: 1024px) {

    .header {
        align-items: start !important;
    }

    .all-main-title-sec {
        .all-bedcrumbs {
            display: none;
        }
    }

    .save-c-table {
        padding: 15px !important;
    }

    .invoice-table {
        padding: 15px !important;

        table.table {
            tbody {
                td {
                    i {
                        position: relative !important;
                        left: 0 !important;
                    }
                }
            }
        }
    }

    .invoice-sec {
        padding: 15px !important;

        .invoice-body {
            .invoice-contact-list {
                .address-box {
                    flex: none !important;
                }
            }
        }
    }

    table.all-table {
        border: 0 !important;
        min-width: fit-content !important;

        thead {
            border: none;
            display: none !important;
        }

        tr {
            display: block;
            margin-bottom: 10px !important;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 8px;

            td {
                &:last-child {
                    border-top-right-radius: 0 !important;
                    border-bottom-right-radius: 0 !important;
                    border-bottom: none !important;
                }

                &:first-child {
                    border-top-right-radius: 0 !important;
                    border-bottom-left-radius: 0 !important;
                    border-top-left-radius: 0 !important;
                }
            }
        }

        td {
            display: flex;
            flex-direction: row-reverse;
            justify-content: space-between;
            align-items: center;
            text-align: right;
            gap: 0 10px;
            padding: 12px 40px 12px 10px !important;
            border-bottom: 1px solid #ddd !important;

            &::after {
                content: attr(data-label);
                font-weight: 600;
                text-transform: uppercase;
                text-align: left;
            }

            &:before {
                left: auto !important;
                right: 10px;
            }

            .form-control {
                padding: 0 20px !important;
                height: 40px !important;
                text-align: right;
                border: none !important;
                width: fit-content;
            }

            button.add-sku-btn {
                right: 0 !important;
            }
        }
    }

    .order-contact-list {
        flex-direction: column !important;
        gap: 20px !important;
        justify-content: start !important;
        align-items: start !important;
        padding: 15px !important;

        .order-c-icon {
            min-width: 44px !important;
        }
    }

    .invoice-contact-list {
        flex-direction: column !important;
        gap: 20px !important;
        justify-content: start !important;
        align-items: start !important;
        padding: 15px !important;

        .invoice-c-icon {
            min-width: 44px !important;
        }

        .invoice-c-box {
            flex: none !important;
            height: fit-content !important;
        }
    }

    .quote-contact-list {
        flex-direction: column !important;
        gap: 20px !important;
        justify-content: start !important;
        align-items: start !important;
        padding: 15px !important;

        .quote-c-icon {
            min-width: 44px !important;
        }
    }

    .customer-info {
        flex-direction: column !important;
        gap: 20px !important;
        justify-content: start !important;
        align-items: start !important;
        padding: 15px !important;

        .col-3 {
            width: 100%;
            padding: 0;
        }

        .col-6 {
            width: 100%;
            padding: 0;
        }
    }

    .ticket-contact-list {
        flex-direction: column !important;
        gap: 20px !important;
        justify-content: start !important;
        align-items: start !important;
        padding: 15px !important;

        .ticket-c-icon {
            min-width: 44px !important;
        }
    }

    .save-c-btn {
        gap: 10px !important;
        flex-direction: column;

        button {
            min-width: 100% !important;
        }
    }

    .all-form-res {
        padding: 15px !important;

        .form-group {
            flex: 0 0 48% !important;
        }

        .form-btn-sec {
            flex-direction: column;
            gap: 10px !important;
            flex: 0 0 100% !important;

            button {
                min-width: 100% !important;
                margin: 0 !important;
            }
        }
    }

    .all-details-page {
        flex-direction: column !important;
    }
}

@media only screen and (max-width:900px) {
    .all-main-title-sec {
        gap: 12px 0;
        width: 100%;

        h1 {
            font-size: var(--snjy-font-size-2);
            line-height: 22px;
        }

        .product-id {
            font-size: var(--snjy-font-size-0-875) !important;
        }

        .all-bedcrumbs ul li {
            padding: 0 0 0 25px;
            font-size: var(--snjy-font-size-0-8);
        }
    }
}

@media only screen and (max-width: 768px) {
    .order-details {
        padding: 15px !important;

        ul {
            padding: 15px !important;
            gap: 15px !important;

            li {
                flex: 0 0 47% !important;

                :last-child {
                    flex: 0 0 100% !important;
                }
            }

            li:last-child {
                flex: 0 0 100% !important;
            }
        }

        .item-box {
            padding: 15px !important;
            flex-direction: column;
            gap: 10px !important;

            .item-box-img {
                flex: 0 0 100% !important;
                min-height: 144px;
            }

            .item-box-bottom-content {
                margin: 20px 0 0 0 !important;
                flex-direction: column !important;
                align-items: start !important;
                gap: 10px !important;

                .item-price {
                    font-size: var(--snjy-font-size-1) !important;
                    text-align: left !important;
                }
            }
        }
    }

    .q-order-buttons-container {
        margin: 20px auto !important;
    }
}

@media only screen and (max-width: 576px) {
    .all-main-title-sec {
        align-items: start;
    }

    .all-main-title-sec .all-bedcrumbs ul {
        align-items: start;
        flex-direction: column;
    }

    .all-main-title-sec h1,
    .all-main-title-sec .h1 {
        font-size: var(--snjy-font-size-1-25);
    }

    .all-main-title-sec.cart-details-main-title h1>span .material-icons-outlined {
        font-size: var(--snjy-font-size-1) !important;
    }

    .save-c-table {
        margin: 10px 0 !important;
        padding: 0 0 30px 0 !important;
        background: none !important;
        border: none !important;
    }

    table.all-table td {
        font-size: var(--snjy-font-size-0-75) !important;
    }

    table.all-table td .form-control {
        width: 66% !important;
    }

    .all-form-res .form-group {
        flex: 0 0 100% !important;
    }

    .order-details {
        ul {
            li {
                flex: 0 0 100% !important;
            }
        }
    }
}

@media only screen and (max-width:480px) {
    .order-details .d-flex {
        flex-direction: column;

        button,
        button:disabled,
        button:hover {
            margin: 5px 0 !important;
        }
    }
}

@media only screen and (max-width:414px) {
    .products-container {
        grid-template-columns: repeat(auto-fill, minmax(100%, 1fr)) !important;

        h3 {
            font-size: var(--snjy-font-size-0-875) !important;
        }

        .pro-extra-c {
            font-size: var(--snjy-font-size-0-75) !important;
        }
    }

    .main-container .pro-details .p-price {
        font-size: var(--snjy-font-size-1) !important;
    }
}