import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { SharedModule } from "src/app/shared/shared.module";
import { ContactListComponent } from "./contact-list.component";

@NgModule({
  declarations: [ContactListComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: ContactListComponent }]),
  ],
})
export class ContactListModule {}
