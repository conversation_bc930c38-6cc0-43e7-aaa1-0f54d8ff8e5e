<div class="p-4">
  <h3 class="mb-2 d-flex justify-content-between">
    <span>Request Quote</span>
    <a class="text-primary" href="javascript:void(0);">
      <span class="material-icons-outlined" (click)="dialogRef.dismiss()">
        close
      </span>
    </a>
  </h3>
  <form [formGroup]="form">
    <div class="form-group mb-3">
      <label class="form-label">Name</label>
      <input class="form-control" type="text" formControlName="name" />
      <span class="text-error" *ngIf="f?.name?.touched && f?.name?.hasError('required')">
        {{ "validation.required" | translate }}
      </span>
    </div>
    <div class="form-group mb-3">
      <label class="form-label">Description</label>
      <textarea class="form-control" rows="3" formControlName="description"></textarea>
      <span class="text-error" *ngIf="f?.description?.touched && f?.description?.hasError('required')">
        {{ "validation.required" | translate }}
      </span>
    </div>
  </form>
  <div>
    <button type="button" class="btn mb-2 d-block w-100" [disabled]="submitting || !form.valid" (click)="onSubmit()">
      {{ submitting ? "Submitting" : "Submit Quote" }}
    </button>
    <button type="button" class="btn btn-light d-flex w-100" (click)="goToCataloguesPage()">
      <span class="material-icons-outlined">local_mall</span>
      Continue Shopping
    </button>
  </div>
  <div class="checkout-id-summary">
    <div class="checkout-id-summary-body">
      <h3>Quote Summary</h3>
      <small>
        {{ sosRes?.to_Item?.A_SalesOrderItemSimulationType.length || 0 }} items
      </small>
      <div class="item-box" *ngFor="
          let product of sosRes?.to_Item?.A_SalesOrderItemSimulationType;
          trackBy: trackBy
        ">
        <ng-container *ngIf="product.Material | getProductImage | async as url">
          <div class="item-box-img" [ngStyle]="{
              'background-image': 'url(' + url + ')'
            }"></div>
        </ng-container>

        <div class="item-box-content">
          <a href="javascript:void(0);" (click)="goToCatalogDetailPage(product?.Material)">
            <div class="d-flex flex-wrap justify-content-between">
              <h4 class="desc text-truncate">
                {{ product?.SalesOrderItemText }}
              </h4>
              <div class="item-price">
                {{ product?.formatted_base_price }}
              </div>
            </div>
            <div class="item-box-list">
              <small>Material No: <span>{{ product?.Material }}</span></small>
              <small>Item Price:
                <span>{{ product?.formatted_base_price_each }}</span></small>
            </div>
            <small>Unit: <span>EACH</span></small>
            <div class="quantity">
              Qty: <span>{{ product.RequestedQuantity }}</span>
            </div>
          </a>
        </div>
      </div>
      <div class="checkout-summary-price">
        <ul>
          <li>
            Subtotal
            <span>{{
              sosRes?.formatted_sub_total || (0 | currency : sosRes?.TransactionCurrency)
              }}</span>
          </li>
        </ul>
        <ul>
          <li class="total-price">
            Total
            <span>{{
              sosRes?.formatted_sub_total || (0 | currency : sosRes?.TransactionCurrency)
              }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>