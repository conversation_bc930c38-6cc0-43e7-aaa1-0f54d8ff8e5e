<div class="p-4">
    <h3 class="mb-2 d-flex justify-content-between">
        <span>Select Customer</span>
        <a class="text-primary" href="javascript:void(0);">
            <span class="material-icons-outlined" (click)="activeModal.dismiss()">
                close
            </span>
        </a>
    </h3>
    <div class="grid-toolbar d-flex justify-content-end align-items-center pb-3 flex-wrap gap-3">
        <input type="text" [ngModel]="searchText" class="form-control flex-grow-1 w-auto"
            placeholder="Search by...Contains, Starts with, Equal to" (ngModelChange)="onSearchChange($event)">
        <select [ngModel]="searchBy" (ngModelChange)="onSearchByChange($event)" class="form-select w-auto"
            aria-label="Default select example">
            <option value="customer_id">Customer ID</option>
            <option value="customer_name" selected>Customer Name</option>
            <option value="cpf_address">Address</option>
            <option value="phone">Phone</option>
        </select>
    </div>
    <ag-grid-angular [getRowId]="getRowId" style="width: 100%; height: 400px;" class="ag-theme-alpine"
        [columnDefs]="columnDefs" [defaultColDef]="defaultColDef" [pagination]="true" [paginationPageSize]="10"
        [gridOptions]="gridOptions" (gridReady)="onGridReady($event)" [rowClass]="rowClass" [paginationPageSizeSelector]="[10, 20, 50, 100]"></ag-grid-angular>
    <div class="mt-3 d-flex justify-content-end btn-container">
        <button class="btn btn-light mb-0 me-3" (click)="onReset()">Clear</button>
        <button (click)="onSubmit()" [disabled]="saving" class="btn mb-0">{{ saving ? 'Selecting' :
            'Select' }}</button>
    </div>
</div>