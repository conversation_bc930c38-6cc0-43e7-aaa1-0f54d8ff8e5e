import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

import { QuoteHistoryComponent } from './quote-history.component';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [QuoteHistoryComponent],
  imports: [
    CommonModule,
    SharedModule,
    NgbDatepickerModule,
    RouterModule.forChild([{ path: '', component: QuoteHistoryComponent }]),
  ]
})
export class QuoteHistoryModule { }
