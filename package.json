{"name": "ppfe", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "copy": "cp manifest.yml dist/ppferls/", "build": "ng build && npm run copy", "build-prod": "ng build --configuration production && npm run copy", "build-staging": "ng build --configuration staging && npm run copy", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^15.0.0", "@angular/cdk": "^15.1.2", "@angular/common": "^15.0.0", "@angular/compiler": "^15.0.0", "@angular/core": "^15.0.0", "@angular/forms": "^15.0.0", "@angular/localize": "^16.1.3", "@angular/platform-browser": "^15.0.0", "@angular/platform-browser-dynamic": "^15.0.0", "@angular/router": "^15.0.0", "@kolkov/angular-editor": "^3.0.0-beta.0", "@ng-bootstrap/ng-bootstrap": "^14.0.0", "@ng-select/ng-select": "^10.0.4", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@popperjs/core": "^2.11.8", "ag-grid-angular": "^31.0.1", "ag-grid-community": "^31.0.1", "bootstrap": "^5.2.3", "crypto-js": "4.1.1", "moment": "^2.29.4", "ngx-image-zoom": "^2.1.0", "rxjs": "~7.5.0", "tslib": "^2.3.0", "xlsx": "0.14.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.0.5", "@angular/cli": "~15.0.5", "@angular/compiler-cli": "^15.0.0", "@types/crypto-js": "4.1.1", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.8.2"}}