import { Component, OnInit } from "@angular/core";
import { InvoiceService } from "./invoice.service";
import {
  forkJoin,
  map,
  take,
  tap,
} from "rxjs";
import moment from "moment";
import { ApiConstant } from "src/app/constants/api.constants";
import { AuthService } from "src/app/core/authentication/auth.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { NgbCalendar, NgbDateStruct } from "@ng-bootstrap/ng-bootstrap";
import { ColDef } from "ag-grid-community";
import { CurrencyPipe } from "@angular/common";

@Component({
  selector: "app-invoices",
  templateUrl: "./invoices.component.html",
  styleUrls: ["./invoices.component.scss"],
})
export class InvoicesComponent implements OnInit {
  statuses: any[] = [];
  types: any[] = [];
  invoices: any[] = [];
  loading = false;
  sellerDetails: any = {};
  searchParams: any = {
    fromDate: "",
    toDate: "",
    type: "all",
    status: "all",
  };
  statusByCode: any = {};
  loadingPdf = false;

  public columnDefs: ColDef[] = [
    {
      field: "INVOICE",
      headerName: 'Invoice #',
      headerComponentParams: { menuIcon: 'pin' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">pin</i>${data.data.INVOICE}</span>`;
      },
      comparator: (valueA, valueB, nodeA, nodeB, isDescending) =>  parseInt(valueA) - parseInt(valueB),
      sortable: true,
      resizable: true
    },
    {
      field: "DOC_DATE",
      headerName: 'Date',
      headerComponentParams: { menuIcon: 'calendar_month' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">calendar_month</i>${this.formatDate(data.data.DOC_DATE)}</span>`;
      },
      sortable: true,
      resizable: true
    },
    {
      field: "AMOUNT",
      headerName: 'Amount',
      headerComponentParams: { menuIcon: 'attach_money' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">attach_money</i>${this.currencyPipe.transform(data.data.AMOUNT, data.data.CURRENCY)}</span>`;
      },
      resizable: true
    },
    {
      field: "CURRENCY",
      headerName: "Currency",
      headerComponentParams: { menuIcon: 'local_atm' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">local_atm</i>${data.data.CURRENCY}</span>`;
      },
      resizable: true
    },
    {
      field: "DOC_STATUS",
      headerName: "Invoice Status",
      headerComponentParams: { menuIcon: 'sticky_note_2' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">sticky_note_2</i>${this.statusByCode[data.data.DOC_STATUS]}</span>`;
      },
      resizable: true
    },
    {
      field: "INVOICE",
      headerName: "Document",
      headerComponentParams: { menuIcon: 'article' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined download">picture_as_pdf</i></span>`;
      },
      resizable: true,
      onCellClicked: (data: any) => {
        this.downloadPDF(data.value);
      }
    },
  ];

  constructor(
    private invoiceService: InvoiceService,
    private _snackBar: AppToastService,
    public authService: AuthService,
    private calendarService: NgbCalendar,
    private currencyPipe: CurrencyPipe
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function
    }
  }

  ngOnInit(): void {
    this.loadOptions();
  }

  search(): void {
    this.loading = true;
    const obj: any = {
      ...this.getDateRange(),
      DOC_STATUS: this.searchParams.status,
      DOC_TYPE: this.searchParams.type,
      SOLDTO: this.sellerDetails.customer_id,
      VKORG: this.sellerDetails.sales_organization,
      COUNT: 100
    };
    this.invoiceService.getAll(obj).pipe(
      map((x) => {
        this.invoices = x.resultData;
        return x.resultData
      }),
      tap((_) => (this.loading = false))
    ).subscribe();
  }

  loadOptions() {
    this.loading = true;
    forkJoin([
      this.invoiceService.getInvoiveStatuses(),
      this.invoiceService.getInvoiveTypes(),
    ]).subscribe({
      next: (results) => {
        this.statuses = [
          { code: results[0].data.map((val: any) => val.code).join(';'), description: "All" },
          ...results[0].data,
        ];
        this.searchParams.status = this.statuses[0].code;
        this.statuses.reduce((acc: { [x: string]: any; }, value: { code: string | number; description: any; }) => {
          acc[value.code] = value.description;
          return acc;
        }, this.statusByCode);
        this.types = [
          { code: results[1].data.map((val: any) => val.code).join(';'), description: "All" },
          ...results[1].data
        ];
        this.searchParams.type = this.types[0].code;
        this.search();
      },
      error: () => {
        this.loading = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  clear() {
    const types = [...[], ...this.types];
    this.types = [];
    setTimeout(() => {
      this.types = types;
    }, 100);
    this.searchParams = {
      fromDate: "",
      toDate: "",
      type: types[0].code,
      status: this.statuses[0].code,
    };
  }

  getDateRange() {
    const fromDate = this.formatSearchDate(this.searchParams.fromDate);
    let toDate = this.formatSearchDate(this.searchParams.toDate);
    if (fromDate && !toDate) {
      toDate = this.formatSearchDate(this.calendarService.getToday());
    }
    return {
      DOCUMENT_DATE: fromDate,
      DOCUMENT_DATE_TO: toDate
    }
  }

  formatSearchDate(date: NgbDateStruct) {
    if (!date) return "";
    let newDate = new Date(date['year'], date['month'] - 1, date['day'])
    return moment(newDate).format("YYYYMMDD");
  }

  formatDate(input: string) {
    return moment(input, 'YYYYMMDD').format('MM/DD/YYYY');
  }

  today() {
    return this.calendarService.getToday();
  }

  downloadPDF(invoiceId: string) {
    this.loadingPdf = true;
    const url = `${ApiConstant.INVOICE}/${invoiceId}/pdf-form`;
    this.invoiceService.invoicePdf(url)
      .pipe(take(1))
      .subscribe((response) => {
        const downloadLink = document.createElement('a');
        //@ts-ignore
        downloadLink.href = URL.createObjectURL(new Blob([response.body], { type: response.body.type }));
        // downloadLink.download = 'invoice.pdf';
        downloadLink.target = '_blank';
        downloadLink.click();
        this.loadingPdf = false;
      });
  }
}
