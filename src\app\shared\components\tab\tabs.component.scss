.tabs {
    margin: 10px auto;
    background: var(--snjy-color-white);
    border-radius: 4px;

    ul.tab-group {
        margin: 0 0 20px 0;
        padding: 3px 8px 8px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 0 10px;
        background: #e6e6e6;
        border-radius: 6px;

        li {
            display: inline-block;
            margin: 0 0 -1px;
            padding: 15px 25px;
            text-align: center;
            color: #555;
            border: 1px solid transparent;
            cursor: pointer;
            margin: 5px 0 0 0 !important;
            padding: 0 13px 0 32px !important;
            min-width: fit-content;
            font-size: var(--snjy-font-size-0-8);
            height: 33px;
            font-weight: var(--snjy-font-weight-medium);
            display: inline-flex !important;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            background: var(--snjy-font-color-primary);
            color: #2c3643 !important;
            box-shadow: 0 2px 2px 1px rgba(0, 0, 0, 0.0784313725);
            position: relative;
            border: none !important;

            &.active {
                border: 1px solid #ddd;
                border-top: 2px solid #285292;
                border-bottom: 1px solid var(--snjy-color-white);
            }

            &::before {
                position: absolute;
                content: '';
                font-family: "Material Icons Outlined";
                top: 0px;
                left: 8px;
                bottom: 0;
                margin: auto 0;
                font-size: 19px;
                color: #2c3643;
                font-weight: var(--snjy-font-weight-normal);
                line-height: 20px;
                height: fit-content;
            }

            &:first-child::before {
                content: '\e88e';
            }

            &:nth-child(2)::before {
                content: '\e16d';
            }

            &:nth-child(3)::before {
                content: '\f04a';
            }

            &:nth-child(4)::before {
                content: '\e8ef';
            }

            &:nth-child(5)::before {
                content: '\e02c';
            }

            &:nth-child(6)::before {
                content: '\e97a';
            }

            &:nth-child(7)::before {
                content: '\ea99';
            }

            &:nth-child(8)::before {
                content: '\e97f';
            }

            &.active {
                background: #0b1c33 !important;
                color: var(--snjy-color-white) !important;

                &::before {
                    color: var(--snjy-color-white);
                }
            }
        }
    }

    .tab-content {
        padding: 20px;
        border: 1px solid #ddd;
        line-height: 1.6rem;
        background: #f4f5f7;
        border: none !important;
        border-radius: 10px;
        padding: 5px;
    }
}