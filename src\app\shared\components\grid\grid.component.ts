import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CustomHeader } from '../grid-header/grid-header.component';
import { ColDef, RowHeightParams } from 'ag-grid-community';

@Component({
  selector: 'app-grid',
  templateUrl: './grid.component.html',
  styleUrls: ['./grid.component.scss']
})
export class GridComponent {
  @Input() columns: any = [];
  @Input() data: any = [];
  @Input() showExport: any = false;
  @Output() rowClick = new EventEmitter<any>();
  @Output() exportClick = new EventEmitter<any>();
  public rowClass = 'ag-row';
  public components: {
    [p: string]: any;
  } = {
      agColumnHeader: CustomHeader
    };

  private gridApi: any;
  public defaultColDef: ColDef = {
    flex: 1,
    minWidth: 150,
  };

  getRowHeight(params: RowHeightParams): number | undefined | null {
    return 62;
  }

  onGridReady(params: any): void {
    this.gridApi = params.api
  }

  onSelectionChanged() {
    const rowData = this.gridApi.getSelectedRows();
    this.rowClick.emit(rowData);
  }

  export() {
    this.exportClick.emit();
  }
}
