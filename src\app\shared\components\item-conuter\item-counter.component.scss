.counter-container {

    button:disabled .material-icons-outlined {
        opacity: .7;
    }

    .cart-quantity {
        background: var(--snjy-font-color-primary);
        border: 1px solid var(--snjy-border-color);
        box-shadow: 0px 3px 3px rgba(0, 0, 0, 0.03);
        border-radius: 7px;
        margin-right: 12px;

        .material-icons-outlined {
            color: var(--snjy-color-primary);
            font-weight: var(--snjy-font-weight-bold);
            cursor: pointer;
        }

        .qty-to-add {
            font-family: var(--snjy-font-family-ternary);
            font-size: var(--snjy-font-size-1);
            color: var(--snjy-font-color-seconday);
        }
    }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type=number] {
    border: none;
    text-align: center;
    -moz-appearance: textfield;
}