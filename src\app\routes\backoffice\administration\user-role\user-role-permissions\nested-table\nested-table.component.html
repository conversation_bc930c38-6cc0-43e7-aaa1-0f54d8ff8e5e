<table class="table table-bordered table-sm">
    <thead>
        <tr>
            <th width="30"></th>
            <th>Code</th>
            <th>Name</th>
            <th>Enabled</th>
        </tr>
    </thead>
    <tbody>
        <ng-container *ngFor="let item of data">
            <tr>
                <td width="30">
                    <a href="javascript:void(0);" class="text-primary d-flex" (click)="onToggle(item)"
                        *ngIf="item?.child?.length">
                        <i class="material-icons-outlined" *ngIf="!item.toggle">add</i>
                        <i class="material-icons-outlined" *ngIf="item.toggle">remove</i>
                    </a>
                </td>
                <td>{{ item.code }}</td>
                <td>{{ item.name }}</td>
                <td>
                    <input type="checkbox" class="m-1 shadow-none checkbox-input" [(ngModel)]="item.is_enabled"
                        (change)="onCheckboxChange(item)" />
                </td>
            </tr>
            <tr *ngIf="item?.child?.length && item.toggle">
                <td width="30"></td>
                <td colspan="4">
                    <app-nested-table [data]="item.child" (toggle)="onToggle($event)"
                        (checkboxChange)="onCheckboxChange($event)"></app-nested-table>
                </td>
            </tr>
        </ng-container>
    </tbody>
</table>