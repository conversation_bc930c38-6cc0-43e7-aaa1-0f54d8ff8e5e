import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { Customer } from "../../models/customer.model";
import { CustomerService } from "../customer.service";
import { Observable } from "rxjs";
import { AppToastService } from "src/app/shared/services/toast.service";
import { Output, EventEmitter } from "@angular/core";

@Component({
  selector: "app-customer-details",
  templateUrl: "./customer-details.component.html",
  styleUrls: ["./customer-details.component.scss"],
})
export class CustomerDetailsComponent implements OnInit, OnChanges {
  @Input() model: Customer = {};
  @Output() onUpdate = new EventEmitter<any>();
  form = new FormGroup({});
  cardOpen = true;
  submitted = false;
  refreshing = false;

  companies$!: Observable<any>;
  partnerFunction$!: Observable<any>;
  salesAreas$!: Observable<any>;
  texts$!: Observable<any>;

  // general form
  generalForm = this.fb.group({
    bp_id: [""],
    email: [
      "",
      [
        Validators.required,
        Validators.pattern(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/),
      ],
    ],
    phone: [
      "",
      [
        Validators.required,
        Validators.pattern(
          /^\+(9[976]\d|8[987530]\d|6[987]\d|5[90]\d|42\d|3[875]\d|2[98654321]\d|9[8543210]|8[6421]|6[6543210]|5[87654321]|4[*********]|3[9643210]|2[70]|7|1)\W*\d\W*\d\W*\d\W*\d\W*\d\W*\d\W*\d\W*\d\W*(\d{1,2})$/
        ),
      ],
    ],
    company: [""],
    bp_category: [""],
    bp_full_name: [""],
    bp_grouping: [""],
    bp_type: [""],
    bp_uuid: [""],
    org_bp_name1: [""],
    org_bp_name2: [""],
    org_bp_name3: [""],
    org_bp_name4: [""],
  });

  companiesForm = this.fb.group({
    bp_id: [""],
  });

  constructor(
    public fb: FormBuilder,
    private _snackBar: AppToastService,
    private translate: TranslateService,
    private customerService: CustomerService
  ) { }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges) {
    this.updateData();
  }

  updateData() {
    if (this.model.bp_id) {
      this.generalForm.patchValue(this.model);
      this.generalForm.disable();
      this.companies$ = this.customerService.getCompanies(this.model.bp_id);
      this.partnerFunction$ = this.customerService.getPartnerFunction(this.model.bp_id);
      this.salesAreas$ = this.customerService.getSalesAreas(this.model.bp_id);
      this.texts$ = this.customerService.getTexts(this.model.bp_id);
    }
  }

  get f() {
    return this.generalForm.controls;
  }

  submitForm() {
    this.submitted = true;
    if (this.generalForm.valid) {
      this._snackBar.open(this.translate.instant("form.submit.success"));
    }
  }

  toogleDetails() {
    this.cardOpen = !this.cardOpen;
  }

  toggleAccordian(data: any, event: any, index: number) {
    const element = event.target;
    element.classList.toggle("active");
    if (data[index].isActive) {
      data[index].isActive = false;
    } else {
      data[index].isActive = true;
    }

    const panel = element.parentElement.nextElementSibling;

    if (panel.style.maxHeight) {
      panel.style.maxHeight = null;
    } else {
      panel.style.maxHeight = panel.scrollHeight + "px";
    }
  }

  refresh() {
    if (!this.model?.bp_id) {
      return;
    }
    this.refreshing = true;
    this.customerService.getCustomerByID(this.model.bp_id).subscribe({
      next: (value) => {
        this.refreshing = false;
        this.model = value.data;
        this.onUpdate.emit(this.model);
        this.updateData();
      },
      error: (err) => {
        this.refreshing = false;
      },
    })
  }
}
