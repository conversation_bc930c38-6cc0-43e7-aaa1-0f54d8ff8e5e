<div class="items-container">
    <div class="d-flex align-items-center"><span class="title">Invoice Ref. No. 1666252086070</span></div>
    <div class="d-flex justify-content-center flex-wrap details-container mt-5 mb-4 flex-column p-4">
        <p class="title">Invoice Date</p>
        <p class="detail">11-5-2023</p>
        <p class="title">Customer Purchase Order</p>
        <p class="detail">123456458-8</p>
    </div>
    <div class="d-flex flex-wrap details-container mb-4">
        <div class="shipping-address-container m-4 border-end pe-4">
            <p class="mb-3">Seller</p>
            <p> Mr. <PERSON> Stoffel 7327 East Myrtle Lane Gloucester, MA 01930 </p>
        </div>
        <div class="shipping-address-container p-4">
            <p class="mb-3">Shipping Method</p>
            <p> Standard </p>
        </div>
    </div>
    <p class="items-to-be-shipped">Items to be shipped</p>
    <div class="items d-flex gap-2 flex-wrap justify-content-center" *ngFor="let product of products">
        <img src="/assets/images/demo-product.png" />
        <div class="d-flex justify-content-between flex-grow-1 flex-wrap gap-2 item-detail">
            <div class="desc d-flex flex-column">
                <p>Lorem ipsum dolor sit amet Lorem</p>
                <p>Lorem ipsum dolor sit amet Lorem</p>
                <p class="flex-grow-1"></p>
                <p class="quantity">Quantity: 1</p>
            </div>
            <div class="cost">
                <p>{{ 96 | currency}}</p>
                <p>({{ 96 | currency }} each)</p>
            </div>
        </div>
    </div>
</div>
<div class="checkout-container">
    <div class="order-summary my-3">
        <div class="p-2 p-md-3">
            <p class="title mb-3">Order Summary</p>
            <div class="d-flex justify-content-between details">
                <span>Subtotal(2)</span>
                <span>{{ 277.27 | currency }}</span>
            </div>
            <div class="d-flex justify-content-between details">
                <span>Shipping</span>
                <span>To be calculated</span>
            </div>
            <div class="d-flex justify-content-between details">
                <span>Sales Tax</span>
                <span>{{ 0 | currency }}</span>
            </div>
        </div>
        <div class="p-2 p-md-3">
            <div class="d-flex justify-content-between mb-3">
                <div>
                    <p class="total">TOTAL</p>
                    <p class="total-note">*No taxes are included in the total</p>
                </div>
                <span class="total-value">{{ 277.27 | currency }}</span>
            </div>
        </div>
    </div>
</div>