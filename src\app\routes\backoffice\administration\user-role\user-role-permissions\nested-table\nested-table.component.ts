import { Component, Input, Output, EventEmitter } from "@angular/core";

@Component({
  selector: "app-nested-table",
  templateUrl: "./nested-table.component.html",
  styleUrls: ["./nested-table.component.scss"],
})
export class NestedTableComponent {
  @Input() data: any[] = [];
  @Output() toggle: EventEmitter<any> = new EventEmitter<any>();
  @Output() checkboxChange: EventEmitter<any> = new EventEmitter<any>();

  // Function to handle toggling a nested table
  onToggle(item: any) {
    this.toggle.emit(item);
  }

  // Function to handle checkbox change
  onCheckboxChange(item: any) {
    this.updateParentCheckboxe(item);
    this.checkboxChange.emit(item);
  }

  updateParentCheckboxe(item: any): void {
    if (item.parent_permission_id !== null) {
      const parentItem = this.data.find(
        (parent: any) => parent.id === item.parent_permission_id
      );
      if (parentItem) {
        parentItem.is_enabled = parentItem.child.some(
          (childItem: any) => childItem.is_enabled
        );
        this.updateParentCheckboxe(parentItem);
      }
    }
  }
}
