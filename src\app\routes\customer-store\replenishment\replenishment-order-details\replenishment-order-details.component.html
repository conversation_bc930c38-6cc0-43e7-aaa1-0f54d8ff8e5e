<ng-container *ngIf="!loading">
  <div class="all-main-title-sec product-details-main-title">
    <h1>Scheduled Order: {{orderDetails?.id}}</h1>
    <div class="all-bedcrumbs">
      <ul>
        <li>
          <a [routerLink]="['/store/dashboard']">
            <span class="material-icons-outlined">home</span> Home
          </a>
        </li>
        <li>
          <a [routerLink]="['/store/replenishment']">
            <span class="material-icons-outlined">list_alt</span> Scheduled Orders
          </a>
        </li>
        <li>Scheduled Order Details</li>
      </ul>
    </div>
  </div>
  <div class="order-id-sec">
    <div class="order-id-body all-details-page">
      <div class="order-id-info">
        <div class="order-details">
          <h3>Order Details</h3>
          <ul>
            <li><span class="material-icons-outlined">pin</span> Order # <span>{{orderDetails?.id}}</span>
            </li>
            <li><span class="material-icons-outlined">pin</span> Customer #
              <span>{{orderDetails?.customer_id}}</span>
            </li>
            <li><span class="material-icons-outlined">person_outline</span> Customer Name
              <span>{{customer?.name}}</span>
            </li>
            <li><span class="material-icons-outlined">event_note</span> Start Date
              <span>{{orderDetails?.start_date}}</span>
            </li>
            <li><span class="material-icons-outlined">event_note</span> End Date
              <span>{{orderDetails?.end_date}}</span>
            </li>
            <li><span class="material-icons-outlined">event_note</span> Next Order Date
              <span>{{orderDetails?.next_order_date}}</span>
            </li>
            <li><span class="material-icons-outlined">event_repeat</span> Frequency
              <span>{{orderDetails?.frequency_format}}</span>
            </li>
            <li><span class="material-icons-outlined">person_outline</span> Created By
              <span>{{orderDetails?.created_by}}</span>
            </li>
          </ul>
        </div>
        <div class="order-details">
          <h3>Shipping Details</h3>
          <ul>
            <li><span class="material-icons-outlined">pin</span> Business Partner #
              <span>{{shipToParty?.bp_customer_number}}</span>
            </li>
            <li><span class="material-icons-outlined">person_outline</span> Name <span>{{shipToParty?.name}}</span></li>
            <li><span class="material-icons-outlined">home</span> Address <span>{{shipToParty?.address}}</span></li>
          </ul>
        </div>
        <div class="order-details">
          <h3>Items to be shipped</h3>
          <div class="item-box" *ngFor="let product of order?.to_Item?.A_SalesOrderItemSimulationType">
            <div class="item-box-img"
              [ngStyle]="{'background-image': 'url('+ (product.Material | getProductImage | async)+ ')'}">
            </div>
            <div class="item-box-content">
              <h4>{{ product?.SalesOrderItemText }}</h4>
              <small>{{ product?.Material }}</small>
              <div class="item-box-bottom-content">
                <div class="quantity">Quantity: <span>{{product.RequestedQuantity}}</span></div>
                <div class="item-price">
                  {{ product?.formatted_base_price }}
                  <span>{{ product?.formatted_base_price_each }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="order-id-summary">
        <div class="d-flex flex-wrap justify-content-between align-items-center">
          <h3>Order Summary</h3>
          <small><strong>Status:</strong> <span class="material-icons-outlined">motion_photos_on</span>
            {{orderDetails?.status || ''}}</small>
        </div>
        <div class="order-summary-price">
          <ul>
            <li>
              Subtotal <span>{{ order?.formatted_sub_total }}</span>
            </li>
            <li>
              Shipping<span>{{ order?.formatted_shipping }}</span>
            </li>
            <li>
              Tax <span>{{ order?.formatted_sales_tax }}</span>
            </li>
          </ul>
          <ul>
            <li class="total-price">
              Total <span>{{ order?.formatted_total }}</span>
            </li>
          </ul>
          <p class="terms">By placing the order, you agree to our <a [href]="settings?.terms_url" target="_blank">Terms
              & Conditions</a> and our <a [href]="settings?.privacy_policy_url" target="_blank">Privacy Policy</a>.</p>
        </div>
      </div>
    </div>
  </div>
</ng-container>
<app-loader *ngIf="loading"></app-loader>