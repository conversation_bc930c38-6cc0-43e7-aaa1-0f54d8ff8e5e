<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">{{data ? 'Edit' : 'Add'}} Banner</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form">
        <div class="form-group mb-3 required">
            <label class="input-label">Type</label>
            <select formControlName="type" class="form-select mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['type'].errors }">
                <option value="IMAGE">Image</option>
                <option value="VIDEO">Video</option>
            </select>
            <div *ngIf="submitted && f['type'].errors" class="invalid-feedback">
                <div *ngIf="submitted && f['type'].errors && f['type'].errors['required']">
                    Type is required
                </div>
            </div>
        </div>

        <div class="form-group mb-3 required">
            <label class="input-label">Name</label>
            <input type="text" formControlName="name" class="form-control mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['name'].errors }" />
            <div *ngIf="submitted && f['name'].errors" class="invalid-feedback">
                <div *ngIf="submitted && f['name'].errors && f['name'].errors['required']">
                    Name is required
                </div>
            </div>
        </div>

        <div class="form-group mb-3 required">
            <label class="input-label">Sequence</label>
            <input type="number" formControlName="sequence" class="form-control mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['sequence'].errors }" min="1" max="10" />
            <div *ngIf="submitted && f['sequence'].errors" class="invalid-feedback">
                <div *ngIf="f['sequence'].errors['required']">Sequence is required</div>
                <div *ngIf="f['sequence'].errors['min'] || f['sequence'].errors['max']">
                    Sequence must be between 1 to 10
                </div>
            </div>
        </div>

        <div class="form-group mb-3 required">
            <label class="input-label">URL</label>
            <input type="text" formControlName="url" class="form-control mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['url'].errors }" />
            <div *ngIf="submitted && f['url'].errors" class="invalid-feedback">
                <div *ngIf="f['url'].errors['required']">URL is required</div>
            </div>
        </div>

        <div class="form-group mb-3">
            <label class="input-label">Caption</label>
            <angular-editor formControlName="caption" [config]="editorConfig"></angular-editor>
        </div>

        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="is_published" formControlName="is_published">
            <label class="form-check-label" for="is_published">Has it been made public?</label>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-light me-2" (click)="activeModal.dismiss()">Cancel</button>
    <button class="btn btn-primary" (click)="onSubmit()">{{data ? 'Save' : 'Add'}}</button>
</div>