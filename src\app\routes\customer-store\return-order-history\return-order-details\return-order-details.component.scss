:host {
  padding: 25px;
  overflow: auto;
}

.return-id-sec {
  margin: 0;
  padding: 0;
  position: relative;

  .return-id-body {
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
    gap: 0 3%;

    .return-id-info {
      margin: 0;
      padding: 0;
      flex: 4;

      .order-details {
        margin: 0 0 20px 0;
        padding: 24px;
        background: var(--snjy-color-white);
        border: 1px solid var(--snjy-border-color-secondary);
        border-radius: 10px;
        box-shadow: var(--snjy-box-shadow);

        h3 {
          margin: 0 0 15px 0;
          padding: 0;
          position: relative;
          font-size: var(--snjy-font-size-1-125);
          font-weight: var(--snjy-font-weight-bold);
          color: #00216c;
          line-height: 20px;
        }

        ul {
          padding: 34px 30px;
          position: relative;
          list-style: none;
          background: #f0f5f6;
          border-radius: 8px;
          display: flex;
          justify-content: flex-start;
          gap: 40px 4%;
          flex-wrap: wrap;

          li {
            margin: 0;
            padding: 0;
            color: #687491;
            font-weight: var(--snjy-font-weight-medium);
            font-size: var(--snjy-font-size-0-8);
            flex: 0 0 30%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 0 3px;
            line-height: 22px;

            .material-icons-outlined {
              margin: 0;
              padding: 0;
              width: fit-content;
              height: fit-content;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              font-size: var(--snjy-font-size-1-25);
              color: #687491;
            }

            span:not(.material-icons-outlined) {
              margin: 0;
              padding: 0 0 0 22px;
              display: block;
              color: #0077d7;
              font-weight: var(--snjy-font-weight-medium);
              font-size: var(--snjy-font-size-0-8);
              width: 100%;
            }
          }
        }

        .item-box {
          margin: 0 0 18px 0;
          padding: 16px;
          background: #eff4f5;
          display: flex;
          justify-content: flex-start;
          gap: 0 3%;
          position: relative;
          border-radius: 7px;

          .item-box-img {
            margin: 0;
            padding: 0;
            position: relative;
            flex: 0 0 26%;
            height: 144px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 7px;
            overflow: hidden;
            border: 1px solid rgba(27, 125, 203, 0.2901960784);
          }

          .item-box-content {
            margin: 0;
            padding: 0;
            position: relative;
            width: 100%;

            h4 {
              margin: 15px 0 0 0;
              padding: 0;
              position: relative;
              font-size: var(--snjy-font-size-1);
              font-weight: var(--snjy-font-weight-medium);
              color: var(--snjy-color-dark-secondary);
              line-height: 20px;
              display: block;
            }

            small {
              margin: 0;
              padding: 0;
              font-size: var(--snjy-font-size-0-75);
              color: #687491;
              font-weight: var(--snjy-font-weight-medium);
            }

            .item-box-bottom-content {
              margin: 35px 0 0 0;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: space-between;

              .quantity {
                margin: 0;
                position: relative;
                background: var(--snjy-color-white);
                height: 38px;
                display: inline-flex;
                align-items: center;
                justify-content: space-between;
                border-radius: 5px;
                border: 1px solid #ffd383;
                font-size: var(--snjy-font-size-0-8);
                font-weight: var(--snjy-font-weight-medium);
                overflow: hidden;

                .req-qty {
                  background: white !important;
                }

                span {
                  margin: 0;
                  padding: 0;
                  height: 38px;
                  background: #ffd383;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                  color: var(--snjy-color-dark-secondary);
                }
              }

              
            }
          }
        }
      }
    }
  }
}


$color_1: var(--snjy-color-dark-secondary);
$color_2: var(--snjy-color-white);

.return-reason {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  position: relative;

  .return-r-dropdown {
    margin: 0;
    padding: 0;
    max-width: 350px;
    width: 100%;

    label {
      margin: 0 0 5px 0;
      padding: 0;
      position: relative;
      font-size: var(--snjy-font-size-1);
      font-weight: var(--snjy-font-weight-medium);
      color: $color_1;
      line-height: 20px;
      display: block;
    }

    select.form-control {
      margin: 0;
      padding: 0 11px;
      height: 35px;
      background: #f0f7ff;
      border-radius: 10px;
      font-size: var(--snjy-font-size-0-875);
      font-weight: var(--snjy-font-weight-normal) !important;
      border: 1px solid rgba(134, 153, 169, 0.3803921569);
    }
  }
}

.initiate-return-list {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 15px;
  position: relative;
  flex-direction: row;

  label {
    display: flex;
    align-items: center;
    gap: 0 7px;

    .form-control {
      margin: auto 0;
      width: 18px;
      height: 18px;
      appearance: auto !important;
      outline: none;
      box-shadow: none;
    }

    span {
      margin: 0;
      padding: 0;
      font-size: var(--snjy-font-size-0-9);
      color: $color_1;
      font-weight: var(--snjy-font-weight-normal);
      line-height: 23px;
    }
  }
}

.item-price {
  margin: 0;
  padding: 0;
  font-size: var(--snjy-font-size-1-125);
  font-weight: var(--snjy-font-weight-bold);
  color: #0077d7;
  line-height: 22px;
  text-align: right;

  span {
    margin: 0;
    padding: 0;
    position: relative;
    font-size: var(--snjy-font-size-0-75);
    font-weight: var(--snjy-font-weight-medium);
    color: #687491;
    display: block;
  }
}