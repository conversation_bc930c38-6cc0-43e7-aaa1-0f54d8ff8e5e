import { Component } from "@angular/core";
import { AuthService } from "src/app/core/authentication/auth.service";

@Component({
  selector: "app-customer-services",
  templateUrl: "./customer-services.component.html",
  styleUrls: ["./customer-services.component.scss"],
})
export class CustomerServicesComponent {
  public customer_id: any = null;
  public settings: any = { company_name: "Customer Service Portal" };

  constructor(private authService: AuthService) {
    const auth = this.authService.getAuth();
    this.customer_id = auth?.partner_function?.customer_id || null;
  }
}
