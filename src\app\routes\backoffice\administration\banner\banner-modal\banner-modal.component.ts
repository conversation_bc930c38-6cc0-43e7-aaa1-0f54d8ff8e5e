import { Component, Input } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { AngularEditorConfig } from "@kolkov/angular-editor";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
  selector: "app-banner-modal",
  templateUrl: "./banner-modal.component.html",
  styleUrls: ["./banner-modal.component.scss"],
})
export class BannerModalComponent {
  @Input() data: any;
  @Input() placement: any;
  @Input() customer_group: any;
  @Input() customer_account_group: any;
  public form: FormGroup;
  public submitted = false;
  public editorConfig: AngularEditorConfig = {
    editable: true,
    spellcheck: true,
    height: "15rem",
    minHeight: "5rem",
    placeholder: "Enter text here...",
    translate: "no",
    defaultParagraphSeparator: "p",
    defaultFontName: "Inter",
    toolbarHiddenButtons: [["insertImage"], ["insertVideo"]],
    fonts: [{ class: "inter", name: "Inter" }],
    sanitize: false,
  };

  constructor(
    public activeModal: NgbActiveModal,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.createForm();
    this.updateForm();
  }

  createForm() {
    this.form = this.formBuilder.group({
      id: [null],
      placement: [this.placement],
      name: [null, Validators.required],
      type: ["IMAGE", Validators.required],
      url: [null, [Validators.required]],
      caption: [null],
      is_published: [false],
      sequence: [
        null,
        [Validators.required, Validators.min(1), Validators.max(10)],
      ],
      customer_group: [this.customer_group],
      customer_account_group: [this.customer_account_group],
    });
  }

  updateForm() {
    if (this.data) {
      this.form.patchValue(this.data);
    }
  }

  get f(): any {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    const value = this.form.value;
    this.activeModal.close(value);
  }
}
