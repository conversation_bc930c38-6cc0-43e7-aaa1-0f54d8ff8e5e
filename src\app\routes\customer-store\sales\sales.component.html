<section class="sales-sec">
    <app-banner-preview [customerID]="customer_id" [settings]="settings" [placement]="'SALES'"></app-banner-preview>
    <!-- <div class="sales-img">
        <video width="100%" height="100%" autoplay loop controls>
            <source src="/assets/images/sales.mp4" type="video/mp4">
        </video>
    </div> -->
    <div class="cus-services-body">
        <!-- <div class="cus-services-title">
            <h1>Welcome to <span>Sales Portal</span></h1>
        </div> -->
        <div class="cus-services-list">
            <a class="cus-services-box" href="javascript:void(0);">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">person</i>
                </div>
                <div class="cus-services-box-text">Account</div>
            </a>
            <a class="cus-services-box" href="javascript:void(0);">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">contacts</i>
                </div>
                <div class="cus-services-box-text">Contacts</div>
            </a>
            <a class="cus-services-box" href="javascript:void(0);">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">campaign</i>
                </div>
                <div class="cus-services-box-text">Activities</div>
            </a>
            <a class="cus-services-box" [routerLink]="['/store/catalogues']" *checkPermission="'P0041'">
                <div class="cus-services-box-icon">
                    <img src="/assets/images/catalogs.svg" />
                </div>
                <div class="cus-services-box-text">Product Catalog</div>
            </a>
            <ng-container *checkAppFeature="'F0006'">
                <a class="cus-services-box" [routerLink]="['/store/order-history']" *checkPermission="'P0043'">
                    <div class="cus-services-box-icon">
                        <img src="/assets/images/order-status.svg" />
                    </div>
                    <div class="cus-services-box-text">Orders</div>
                </a>
            </ng-container>
            <ng-container *checkAppFeature="'F0002'">
                <a class="cus-services-box" [routerLink]="['/store/invoices']" *checkPermission="'P0045'">
                    <div class="cus-services-box-icon">
                        <img src="/assets/images/invoices.svg" />
                    </div>
                    <div class="cus-services-box-text">Invoices</div>
                </a>
            </ng-container>
            <ng-container *checkAppFeature="'F0004'">
                <a class="cus-services-box" [routerLink]="['/store/quote-history']" *checkPermission="'P0046'">
                    <div class="cus-services-box-icon">
                        <img src="/assets/images/quote-history.svg" />
                    </div>
                    <div class="cus-services-box-text">Quotes</div>
                </a>
            </ng-container>
            <a class="cus-services-box" href="javascript:void(0);">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">summarize</i>
                </div>
                <div class="cus-services-box-text">Reports</div>
            </a>
            <a class="cus-services-box" href="javascript:void(0);">
                <div class="cus-services-box-icon">
                    <i class="material-icons-outlined">leaderboard</i>
                </div>
                <div class="cus-services-box-text">Leads</div>
            </a>
        </div>
    </div>

</section>