<div class="d-flex flex-column-reverse flex-md-row p-3 sign_section gap-3 align-items-center">
  <div class="flex-grow-1 d-flex justify-content-center align-items-center">
    <div class="login_box p-4 flex-grow-1">
      <a class="navbar-brand" href="#"><img src="assets/images/logo.png"></a>
      <h1>Login<span> to Your Account</span></h1>
      <form class="form-field-full" [formGroup]="loginForm">
        <div class="form-group user-name">
          <label for="exampleInputEmail1">Email Address</label>
          <input type="email" class="form-control" id="username" formControlName="email"
            placeholder="Enter Email Address" />
        </div>
        <div class="form-group user-pass">
          <label for="exampleInputPassword1">Password</label>
          <input type="password" class="form-control" formControlName="password" placeholder="Enter Password" />
          <span class="form-text hint" role="button" (click)="forgotPassword()">Forgot Password?</span>
        </div>
        <div class="form-group form-check">
          <input type="checkbox" class="form-check" formControlName="rememberMe" />
          <label class="form-check-label remember" for="exampleCheck1">Remember Me</label>
        </div>
        <div class="button-section">
          <button type="submit" class="btn btn-light btn-login" [disabled]="!!loginForm.invalid || isSubmitting"
            (click)="login()">Login</button>
          <button type="submit" class="btn btn-primary" (click)="reset()">Cancel</button>
        </div>
        <p class="text-danger" *ngIf="errMsg">{{errMsg}}</p>
      </form>
      <a class="sso-btn w-100 d-flex justify-content-center align-items-center"
        [attr.href]="API_ENDPOINT + '/auth/signin'" role="button">
        Continue with Microsoft SSO
      </a>
    </div>
  </div>
  <div class="flex-grow-1 d-flex justify-content-center align-items-center img-container">
    <img class="img-fluid" [src]="'' | getCompanyLogo | async" alt="" title="" />
  </div>
</div>