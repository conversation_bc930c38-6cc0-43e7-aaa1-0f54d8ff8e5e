<section class="m-0 p-0 position-relative">
	<div class="ticket-status-body">
		<div class="all-main-title-sec">
			<h1>Check Registered Products</h1>
			<div class="all-bedcrumbs">
				<ul>
					<li>
						<a [routerLink]="['/store/dashboard']">
							<span class="material-icons-outlined">home</span> Home
						</a>
					</li>
					<li>
						<a [routerLink]="['/store/customer-services']">
							<span class="material-icons-outlined">support_agent</span> Customer Service
						</a>
					</li>
					<li>Check Registered Products</li>
				</ul>
			</div>
		</div>
		<div class="ticket-contact-list">
			<div class="ticket-c-box">
				<div class="ticket-c-icon"><img src="/assets/images/seller-icon.png" alt="" title="" /></div>
				<div class="ticket-c-details">
					<h4>Customer #</h4>
					<small>{{sellerDetails.bp_customer_number}}</small>
				</div>
			</div>
			<div class="ticket-c-box">
				<div class="ticket-c-icon"><img src="/assets/images/phone-icon.png" alt="" title="" /></div>
				<div class="ticket-c-details">
					<h4>Customer Name</h4>
					<small>{{sellerDetails.name}}</small>
				</div>
			</div>
			<div class="ticket-c-box address-box">
				<div class="ticket-c-icon"><img src="/assets/images/address-icon.png" alt="" title="" /></div>
				<div class="ticket-c-details">
					<h4>ADDRESS</h4>
					<small>{{sellerDetails.address}}</small>
				</div>
			</div>
		</div>
		<form class="ticket-status-form all-form-res" [formGroup]="filterForm">
			<div class="form">
				<div class="form-group o-num">
					<label>
						<span class="material-icons-outlined">subject</span>
						Product ID #
					</label>
					<input type="input" class="form-control" placeholder="Product ID #" formControlName="product_id" />
				</div>
				<div class="form-group o-num">
					<label>
						<span class="material-icons-outlined">subject</span>
						Serial #
					</label>
					<input type="input" class="form-control" placeholder="Order #" formControlName="serial" />
				</div>
				<div class="form-btn-sec d-flex justify-content-center gap-1">
					<button type="button" class="ticket-s-btn" (click)="clear()">
						Clear
					</button>
					<button type="button" class="ticket-s-btn" (click)="getRegisterProductList()" [disabled]="loading">
						{{ loading ? "Searching..." : "Search" }}
					</button>
				</div>
			</div>
		</form>
		<div class="ticket-s-table">
			<table class="table all-table">
				<thead>
					<tr>
						<td>
							<div class="ticket-s-table-box">
								<span class="material-icons-outlined">subject</span> Product
								Name
							</div>
						</td>
						<td>
							<div class="ticket-s-table-box">
								<span class="material-icons-outlined">subject</span> Serial #
							</div>
						</td>
						<td>
							<div class="ticket-s-table-box">
								<span class="material-icons-outlined">calendar_month</span>
								Purchase Date
							</div>
						</td>
						<td>
							<div class="ticket-s-table-box">
								<span class="material-icons-outlined">sell</span>
								Purchase Price
							</div>
						</td>
						<td>
							<div class="ticket-s-table-box">
								<span class="material-icons-outlined">download</span>
								Proof of Purchase
							</div>
						</td>
					</tr>
				</thead>
				<tbody>
					<tr *ngIf="!prPaginated.length || loading">
						<td colspan="6">
							{{ loading ? "Loading..." : "No records found." }}
						</td>
					</tr>
					<ng-container *ngIf="prPaginated.length && !loading">
						<tr *ngFor="let pr of prPaginated">
							<td data-label="Product Name">
								<p>{{ pr?.product_description }}</p>
								<small>SKU ID: {{ pr?.product_id }}</small>
							</td>
							<td data-label="Serial #">{{ pr?.serial }}</td>
							<td data-label="Purchase Date">{{ moment(pr?.purchase_date).format("MM/DD/YYYY") }}</td>
							<td data-label="Purchase Price">{{ pr?.purchase_price | currency }}</td>
							<td data-label="Proof of Purchase" class="text-danger">
								<i *ngIf="pr?.has_purchase_proof" class="material-icons-outlined"
									(click)="downloadPurchaseProof(pr.serial)">
									cloud_download
								</i>
							</td>
						</tr>
					</ng-container>
				</tbody>
			</table>
			<div class="pagination-container">
				<div class="pages">
					<span class="material-icons-outlined" [ngClass]="{ disable: pager.currentPage === 1 }"
						(click)="setPage(1, true)">arrow_backward</span>
					<span *ngFor="let page of pager.pages" [ngClass]="{ selected: pager.currentPage === page }"
						(click)="setPage(page, true)">{{ page }}</span>
					<span class="material-icons-outlined"
						[ngClass]="{ disable: pager.currentPage === pager.totalPages }"
						(click)="setPage(pager.totalPages, true)">arrow_forward</span>
				</div>
			</div>
		</div>
	</div>
</section>