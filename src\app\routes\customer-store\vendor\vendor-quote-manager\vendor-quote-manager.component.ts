import { Component } from "@angular/core";
import { NgbModal, NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";
import { ColDef } from "ag-grid-community";
import { PlaceVendorBidComponent } from "./place-vendor-bid/place-vendor-bid.component";

@Component({
  selector: "app-vendor-quote-manager",
  templateUrl: "./vendor-quote-manager.component.html",
  styleUrls: ["./vendor-quote-manager.component.scss"],
})
export class VendorQuoteManagerComponent {
  public isDialogOpen: any = false;
  private modalOption: NgbModalOptions = {};
  public quotedItems: any[] = [
    { id: 1, added_date: "11/01/2016", quoted_item_Number: "ABCD-1",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"2000",product:"XYZ-1",musa_requirements:"",territory:"USA"},
    { id: 2, added_date: "10/25/2016",quoted_item_Number: "ABCD-2",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"500",product:"XYZ-2",musa_requirements:"",territory:"USA"},
    { id: 3, added_date: "10/18/2016",quoted_item_Number: "ABCD-3",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"4000",product:"XYZ-3",musa_requirements:"",territory:"USA"},
    { id: 4, added_date: "10/11/2016",quoted_item_Number: "ABCD-4",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"30000",product:"XYZ-4",musa_requirements:"",territory:"USA"},
    { id: 5, added_date: "10/04/2016",quoted_item_Number: "ABCD-5",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"20000",product:"XYZ-5",musa_requirements:"",territory:"USA"},
    { id: 6, added_date: "10/03/2016", quoted_item_Number: "ABCD-6",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"40000",product:"XYZ-6",musa_requirements:"",territory:"USA"},
    { id: 7, added_date: "9/28/2016", quoted_item_Number: "ABCD-7",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"10000",product:"XYZ-7",musa_requirements:"",territory:"USA"},
    { id: 8, added_date: "9/27/2016", quoted_item_Number: "ABCD-8",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"9000",product:"XYZ-8",musa_requirements:"",territory:"USA"},
    { id: 9, added_date: "9/27/2016", quoted_item_Number: "ABCD-9",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"3000",product:"XYZ-9",musa_requirements:"",territory:"USA"},
    { id: 10, added_date: "9/27/2016", quoted_item_Number: "ABCD-10",requires_a56081:"false",requires_factory_packaging:"false",quantity_needed:"6000",product:"XYZ-10",musa_requirements:"",territory:"USA"},
  ];

  public columnDefs: ColDef[] = [
    {
      field: "id",
      headerName: "ID",
      sortable: false,
      filter: false,
      hide:true,
    },
    {
      field: "added_date",
      headerName: "Added Date",
      sortable: false,
      filter: false,
      maxWidth: 120
    },
    {
      field: "quoted_item_Number",
      headerName: "Quoted Item Number",
      sortable: false,
      filter: false,
      minWidth: 175
    },
    {
      field: "requires_a56081",
      headerName: "Requires A56081",
      sortable: false,
      filter: false,
    },
    {
      field: "requires_factory_packaging",
      headerName: "Requires: Factory Packaging",
      sortable:false,
      filter:false,
      minWidth: 225
    },
    {
      field: "quantity_needed",
      headerName: "Quantity Needed",
      sortable:false,
      filter:false,
    },
    {
      field: "product",
      headerName: "Product",
      sortable:false,
      filter:false,
      maxWidth: 110
    },
    {
      field: "musa_requirements",
      headerName: "MUSA Requirements",
      sortable:false,
      filter: false,
      minWidth: 175
    },
    {
      field: "territory",
      headerName: "Territory",
      sortable: false,
      filter: false,
      maxWidth: 100
    },
    {
      field: "bid",
      headerName: "Place Vendor Bid",
      cellRenderer: (data: any) => {
        return `<a class="d-flex align-items-center gap-1 grid-cell text-primary">Place Vendor Bid</a>`;
      },
      sortable: false,
      filter: false,
      resizable: false,
      onCellClicked: (data: any) => {
        this.openDialog();
      }
    },
  ];

  constructor(private dialog: NgbModal) {
  }

  openDialog() {
    if (!this.isDialogOpen) {
      this.isDialogOpen = true;
      this.modalOption.backdrop = "static";
      this.modalOption.keyboard = false;
      this.modalOption.size = "xl";
      const dialogRef = this.dialog.open(
        PlaceVendorBidComponent,
        this.modalOption
      );
      dialogRef.result
        .then(() => {
          this.isDialogOpen = false;
        })
        .catch(() => {
          this.isDialogOpen = false;
        });
    }
  }

}
