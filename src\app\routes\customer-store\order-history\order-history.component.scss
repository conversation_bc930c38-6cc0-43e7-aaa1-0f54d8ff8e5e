table {
  min-width: 700px;

  thead {
    background: var(--snjy-color-text-secondary);
    color: var(--snjy-color-main-background);
    font-family: var(--snjy-font-family);

    th {
      padding: 1rem 2rem;
    }
  }

  td {
    font-size: var(--snjy-font-size-0-875);
    padding: 2.5rem 2rem;
    color: var(--snjy-color-dark-secondary);
    font-family: var(--snjy-font-family);
    font-weight: var(--snjy-font-weight-medium);

    .circle {
      height: 12px;
      width: 12px;
      border-radius: 50%;
      display: inline-block;

      &.Cancelled {
        background-color: red;
      }

      &.Completed {
        background-color: green;
      }
    }
  }

  tbody tr {
    cursor: pointer;
  }
}

.order-status-sec {
  margin: 0;
  padding: 0;
  position: relative;

  .order-status-body {
    margin: 0 auto;
    padding: 0 25px 50px 25px;
    position: relative;
    max-width: 100%;

    .order-contact-list {
      margin: 0;
      padding: 18px 20px;
      background: #e5ecf3;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 12px;
      border: 1px solid rgba(208, 215, 216, 0.3882352941);
      box-shadow: var(--snjy-box-shadow);

      .order-c-box {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0 10px;
        flex: 1;

        &.address-box {
          flex: 0 0 42% !important;
        }

        .order-c-icon {
          margin: 0;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 44px;
          height: 44px;
          background: var(--snjy-font-color-primary);
          border-radius: 50px;
          box-shadow: 0 1px 3px rgba(97, 134, 177, 0.1882352941);
        }

        .order-c-details {
          margin: 0;
          padding: 6px 0 0 0;


          h4 {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-8);
            text-transform: uppercase;
            font-weight: var(--snjy-font-weight-bold);
            color: var(--snjy-button-color-primary);
            line-height: 13px;
          }

          small {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-0-8);
            text-transform: uppercase;
            font-weight: var(--snjy-font-weight-medium);
            color: #2e3237;
            line-height: 20px;
          }
        }
      }
    }
  }
}

.order-status-form {
  margin: 15px 0 0 0;
  padding: 30px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);

  .form {
    display: flex;
    justify-content: space-between;
    gap: 0 2%;
    flex-wrap: wrap;

    .form-group {
      -webkit-box-flex: 0;
      flex: 1;
      margin: 0 0 15px 0;
      position: relative;

      &:before {
        position: absolute;
        content: '';
        font-family: 'Material Icons Outlined';
        top: 39px;
        right: 10px;
        bottom: 0;
        margin: auto 0;
        font-size: var(--snjy-font-size-1-25);
        color: var(--snjy-color-dark-secondary);
        font-weight: var(--snjy-font-weight-normal);
        line-height: 23px;
      }

      label {
        margin: 0 0 8px 0;
        padding: 0;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        color: var(--snjy-color-dark-secondary);
        line-height: 14px;
        display: flex;
        align-items: center;
        gap: 0 2px;

        .material-icons-outlined {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-1-25);
          color: #b3b3b3;
          width: fit-content;
          height: fit-content;
        }

      }

      .form-control {
        margin: 0;
        padding: 0 11px;
        height: 44px;
        background: #f0f7ff;
        font-size: var(--snjy-font-size-0-875);
        font-weight: var(--snjy-font-weight-medium);
        border: 1px solid #8699a961;

        &::placeholder {
          color: #a1a1a1;
          font-weight: var(--snjy-font-weight-semi-bold);
        }
      }

      select.form-control {
        -webkit-appearance: none;
      }

      &:before {
        color: #b7b7b7;
      }

      .p-o-num:before {
        content: '\f0c5';
        top: 40px;
      }

      .o-num:before {
        content: '\f045';
        top: 40px;
      }
    }

    .form-btn-sec {
      margin: 15px 0 0 0;
      -webkit-box-flex: 0;
      flex: 0 0 100%;
      max-width: 100%;
      text-align: center;

      .order-s-btn {
        border-radius: 10px;
        display: flex;
        font-size: var(--snjy-font-size-0-875);
        height: 48px;
        align-items: center;
        justify-content: center;
        line-height: 14px;
        min-width: 270px;
        padding-bottom: 0;
        padding-top: 0;
        cursor: pointer;
        color: var(--snjy-color-white);
        background: var(--snjy-button-gradient);
        position: relative;
        text-transform: uppercase;
        font-weight: var(--snjy-font-weight-medium);
        transition: all0 0.3s ease-in-out;
        border: none;
      }
    }
  }
}

$color_1: #b7b7b7;
$color_2: var(--snjy-color-white);
$color_3: var(--snjy-font-color-primary);
$color_4: #b3b3b3;
$color_5: #2da066;
$color_6: #b52c2c;
$font-family_1: "Material Icons Outlined";

.order-s-table {
  margin: 15px 0 0 0;
  padding: 18px;
  background: var(--snjy-color-white);
  border-radius: 12px;
  border: 1px solid rgba(208, 215, 216, 0.3882352941);
  box-shadow: var(--snjy-box-shadow);

  table.table {
    border-collapse: separate;
    border-spacing: 0 10px;
    margin: 0;

    thead {
      td {
        padding: 16px;
        background: #0077d7;
        border: none;

        .order-s-table-box {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-875);
          font-weight: var(--snjy-font-weight-medium);
          color: $color_2;
          line-height: 12px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0 2px;

          .material-icons-outlined {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-1-125);
            color: $color_3;
            width: -moz-fit-content;
            width: fit-content;
            height: -moz-fit-content;
            height: fit-content;
          }
        }
      }
    }

    tbody {
      td {
        padding: 16px 10px 16px 37px;
        background: #f5f5f5;
        border: none;
        position: relative;

        &:before {
          position: absolute;
          content: "";
          font-family: $font-family_1;
          top: 0;
          left: 15px;
          bottom: 0;
          margin: auto 0;
          font-size: var(--snjy-font-size-1-25);
          color: $color_1;
          font-weight: var(--snjy-font-weight-normal);
          line-height: 20px;
          height: fit-content;
        }

        &:first-child {
          &::before {
            content: "\f045";
          }
        }

        &:nth-child(2) {
          &::before {
            content: "\f0c5";
          }
        }

        &:nth-child(3) {
          &::before {
            content: "\ebcc";
          }
        }

        &:nth-child(4) {
          &::before {
            content: "\ef42";
          }
        }

        .order-s-table-box {
          margin: 0;
          padding: 0;
          font-size: var(--snjy-font-size-0-875);
          font-weight: var(--snjy-font-weight-semi-bold);
          color: $color_2;
          line-height: 13px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0 2px;

          .material-icons-outlined {
            margin: 0;
            padding: 0;
            font-size: var(--snjy-font-size-1-25);
            color: $color_4;
            width: fit-content;
            height: fit-content;
          }

          .box-checked {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 2px;
            color: $color_5;
            font-weight: var(--snjy-font-weight-medium);

            .material-icons-outlined {
              color: $color_5;
            }
          }

          .box-unchecked {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0 2px;
            color: $color_6;
            font-weight: var(--snjy-font-weight-medium);

            .material-icons-outlined {
              color: $color_6;
            }
          }
        }
      }
    }

    tr {
      td {
        &:first-child {
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
        }

        &:last-child {
          border-top-right-radius: 8px;
          border-bottom-right-radius: 8px;
        }
      }
    }
  }
}

/*--------------ORDER STATUS SEC------------*/

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 15px 0 7px 0;

  .rpp-container {
    display: flex;
    align-items: center;

    .rpp {
      font-size: var(--snjy-font-size-0-75);
      color: var(--snjy-color-secondary);
      margin-right: 12px;
    }

    select {
      width: auto;
      font-size: var(--snjy-font-size-0-75);
      color: var(--snjy-color-dark-secondary);
    }
  }

  .pages {
    display: flex;
    align-items: center;
    gap: 0 8px;

    .material-icons-outlined {
      color: var(--snjy-color-dark-secondary);
      padding: 0 4px;
      font-size: var(--snjy-font-size-0-9);
      height: 28px !important;
      width: 28px !important;
      background: var(--snjy-font-color-primary);
      border: 1px solid #dfdfdf;
      border-radius: 4px;
      box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);
      position: relative;
      overflow: visible;
      text-align: center;
      line-height: 26px;
      cursor: pointer;
    }

    span:not(.material-icons-outlined) {
      font-size: var(--snjy-font-size-0-75);
      color: #AAAAAA;
      cursor: pointer;
      height: 28px !important;
      width: 28px !important;
      background: var(--snjy-font-color-primary);
      border: 1px solid #dfdfdf;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &.selected {
        color: var(--snjy-font-color-primary);
        background: var(--snjy-button-color-primary);
        border: 1px solid var(--snjy-button-color-primary);
      }
    }
  }
}

.form-group {
  min-width: 160px;
}

@media (min-width: 1024px) and (max-width: 1248px) {
  .form-group {
    min-width: 300px;
  }
}