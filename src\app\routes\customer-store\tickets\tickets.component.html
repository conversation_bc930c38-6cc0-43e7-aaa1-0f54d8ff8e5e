<section class="ticket-status-sec">
	<div class="ticket-status-body">
		<div class="all-main-title-sec">
			<h1>Ticket Status</h1>
			<div class="all-bedcrumbs">
				<ul>
					<li>
						<a [routerLink]="['/store/dashboard']">
							<span class="material-icons-outlined">home</span> Home
						</a>
					</li>
					<li>
						<a [routerLink]="['/store/customer-services']">
							<span class="material-icons-outlined">support_agent</span> Customer Service
						</a>
					</li>
					<li>Ticket Status</li>
				</ul>
			</div>
		</div>
		<div class="ticket-contact-list">
			<div class="ticket-c-box">
				<div class="ticket-c-icon"><img src="/assets/images/seller-icon.png" alt="" title="" /></div>
				<div class="ticket-c-details">
					<h4>Customer #</h4>
					<small>{{sellerDetails.bp_customer_number}}</small>
				</div>
			</div>
			<div class="ticket-c-box">
				<div class="ticket-c-icon"><img src="/assets/images/phone-icon.png" alt="" title="" /></div>
				<div class="ticket-c-details">
					<h4>Customer Name</h4>
					<small>{{sellerDetails.name}}</small>
				</div>
			</div>
			<div class="ticket-c-box address-box">
				<div class="ticket-c-icon"><img src="/assets/images/address-icon.png" alt="" title="" /></div>
				<div class="ticket-c-details">
					<h4>ADDRESS</h4>
					<small>{{sellerDetails.address}}</small>
				</div>
			</div>
		</div>
		<form class="ticket-status-form all-form-res" [formGroup]="filterForm">
			<div class="form">
				<div class="form-group">
					<label><span class="material-icons-outlined">calendar_month</span> Date From</label>
					<div class="input-group">
						<input class="form-control" name="picker1" formControlName="DATE_FROM" ngbDatepicker
							#d="ngbDatepicker" [maxDate]="today()" />
						<button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
							<i class="material-icons-outlined">
								calendar_month
							</i>
						</button>
					</div>
				</div>
				<div class="form-group">
					<label><span class="material-icons-outlined">calendar_month</span> Date To</label>
					<div class="input-group">
						<input class="form-control" name="picker2" formControlName="DATE_TO" ngbDatepicker
							#d1="ngbDatepicker" [minDate]="f.DATE_FROM.value" [maxDate]="today()" />
						<button class="btn btn-outline-secondary" (click)="d1.toggle()" type="button">
							<i class="material-icons-outlined">
								calendar_month
							</i>
						</button>
					</div>
				</div>
				<div class="form-group">
					<label><span class="material-icons-outlined">feed</span> Status</label>
					<select class="form-control select-arrow-down" formControlName="STATUS">
						<option *ngFor="let status of statuses" [value]="status.code">{{status.description}}</option>
					</select>
				</div>
				<div class="form-group o-num">
					<label><span class="material-icons-outlined">pin</span> Service Ticket #</label>
					<input type="input" class="form-control" placeholder="Service Ticket #"
						(keyup.enter)="getTicketHistory()" formControlName="TICKET">
				</div>
				<p class="text-danger search-error" *ngIf="submitted && filterForm.touched && filterForm.invalid">
					Please add a valid from and to date
				</p>
				<div class="form-btn-sec d-flex gap-1 justify-content-center">
					<button type="button" class="mx-4 ticket-s-btn" (click)="clear()">Clear</button>
					<button type="button" class="mx-4 ticket-s-btn" (click)="search()" [disabled]="loading">{{loading ?
						'Searching...' : 'Search'}}</button>
				</div>
			</div>
		</form>
		<div class="ticket-s-table">
			<app-grid [columns]="columnDefs" (rowClick)="goToTicket($event)" [data]="tickets" [showExport]="true"
				(exportClick)="exportToExcel()" *ngIf="!loading && tickets.length"></app-grid>
			<div class="w-100" *ngIf="loading || !tickets.length">{{ loading ? 'Loading...' : 'No records found.'
				}}
			</div>
		</div>
	</div>
</section>