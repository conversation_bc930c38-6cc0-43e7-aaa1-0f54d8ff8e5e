import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormGroup, Validators, FormBuilder, FormArray, FormControl } from '@angular/forms';
import { ConfigService } from '../config.service';
import moment from 'moment';
import { ApiConstant } from 'src/app/constants/api.constants';
import { AppToastService } from 'src/app/shared/services/toast.service';

@Component({
  selector: 'app-catalog',
  templateUrl: './catalog.component.html',
  styleUrls: ['./catalog.component.scss']
})
export class CatalogComponent {
  @Input() model: any = {};
  @Input() categories: any = [];
  @Output() dataUpdated = new EventEmitter<any>();
  form = new FormGroup({});
  cardOpen = true;
  submitted = false;
  aciveTab = "General";
  currentForm: any;
  moment = moment;

  generalForm = this.fb.group({
    id: [""],
    language: ["", Validators.required],
    name: ["", Validators.required],
    status: ["", Validators.required],
    web_store: [""],
    categories: []
  });

  categoriesForm = this.fb.group({
    id: [""],
    categories: [""],
    selectedCategories: ['']
  });

  formMap: Record<string, any> = {
    General: this.generalForm,
    Categories: this.categoriesForm
  };

  constructor(
    public fb: FormBuilder,
    private service: ConfigService,
    private _snackBar: AppToastService
  ) { }

  ngOnInit(): void {
    this.setActiveform('General');
  }

  ngOnChanges(changes: SimpleChanges) {
    this.generalForm.patchValue(this.model);
    this.generalForm.patchValue({categories: this.model?.categories?.map((c: any) => c.id)});
    this.categoriesForm.patchValue({
      id: this.model.id,
      categories: this.model?.categories?.map((c: any) => c.id)
    });
    this.submitted = false;
  }

  get f() {
    return this.currentForm.controls;
  }

  setActiveform(name: string) {
    this.aciveTab = name;
    this.formMap[name] && (this.currentForm = this.formMap[name]);
  }

  update(data: any) {
    const id = data.id;
    const newObj = {
      ...data
    };
    if (data.categories) {
      delete newObj.categories;
      newObj.product_categories_ids = data.categories.join(',');
    }
    delete newObj.id;
    this.service.update(ApiConstant.GET_CATALOGS, newObj, id).subscribe({
      complete: () => {
        this.submitted = false;
        this.dataUpdated.emit({
          url: ApiConstant.GET_CATALOGS,
          id
        });
        this._snackBar.open("Changes saved successfully!");
      },
      error: (err) => {
        this.submitted = false;
        this._snackBar.open("Error while processing your request.", { type: 'Error' });
      },
    });
  }

  submitForm() {
    this._markAsTouched(this.currentForm);
    if (this.currentForm.valid) {
      this.submitted = true;
      this.update(this.currentForm.value);
    }
  }

  private _markAsTouched(group: FormGroup | FormArray) {
    group.markAsTouched({ onlySelf: true });

    Object.keys(group.controls).map((field) => {
      const control = group.get(field);
      if (control instanceof FormControl) {
        control.markAsTouched({ onlySelf: true });
      } else if (control instanceof FormGroup) {
        this._markAsTouched(control);
      }
    });
  }

  toogleDetails() {
    this.cardOpen = !this.cardOpen;
  }

  getCategoryName(id: string) {
    const category = this.categories.find((category: any) => category.id === id)
    return category?.name || '';
  }

  removeCategory(id: string) {
    let pushedCategories = this.f.categories.value;
    pushedCategories.splice(pushedCategories.indexOf(id), 1);
    this.categoriesForm.patchValue({
      categories: pushedCategories
    });
  }

  addCategory() {
    let pushedCategories = this.f.categories.value;
    let selectedCategories = this.f.selectedCategories.value;
    if (selectedCategories.length) {
      for (let i = 0; i < selectedCategories.length; i++) {
        const element = selectedCategories[i];
        if (pushedCategories.indexOf(element) === -1) {
          pushedCategories.push(element);
        }
      }
    };
    this.categoriesForm.patchValue({
      categories: pushedCategories,
      selectedCategories: ''
    });
  }

}
