<section class="order-status-sec">
  <div class="order-status-body">
    <div class="all-main-title-sec">
      <h1>Check Warranty</h1>
      <p>Review The Warranty coverage For Your Product</p>
      <div class="all-bedcrumbs">
        <ul>
          <li>
            <a [routerLink]="['/store/dashboard']">
              <span class="material-icons-outlined">home</span> Home
            </a>
          </li>
          <li>
            <a [routerLink]="['/store/customer-services']">
              <span class="material-icons-outlined">support_agent</span>
              Customer Service
            </a>
          </li>
          <li>Check Warranty</li>
        </ul>
      </div>
    </div>
    <div class="order-contact-list">
      <div class="order-c-box">
        <div class="order-c-icon">
          <img src="/assets/images/seller-icon.png" alt="" title="" />
        </div>
        <div class="order-c-details">
          <h4>Customer #</h4>
          <small>{{ sellerDetails.bp_customer_number }}</small>
        </div>
      </div>
      <div class="order-c-box">
        <div class="order-c-icon">
          <img src="/assets/images/phone-icon.png" alt="" title="" />
        </div>
        <div class="order-c-details">
          <h4>Customer Name</h4>
          <small>{{ sellerDetails.name }}</small>
        </div>
      </div>
      <div class="order-c-box address-box">
        <div class="order-c-icon">
          <img src="/assets/images/address-icon.png" alt="" title="" />
        </div>
        <div class="order-c-details">
          <h4>ADDRESS</h4>
          <small>{{ sellerDetails.address }}</small>
        </div>
      </div>
    </div>
    <div class="order-status-form all-form-res">
      <h3 _ngcontent-ncw-c226="">Please Enter the Serial Number</h3>
      <form [formGroup]="filterForm">
        <div class="form warranty-form">
          <div class="form-group o-num">
            <label>
              <span class="material-icons-outlined">subject</span>
              Product ID #
            </label>
            <input type="input" class="form-control" placeholder="Product ID #" formControlName="product_id" />
          </div>
          <div class="form-group o-num">
            <label>
              <span class="material-icons-outlined">subject</span>
              Serial #
            </label>
            <input type="input" class="form-control" placeholder="Order #" formControlName="serial" />
          </div>
          <div class="form-btn-sec d-flex justify-content-center gap-1">
            <button type="button" class="order-s-btn" (click)="clear()">
              Clear
            </button>
            <button type="button" class="order-s-btn" (click)="getRegisterProductList()" [disabled]="loading">
              {{ loading ? "Searching..." : "Search" }}
            </button>
          </div>
        </div>
      </form>
    </div>
    <div class="row">
      <div class="col">
        <div class="display-warranty" *ngIf="registerProductList.length">
          <h3>
            <strong>Warranty Status: </strong>
            <span class="text-primary">{{ warrantyStatus?.status }}</span>
          </h3>
          <h5>
            <strong>Current Service & Support Status: </strong>
            <span class="text-primary">{{ warrantyStatus?.message }}</span>
          </h5>
          <app-warranty [warrantyPeriods]="registerProductList"></app-warranty>
        </div>
      </div>
    </div>
  </div>
</section>