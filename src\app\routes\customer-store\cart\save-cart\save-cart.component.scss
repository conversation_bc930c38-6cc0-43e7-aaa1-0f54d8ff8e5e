:host {
    min-width: 400px;
}

h3 {
    font-size: var(--snjy-font-size-1-25);
    font-weight: var(--snjy-font-weight-bold);
    color: var(--snjy-button-color-primary);
}

.note {
    font-size: var(--snjy-font-size-0-9);
    font-weight: var(--snjy-font-weight-semi-bold);
    color: var(--snjy-color-text-ternary);
}

button,
button:disabled,
button:hover {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 0.8rem;
    padding: 0;
    min-height: 50px;
    font-family: var(--snjy-font-family);
    background-color: var(--snjy-button-color-primary);
    color: var(--snjy-color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 5px;
    font-size: var(--snjy-font-size-0-875);
    line-height: 16px;
    font-weight: var(--snjy-font-weight-medium);
}

button:disabled {
    opacity: .8;
}

.btn-light:hover,
.btn-light {
    background-color: var(--snjy-font-color-primary);
    color: var(--snjy-button-color-primary);
    border: 1px solid var(--snjy-button-color-primary);
}