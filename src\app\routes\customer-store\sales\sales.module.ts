import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { SalesComponent } from "./sales.component";
import { SharedModule } from "src/app/shared/shared.module";

@NgModule({
  declarations: [SalesComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: "", component: SalesComponent }]),
  ],
})
export class SalesModule {}
