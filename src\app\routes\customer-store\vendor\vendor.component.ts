import { Component } from '@angular/core';
import { AuthService } from 'src/app/core/authentication/auth.service';

@Component({
  selector: 'app-vendor',
  templateUrl: './vendor.component.html',
  styleUrls: ['./vendor.component.scss']
})
export class VendorComponent {
  public customer_id: any = null;
  public settings: any = { company_name: "Vendor Portal" };

  constructor(private authService: AuthService) {
    const auth = this.authService.getAuth();
    this.customer_id = auth?.partner_function?.customer_id || null;
  }
}
