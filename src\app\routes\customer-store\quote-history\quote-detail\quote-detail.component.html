<ng-container *ngIf="!loading">
  <div class="all-main-title-sec product-details-main-title">
    <h1>Quote ID: {{ quote?.QUOTE_HDR?.DOC_NUMBER }}</h1>
    <div class="all-bedcrumbs">
      <ul>
        <li>
          <a [routerLink]="['/store/dashboard']">
            <span class="material-icons-outlined">home</span> Home
          </a>
        </li>
        <li>
          <a [routerLink]="['/store/quote-history']">
            <span class="material-icons-outlined">list_alt</span> Quotes
          </a>
        </li>
        <li>Quote Details</li>
      </ul>
    </div>
  </div>
  <div class="q-order-buttons-container">
    <button type="button" class="q-order-btn">
      <span class="material-icons-outlined">edit</span> Edit Quote
    </button>
  </div>
  <div class="quote-id-sec">
    <div class="quote-id-body all-details-page">
      <div class="quote-id-info">
        <div class="order-details">
          <h3>Quote Details</h3>
          <ul>
            <li>
              <span class="material-icons-outlined">pin</span> Quote #
              <span>{{ quote?.QUOTE_HDR?.DOC_NUMBER }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">pin</span> Customer
              # <span>{{ sellerDetails?.bp_customer_number }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">
                person_outline
              </span>
              Customer Name <span>{{ sellerDetails?.name }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">person</span> Name
              <span>{{ quote?.QUOTE_HDR?.DOC_NAME }}</span>
            </li>
            <li>
              <span class="material-icons-outlined">event_note</span>
              Date Placed
              <span>{{
                moment(quote?.QUOTE_HDR?.DOC_DATE).format("MM/DD/YYYY")
                }}</span>
            </li>
          </ul>
        </div>
        <div class="order-details">
          <h3>Quote Description</h3>
          <ul>
            <li class="flex-fill">
              <span class="material-icons-outlined">description</span>
              Description
              <span *ngIf="quote?.QUOTE_HDR_TEXT?.length">
                {{ quote?.QUOTE_HDR_TEXT[0]?.TEXT }}
              </span>
            </li>
          </ul>
        </div>
        <div class="order-details">
          <h3>Items</h3>
          <div class="item-box" *ngFor="let item of quote.QUOTE_LINE_DETAIL">
            <div class="item-box-img" [ngStyle]="{
                'background-image':
                  'url(' + (item.MATERIAL | getProductImage | async) + ')'
              }"></div>
            <div class="item-box-content">
              <h4>{{ item.SHORT_TEXT }}</h4>
              <small>{{ item.MATERIAL }}</small>
              <div class="item-box-bottom-content">
                <div class="quantity">
                  Quantity: <span>{{ item.REQ_QTY }}</span>
                </div>
                <div class="item-price">
                  {{ item?.formatted_base_price }}
                  <span>
                    {{ item?.formatted_base_price_each }}
                    each
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="quote-id-summary">
        <h3>Quote Summary</h3>
        <small>
          <strong>Status:</strong>
          <span class="material-icons-outlined">motion_photos_on</span>
          {{ getStatusName(quote?.QUOTE_HDR?.DOC_STAT) }}
        </small>
        <div class="order-summary-price">
          <ul>
            <li>
              Subtotal <span>{{ quote?.formatted_sub_total }}</span>
            </li>
          </ul>
          <ul>
            <li class="total-price">
              Total <span>{{ quote?.formatted_sub_total }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</ng-container>
<app-loader *ngIf="loading"></app-loader>