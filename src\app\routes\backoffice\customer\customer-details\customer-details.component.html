<div class="mt-2">
  <div class="d-flex justify-content-between">
    <span (click)="toogleDetails()" class="expand-title d-flex align-items-center">
      <i class="material-icons-outlined">{{cardOpen ? 'expand_more': 'chevron_right'}}</i>&nbsp;{{
      'form.details' | translate }}
    </span>
    <button class="btn btn-primary" (click)="refresh()" [disabled]="refreshing">
      <span class="material-icons-outlined">
        refresh
      </span>
    </button>
  </div>
  <div *ngIf="cardOpen">
    <asar-tabs>
      <asar-tab [tabTitle]="'General'">
        <form [formGroup]="generalForm">
          <div class="row">
            <div class="col-md-4 col-12 form-group">
              <label> {{ 'backoffice.partner.partnerID' | translate }}</label>
              <input class="form-control" type="text" formControlName="bp_id" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.email' | translate }}</label>
              <input class="form-control" type="text" formControlName="email" />
              <span class="text-error" *ngIf="submitted && f.email.hasError('required')">
                {{ 'validation.email.required' | translate }}
              </span>
              <span class="text-error" *ngIf="submitted && f.email.hasError('pattern')">
                {{ 'validation.email.pattern' | translate }}
              </span>
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.phone' | translate }}</label>
              <input class="form-control" type="text" formControlName="phone" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.company' | translate }}</label>
              <input class="form-control" type="text" formControlName="company" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerCategory' | translate }}</label>
              <input class="form-control" type="text" formControlName="bp_category" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerFullName' | translate }}</label>
              <input class="form-control" type="text" formControlName="bp_full_name" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerGrouping' | translate }}</label>
              <input class="form-control" type="text" formControlName="bp_grouping" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerType' | translate }}</label>
              <input class="form-control" type="text" formControlName="bp_type" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerUUID' | translate }}</label>
              <input class="form-control" type="text" formControlName="bp_uuid" />
            </div>
            <div class="col-md-4 form-group">
              <label>{{ 'backoffice.partner.partnerName' | translate }} 1 </label>
              <input class="form-control" type="text" formControlName="org_bp_name1" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerName' | translate }} 2</label>
              <input class="form-control" type="text" formControlName="org_bp_name2" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerName' | translate }} 3</label>
              <input class="form-control" type="text" formControlName="org_bp_name3" />
            </div>
            <div class="col-md-4 col-12 form-group">
              <label>{{ 'backoffice.partner.partnerName' | translate }} 4</label>
              <input class="form-control" type="text" formControlName="org_bp_name4" />
            </div>
          </div>
        </form>
      </asar-tab>
      <asar-tab [tabTitle]="'Companies'">
        <div class="row">
          <div class="accordion accordion-flush" *ngIf="companies$ | async as companies">
            <div class="accordion-item" *ngFor="let company of companies;let i = index;">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed" (click)="toggleAccordian(companies,$event, i)" type="button">
                  {{ 'backoffice.partner.company' | translate }} #{{i+1}}
                </button>
              </h2>
              <div id="flush-collapseOne" class="panel">
                <div class="accordion-body">
                  <div class="row">
                    <div class="col-md-6 form-group">
                      <label> {{ 'backoffice.partner.companyCode' | translate }}</label>
                      <input class="form-control" type="text" [value]="company.company_code" disabled />
                    </div>
                    <div class="col-md-6 form-group">
                      <label>{{ 'backoffice.partner.customerCode' | translate }}</label>
                      <input class="form-control" type="text" [value]="company.customer_id" disabled />
                    </div>
                    <div class="col-md-6 form-group">
                      <label>{{ 'backoffice.partner.deletionIndicator' | translate }}</label>
                      <input class="form-control" type="text" [value]="company.deletion_indicator" disabled />
                    </div>
                    <div class="col-md-6 form-group">
                      <label>{{ 'backoffice.partner.customerAccountGroup' | translate }}</label>
                      <input class="form-control" type="text" [value]="company.customer_account_group" disabled />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </asar-tab>
      <asar-tab [tabTitle]="'Partner Function'">
        <div class="row">
          <div class="accordion accordion-flush" *ngIf="partnerFunction$ | async as partnerFunctions">
            <div class="accordion-item" *ngFor="let partnerFunction of partnerFunctions;let i = index;">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed" (click)="toggleAccordian(partnerFunctions,$event, i)"
                  type="button">
                  {{ 'backoffice.partner.partnerFunction' | translate }} #{{i+1}}
                </button>
              </h2>
              <div id="flush-collapseOne" class="panel">
                <div class="accordion-body">
                  <div class="row">
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.name' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.name" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.address' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.address" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.salesOrg' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.sales_organization" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.distributionChannel' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.distribution_channel" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.division' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.division" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.partnerCounter' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.partner_counter" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.partnerFunction' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.partner_function" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.customerNumber' | translate }}</label>
                      <input class="form-control" type="text" [value]="partnerFunction.bp_customer_number" disabled />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </asar-tab>
      <asar-tab [tabTitle]="'Sales Area'">
        <div class="row">
          <div class="accordion accordion-flush" *ngIf="salesAreas$ | async as salesAreas">
            <div class="accordion-item" *ngFor="let salesArea of salesAreas;let i = index;">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed" (click)="toggleAccordian(salesAreas,$event, i)"
                  type="button">
                  {{ 'backoffice.partner.salesArea' | translate }} #{{i+1}}
                </button>
              </h2>
              <div id="flush-collapseOne" class="panel">
                <div class="accordion-body">
                  <div class="row">
                    <div class="col-md-4 col-12 form-group">
                      <label> {{ 'backoffice.partner.organization' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.sales_organization" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.distributionChannel' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.distribution_channel" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.division' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.division" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.currency' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.currency" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.customerAccountAssignmentGroup' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.customer_account_assignment_group"
                        disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.customerGroup' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.customer_group" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.customerPriceGroup' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.customer_price_group" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.customerPricingProcedure' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.customer_pricing_procedure" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.deliveryBlockedForCustomer' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.delivery_is_blocked_for_customer"
                        disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.deletionIndicator' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.deletion_indicator" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.salesGroup' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.sales_group" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.salesOffice' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.sales_office" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.shippingCondition' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.shipping_condition" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.supplyingPlant' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.supplying_plant" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>{{ 'backoffice.partner.accountGroup' | translate }}</label>
                      <input class="form-control" type="text" [value]="salesArea.customer_account_group" disabled />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </asar-tab>
      <asar-tab [tabTitle]="'Texts'">
        <div class="row">
          <div class="accordion accordion-flush" *ngIf="texts$ | async as texts">
            <div class="accordion-item" *ngFor="let text of texts;let i = index;">
              <h2 class="accordion-header">
                <button class="accordion-button collapsed" (click)="toggleAccordian(texts, $event, i)" type="button">
                  {{ text?.description ? text?.description : text?.long_text_id }}
                </button>
              </h2>
              <div id="flush-collapseOne" class="panel">
                <div class="accordion-body">
                  <div class="row">
                    <div class="col-12 form-group">
                      <label>Text</label>
                      <textarea class="form-control text-area" type="text" [value]="text.long_text" disabled></textarea>
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>Text Id</label>
                      <input class="form-control" type="text" [value]="text.long_text_id" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>Language</label>
                      <input class="form-control" type="text" [value]="text.language" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>Created By</label>
                      <input class="form-control" type="text" [value]="text.created_by" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group">
                      <label>Created At</label>
                      <input class="form-control" type="text" [value]="text.created_at | date" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group" *ngIf="text.updated_by">
                      <label>Updated By</label>
                      <input class="form-control" type="text" [value]="text.updated_by" disabled />
                    </div>
                    <div class="col-md-4 col-12 form-group" *ngIf="text.updated_at">
                      <label>Updated At</label>
                      <input class="form-control" type="text" [value]="text.updated_at | date" disabled />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </asar-tab>
    </asar-tabs>
  </div>
</div>