<section class="customer-services-sec">
  <app-banner-preview [customerID]="customer_id" [settings]="settings"
    [placement]="'CUST_SERVICE'"></app-banner-preview>
  <!-- <div class="customer-services-img">
    <video width="100%" height="100%" autoplay loop controls>
      <source src="/assets/images/customer-s-video.mp4" type="video/mp4">
    </video>
  </div> -->
  <div class="cus-services-body">
    <!-- <div class="cus-services-title">
      <h1>Welcome to <span>Customer Service Portal</span></h1>
    </div> -->
    <div class="cus-services-list">
      <a class="cus-services-box" [routerLink]="['/store/account']">
        <div class="cus-services-box-icon">
          <i class="material-icons-outlined">person</i>
        </div>
        <div class="cus-services-box-text">Account</div>
      </a>
      <ng-container *checkAppFeature="'F0013'">
        <a class="cus-services-box" [routerLink]="['/store/tickets/create']" *checkPermission="'P0025'">
          <div class="cus-services-box-icon">
            <img src="assets/images/create-services-request.svg" />
          </div>
          <div class="cus-services-box-text">Create Service Request</div>
        </a>
      </ng-container>
      <ng-container *checkAppFeature="'F0012'">
        <a class="cus-services-box" [routerLink]="['/store/tickets']" *checkPermission="'P0023'">
          <div class="cus-services-box-icon">
            <img src="assets/images/services-request.svg" />
          </div>
          <div class="cus-services-box-text">Service Request Status</div>
        </a>
      </ng-container>
      <ng-container *checkAppFeature="'F0006'">
        <a class="cus-services-box" [routerLink]="['/store/order-history']" *checkPermission="'P0048'">
          <div class="cus-services-box-icon">
            <img src="/assets/images/order-status.svg" />
          </div>
          <div class="cus-services-box-text">Orders</div>
        </a>
      </ng-container>
      <ng-container *checkAppFeature="'F0011'">
        <a class="cus-services-box" [routerLink]="['/store/register-product']" *checkPermission="'P0022'">
          <div class="cus-services-box-icon">
            <img src="assets/images/product-reg.svg" />
          </div>
          <div class="cus-services-box-text">Register Product</div>
        </a>
      </ng-container>
      <ng-container *checkAppFeature="'F0009'">
        <a class="cus-services-box" [routerLink]="['/store/check-registered-product']" *checkPermission="'P0020'">
          <div class="cus-services-box-icon">
            <img src="assets/images/check-registered-product.svg" />
          </div>
          <div class="cus-services-box-text">Check Registered Product</div>
        </a>
      </ng-container>
      <ng-container *checkAppFeature="'F0014'">
        <a class="cus-services-box" [routerLink]="['/store/check-warranty']" *checkPermission="'P0026'">
          <div class="cus-services-box-icon">
            <img src="assets/images/product-warranty.svg" />
          </div>
          <div class="cus-services-box-text">Check Product Warranty</div>
        </a>
      </ng-container>
      <ng-container *checkAppFeature="'F0010'">
        <a class="cus-services-box" [routerLink]="['/store/knowledge-base']" *checkPermission="'P0021'">
          <div class="cus-services-box-icon">
            <img src="assets/images/knowledge-base.svg" />
          </div>
          <div class="cus-services-box-text">Knowledge Base</div>
        </a>
      </ng-container>
      <a class="cus-services-box" [routerLink]="['/store/customer-services/reports']">
        <div class="cus-services-box-icon">
          <i class="material-icons-outlined">summarize</i>
        </div>
        <div class="cus-services-box-text">Reports</div>
      </a>
      <a class="cus-services-box" [routerLink]="['/store/customer-services/dashboard']">
        <div class="cus-services-box-icon">
          <i class="material-icons-outlined">leaderboard</i>
        </div>
        <div class="cus-services-box-text">Dashboard</div>
      </a>
    </div>
  </div>

</section>