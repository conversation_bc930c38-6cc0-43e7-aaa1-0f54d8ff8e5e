<section class="quote-sec">
  <div class="quote-body">
    <div class="all-main-title-sec">
      <h1>Quote Status</h1>
      <div class="all-bedcrumbs">
        <ul>
          <li>
            <a [routerLink]="['/store/dashboard']">
              <span class="material-icons-outlined">home</span> Home
            </a>
          </li>
          <li>Quotes</li>
        </ul>
      </div>
    </div>
    <div class="quote-contact-list">
      <div class="quote-c-box">
        <div class="quote-c-icon">
          <img src="/assets/images/seller-icon.png" alt="" title="" />
        </div>
        <div class="quote-c-details">
          <h4>Customer #</h4>
          <small>{{ sellerDetails.bp_customer_number }}</small>
        </div>
      </div>
      <div class="quote-c-box">
        <div class="quote-c-icon">
          <img src="/assets/images/phone-icon.png" alt="" title="" />
        </div>
        <div class="quote-c-details">
          <h4>Customer Name</h4>
          <small>{{ sellerDetails.name }}</small>
        </div>
      </div>
      <div class="quote-c-box address-box">
        <div class="quote-c-icon">
          <img src="/assets/images/address-icon.png" alt="" title="" />
        </div>
        <div class="quote-c-details">
          <h4>ADDRESS</h4>
          <small>{{ sellerDetails.address }}</small>
        </div>
      </div>
    </div>
    <form class="quote-form all-form-res" [formGroup]="filterForm">
      <div class="form">
        <div class="form-group">
          <label><span class="material-icons-outlined">calendar_month</span> Date From</label>
          <div class="input-group">
            <input class="form-control" name="picker1" formControlName="DOCUMENT_DATE" ngbDatepicker #d="ngbDatepicker"
              [maxDate]="today()" />
            <button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
              <i class="material-icons-outlined">
                calendar_month
              </i>
            </button>
          </div>
        </div>
        <div class="form-group">
          <label><span class="material-icons-outlined">calendar_month</span> Date To</label>
          <div class="input-group">
            <input class="form-control" name="picker2" formControlName="DOCUMENT_DATE_TO" ngbDatepicker
              #d1="ngbDatepicker" [minDate]="f.DOCUMENT_DATE.value" [maxDate]="today()" />
            <button class="btn btn-outline-secondary" (click)="d1.toggle()" type="button">
              <i class="material-icons-outlined">
                calendar_month
              </i>
            </button>
          </div>
        </div>
        <div class="form-group">
          <label><span class="material-icons-outlined">feed</span> Quote
            Status</label>
          <select class="form-control select-arrow-down" formControlName="DOC_STATUS">
            <option *ngFor="let status of statuses" [value]="status.code">
              {{ status.description }}
            </option>
          </select>
        </div>
        <div class="form-group o-num">
          <label><span class="material-icons-outlined">pin</span> Quote
            #</label>
          <input type="input" class="form-control" placeholder="Enter Quote #" formControlName="SD_DOC" />
        </div>
        <p class="text-danger d-flex w-100" *ngIf="submitted && filterForm.touched && filterForm.invalid">
          Please add a valid from and to date
        </p>
        <div class="form-btn-sec d-flex justify-content-center gap-1">
          <button type="button" class="mx-4 quote-s-btn" (click)="clear()">Clear</button>
          <button type="button" class="mx-4 quote-s-btn" (click)="search()" [disabled]="loading">
            {{ loading ? "Searching..." : "Search" }}
          </button>
        </div>
      </div>
    </form>
    <div class="quote-s-table">
      <app-grid [columns]="columnDefs" (rowClick)="goToQuote($event)" [data]="quotes" [showExport]="true"
        (exportClick)="exportToExcel()" *ngIf="!loading && quotes.length"></app-grid>
      <div class="w-100" *ngIf="loading || !quotes.length">{{ loading ? 'Loading...' : 'No records found.' }}
      </div>
    </div>
  </div>
</section>