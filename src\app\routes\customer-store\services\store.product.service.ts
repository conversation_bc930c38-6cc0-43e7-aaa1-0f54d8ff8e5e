import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs";

import { ApiConstant } from "src/app/constants/api.constants";

export interface ProductSearchParam {
  perPage?: number;
  pageNo?: number;
  sortBy?: string;
  sortOrder?: string;
  search?: string;
  searchBy?: string;
  [param: string]: any;
}

@Injectable({
  providedIn: "root",
})
export class StoreProductService {
  constructor(private http: HttpClient) {}

  search(searchTerms: any) {
    const params = new HttpParams().appendAll(searchTerms);
    return this.http.get<any>(ApiConstant.GET_ALL_PRODUCTS, {
      params,
    });
  }

  getById(productId: string) {
    return this.http.get<any>(`${ApiConstant.GET_ALL_PRODUCTS}/${productId}`);
  }

  getImages(productId: string) {
    return this.http.get<any>(`${ApiConstant.PRODUCT_IMAGE}/${productId}`);
  }

  getMedia(id: any) {
    return this.http
      .get<any>(
        ApiConstant.GET_PRODUCT_IMAGES.replace("{id}", (id || "").toString())
      )
      .pipe(
        map((res) => {
          if (res.status === "success") {
            const data = res?.data || [];
            res.data = data.sort((a: any, b: any) =>
              a.is_cover_image === b.is_cover_image
                ? 0
                : a.is_cover_image
                ? -1
                : 1
            );
          }
          return res;
        })
      );
  }

  getMaterialStock(id: string) {
    return this.http.get<any>(
      ApiConstant.GET_MATERIAL_STOCK.replace("{id}", id)
    );
  }

  getSalesPrice(obj: any) {
    return this.http.post<any>(ApiConstant.GET_SALES_PRICE, obj);
  }

  getProductClassification(productId: any) {
    return this.http.get<any>(
      `${ApiConstant.GET_ALL_PRODUCTS}/${productId}/classification`
    );
  }
}
