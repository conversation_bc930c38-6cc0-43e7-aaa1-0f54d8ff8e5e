import { Component } from "@angular/core";
import { Subject, takeUntil, take } from "rxjs";
import { <PERSON><PERSON><PERSON><PERSON>, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { NgbModal, NgbModalOptions } from "@ng-bootstrap/ng-bootstrap";

import { UserRoleService } from "./user-role.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { UserRolePermissionsComponent } from "./user-role-permissions/user-role-permissions.component";
import { AuthService } from "src/app/core/authentication/auth.service";

@Component({
  selector: "app-user-role",
  templateUrl: "./user-role.component.html",
  styleUrls: ["./user-role.component.scss"],
})
export class UserRoleComponent {
  private ngUnsubscribe = new Subject<void>();
  public userRoles: any[] = [];
  public userRoleTypes: any[] = [
    { code: "BACKOFFICE", description: "Backoffice" },
    { code: "CUST_SERVICE", description: "Customer Service" },
    { code: "SALES", description: "Sales" },
    { code: "STOREFRONT", description: "Storefront" },
    { code: "VENDOR", description: "Vendor" },
  ];
  public form: FormGroup;
  public submitted: any = false;
  public saving: any = false;
  public isDialogOpen: any = false;
  private modalOption: NgbModalOptions = {};
  public roleHierarchy: any[] = [];
  public loggedInUserRole: any = null;

  constructor(
    private fb: FormBuilder,
    private dialog: NgbModal,
    private userRoleService: UserRoleService,
    private _snackBar: AppToastService,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    this.createForm();
    this.getUserRoles();
  }

  createForm() {
    this.form = this.fb.group({
      new_role: this.fb.group({
        role_name: ["", Validators.required],
        role_type: ["", Validators.required],
        parent_role_id: ["", Validators.required],
      }),
      list: this.fb.array([]),
    });
  }

  get f(): any {
    return this.form.controls;
  }

  get newRoleF(): any {
    return this.f?.new_role?.controls;
  }

  get roles(): any {
    return this.form.get("list") as FormArray;
  }

  insertRole(role: any): FormGroup {
    return this.fb.group({
      id: [role?.id],
      role_name: [role?.role_name || "", Validators.required],
      role_type: [role?.role_type || "", Validators.required],
      parent_role_id: [role?.parent_role_id || "", Validators.required],
      editing: [false],
      submitted: [false],
      saving: [false],
    });
  }

  makeRoleList(roles: any) {
    this.roles.clear();
    roles.forEach((role: any) => {
      this.roles.push(this.insertRole(role));
    });
  }

  getUserRoles() {
    this.userRoleService
      .getUserRoles()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          const data = res?.data || [];
          this.loggedInUserRole = data.find(
            (d: any) => d.id === this.auth?.userDetail?.user_role_id
          );
          this.roleHierarchy = this.loggedInUserRole?.roleHierarchy || [];
          this.userRoles = data;
          this.makeRoleList([...this.userRoles]);
        },
        error: () => {
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  addRole() {
    this.submitted = true;
    if (this.f?.new_role?.status === "INVALID") {
      return;
    }
    const payload = this.f?.new_role?.value;
    this.saving = true;
    this.userRoleService
      .createUserRole(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.f?.new_role.patchValue({
            role_name: "",
            role_type: "",
            parent_role_id: "",
          });
          this.submitted = false;
          this.saving = false;
          this.getUserRoles();
        },
        error: () => {
          this.submitted = false;
          this.saving = false;
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  editRole(role: any) {
    role.patchValue({ editing: true });
  }

  cancelEditing(role: any, intIndex: number) {
    const oldVal = this.userRoles[intIndex];
    const role_name = oldVal.role_name;
    const role_type = oldVal.role_type;
    const parent_role_id = oldVal.parent_role_id;
    role.patchValue({ role_name, role_type, parent_role_id, editing: false });
  }

  saveRole(role: any) {
    role.patchValue({ submitted: true });
    if (role?.status === "INVALID") {
      return;
    }
    const val = role?.value;
    const id = val?.id;
    const payload = {
      role_name: val?.role_name,
      role_type: val?.role_type,
      parent_role_id: val?.parent_role_id,
    };
    role.patchValue({ saving: true });
    this.userRoleService
      .updateUserRole(id, payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this._snackBar.open("Changes saved successfully!");
          role.patchValue({
            submitted: false,
            saving: false,
            editing: false,
          });
          this.getUserRoles();
        },
        error: () => {
          role.patchValue({
            submitted: false,
            saving: false,
            editing: false,
          });
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  deleteRole(role: any) {
    const val = role?.value;
    const id = val?.id;
    role.patchValue({ saving: true });
    this.userRoleService
      .deleteUserRole(id)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          role.patchValue({ saving: false });
          this.getUserRoles();
        },
        error: () => {
          role.patchValue({ saving: false });
          this._snackBar.open("Error while processing your request.", {
            type: "Error",
          });
        },
      });
  }

  openDialog(userRole: any) {
    if (!this.isDialogOpen) {
      this.isDialogOpen = true;
      this.modalOption.backdrop = "static";
      this.modalOption.keyboard = false;
      this.modalOption.size = "xl";
      const dialogRef = this.dialog.open(
        UserRolePermissionsComponent,
        this.modalOption
      );
      dialogRef.componentInstance.data = userRole;
      dialogRef.result
        .then(() => {
          this.isDialogOpen = false;
        })
        .catch(() => {
          this.isDialogOpen = false;
        });
    }
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
