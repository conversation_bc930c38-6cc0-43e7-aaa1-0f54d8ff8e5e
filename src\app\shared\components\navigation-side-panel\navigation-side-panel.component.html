<div class="navigation-panel vh-100 overflow-hidden">
  <div class="fixed-sidebar m-0 p-0 position-fixed top-0 start-0 vh-100">
    <div class="toggler m-0 p-0 d-flex align-items-center justify-content-center w-100" role="button">
      <span class="material-icons-outlined" (click)="handleClick()">apps</span>
    </div>
    <ul class="m-0 p-0 position-relative d-flex flex-column">
      <li class="p-0 position-relative"><a class="m-0 p-0 position-relative d-block text-center"
          [routerLink]="['/store']" *checkPermission="'P0001'"><span
            class="material-icons-outlined p-0 d-inline-flex align-items-center justify-content-center">add_business</span>
          <span class="d-block text-center">{{ 'Store' |
            translate }}</span></a></li>
      <li class="p-0 position-relative"><a class="m-0 p-0 position-relative d-block text-center" href=""><span
            class="material-icons-outlined p-0 d-inline-flex align-items-center justify-content-center">perm_phone_msg</span>
          <span class="d-block text-center">{{
            'Help' | translate
            }}</span></a></li>
      <li class="p-0 position-relative"><a class="m-0 p-0 position-relative d-block text-center"
          (click)="handleLogout()"><span
            class="material-icons-outlined p-0 d-inline-flex align-items-center justify-content-center">logout</span>
          <span class="d-block text-center">{{ 'Logout' | translate
            }}</span></a></li>
    </ul>
  </div>

  <ul class="navigation-panel-menu" *ngIf="links && links.length > 0">
    <ng-container *ngFor="let link of links">
      <li *checkPermission="link.permission">
        <ng-container *ngIf="link.subLinks?.length">
          <a href="javascript:void(0)" (click)="link.open = !link.open">
            <span class="d-flex gap-1 align-items-center">
              <span class="material-icons-outlined">{{link.iconClass}}</span>
              <span [ngClass]="currentPanelState">{{ link.text| translate}}</span>
            </span>
            <span class="material-icons-outlined">{{link.open ? 'expand_more': 'chevron_right'}}</span>
          </a>
          <ul *ngIf="link.open">
            <ng-container *ngFor="let sublink of link.subLinks">
              <li class="mx-2 sub-link" *checkPermission="sublink.permission">
                <a [routerLink]="sublink.url" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: true}">
                  <span class="d-flex gap-1 align-items-center">
                    <span class="material-icons-outlined">{{sublink.iconClass}}</span>
                    <span [ngClass]="currentPanelState">{{ sublink.text| translate}}</span>
                  </span>
                </a>
              </li>
            </ng-container>
          </ul>
        </ng-container>
        <ng-container *ngIf="!link.subLinks?.length">
          <a [routerLink]="link.url" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: true}">
            <span class="d-flex gap-1 align-items-center">
              <span class="material-icons-outlined">{{link.iconClass}}</span>
              <span [ngClass]="currentPanelState">{{ link.text| translate}}</span>
            </span>
          </a>
        </ng-container>
      </li>
    </ng-container>
  </ul>
</div>