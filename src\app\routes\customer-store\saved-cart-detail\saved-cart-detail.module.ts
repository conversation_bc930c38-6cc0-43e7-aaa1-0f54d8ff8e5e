import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SavedCartDetailComponent } from './saved-cart-detail.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [
    SavedCartDetailComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: '', component: SavedCartDetailComponent }]),
  ]
})
export class SavedCartDetailModule { }
