import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  Output,
  ViewChild,
  SimpleChanges,
} from "@angular/core";
import {
  ColDef,
  ColumnMovedEvent,
  GridOptions,
  IDatasource,
  IGetRowsParams,
} from "ag-grid-community";
import { TranslateService } from "@ngx-translate/core";

import { GridColumn } from "../../grid-column/grid-column.model";
import { ManageUserService } from "../manage-user-service/manage-user.service";
import { AppToastService } from "src/app/shared/services/toast.service";
import { BtnDelRendererComponent } from "../renderer/btn-del-renderer/btn-del-renderer.component";
import { RolesType } from "src/app/constants/api.constants";
import { AuthService } from "src/app/core/authentication/auth.service";

@Component({
  selector: "app-manage-user-customer",
  templateUrl: "./manage-user-customer.component.html",
  styleUrls: ["./manage-user-customer.component.scss"],
})
export class ManageUserCustomerComponent {
  @Input() model: any = {};
  @Input() userRoleType: any;
  public columnDefs: ColDef[] = [
    {
      field: "customer_id",
      headerName: this.getTranslate("backoffice.contact.customerID"),
      sortable: true,
      minWidth: 125,
    },
    {
      field: "bp_uuid",
      headerName: this.getTranslate("backoffice.contact.accountGUID"),
      sortable: true,
    },
    {
      field: "customer_name",
      headerName: this.getTranslate("backoffice.contact.customerName"),
      sortable: true,
      minWidth: 200,
    },
    {
      field: "cpf_address",
      headerName: this.getTranslate("backoffice.contact.address"),
      sortable: true,
    },
    {
      field: "phone",
      headerName: this.getTranslate("backoffice.contact.phone"),
      sortable: true,
      minWidth: 150,
    },
  ];

  public defaultColDef: ColDef = {
    flex: 1,
    minWidth: 250,
  };

  public rowData!: any[];
  private gridApi!: any;
  private gridColumnApi!: any;
  public isLoading = true;
  public query = {
    perPage: 10,
    pageNo: 1,
  };
  public searchText: string = "";
  public searchBy: string = "customer_name";
  public pageSize = 10;
  public gridOptions: GridOptions = {
    suppressMenuHide: true,
    pagination: true,
    cacheBlockSize: 10,
    paginationPageSize: 10,
    rowModelType: "infinite",
    onColumnMoved: (event: ColumnMovedEvent) => this._onColumnMoved(event),
  };

  public dataSource: IDatasource = {
    getRows: (params: IGetRowsParams) => {
      this.query.pageNo = params.endRow / this.query.perPage;
      this.gridApi.showLoadingOverlay();
      this.manageUserService
        .getCustomersByUserId(this.model.id, {
          perPage: this.pageSize,
          search: this.searchText,
          searchBy: this.searchBy,
          pageNo: params.endRow / this.pageSize,
        })
        .subscribe({
          next: ({ data, total }: any) => {
            params.successCallback(data, total);
            this.gridApi.hideOverlay();
          },
          error: (err: any) => {
            this.gridApi.hideOverlay();
            this._snackBar.open(
              err.error?.message ||
                "Error while processing get user's customer request.",
              { type: "Error" }
            );
          },
        });
    },
  };

  constructor(
    private translate: TranslateService,
    private _snackBar: AppToastService,
    private manageUserService: ManageUserService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    if (this.userRoleType !== RolesType.STOREFRONT) {
      this.columnDefs.push({
        headerName: "Action",
        cellRenderer: BtnDelRendererComponent,
        cellRendererParams: {
          onClick: this.deleteUserCustomer.bind(this),
          label: "Delete",
        },
        minWidth: 120,
      });
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    const model: any = changes["model"];
    if (model) {
      const previousValue = model.previousValue;
      const currentValue = model.currentValue;
      if (this.gridApi && previousValue !== currentValue) {
        this.updateTable();
      }
    }
  }

  @HostListener("window:resize", ["$event"])
  onResize(event: any) {
    this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params: { api: any; columnApi: any }) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
    this.gridApi.setDatasource(this.dataSource);
  }

  onSearchChange(text: string) {
    this.searchText = text;
    this.updateTable();
  }

  onSearchByChange(option: string) {
    this.searchBy = option;
    this.updateTable();
  }

  updateTable() {
    this.gridApi.paginationGoToPage(0);
    setTimeout(() => {
      this.gridApi.purgeInfiniteCache();
    }, 100);
  }

  getTranslate(key: string) {
    return this.translate.instant(key);
  }

  @ViewChild("columnMenu") columnMenu: any;
  @Output() columnChange = new EventEmitter<GridColumn[]>();
  _onColumnChange(columns: any[]) {
    this.columnChange.emit(columns);

    const displayedColumns = Object.assign(
      [],
      this.getDisplayedColumnFields(columns)
    );
    const allColumns = Object.assign(
      [],
      this.getAllDisplayedColumnFields(columns)
    );
    this.gridColumnApi.setColumnsVisible(displayedColumns, false);
    this.gridColumnApi.setColumnsVisible(allColumns, true);
    this.gridApi.sizeColumnsToFit();
  }

  getDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => !item.show)
      .map((item: GridColumn) => item.field)
      .filter((item) => item);
    return fields;
  }

  getAllDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => item.show)
      .map((item: GridColumn) => item.field)
      .filter((item) => item);
    return fields;
  }

  _columnPositionChange(position: number[]) {
    this.gridColumnApi.moveColumnByIndex(position[0], position[1]);
    this.gridApi.sizeColumnsToFit();
  }

  _onColumnMoved(params: any) {
    const columnDragState = params.columnApi.getColumnState();
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map((e: { colDef: any }) => {
        return e.colDef;
      });
    const newColDef: ColDef[] = colIds.map((item: any, i: string | number) =>
      Object.assign({}, item, columnDragState[i])
    );
    this.columnDefs = newColDef;
  }

  refreshRowData(data: any) {
    const rowNode = this.gridApi.getRowNode(data.customer_id)!;
    rowNode && rowNode.setData(data);
  }

  getRowId(params: any) {
    return params.data.customer_id;
  }

  deleteUserCustomer(e: any) {
    this.manageUserService
      .removeCustByUserIdAndCustId(this.model.id, e.rowData.customer_id)
      .subscribe({
        next: () => {
          this.updateTable();
          const user = this.authService?.userDetail || null;
          if (
            this.model?.email === user?.email &&
            user?.partner_function?.customer_id === e.rowData.customer_id
          ) {
            const authData = { ...this.authService?.userDetail };
            delete authData.partner_function;
            this.authService.setAuth(authData);
          }
        },
        error: (err: any) => {
          this._snackBar.open(
            err.error?.message ||
              "Error while processing remove user's customer request.",
            { type: "Error" }
          );
        },
      });
  }
}
