<div class="all-main-title-sec cart-details-main-title">
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>
        <a [routerLink]="['/store/saved-cart']">
            <span class="material-icons-outlined">shopping_cart</span>
            Saved Carts
        </a>
      </li>
      <li>Import Saved Cart</li>
    </ul>
  </div>
  <h1>Import Saved Cart</h1>
  <small>The imported file will be used to create a saved cart</small>
</div>

<div class="import-saved-card-body flex-wrap">
  <div class="import-saved-cart-box">
    <h3>The CSV file should list the product SKUs and quantities in the following format:</h3>
    <ul>
      <li><span class="material-icons-outlined">east</span>Headers: SKU, Quantity, Name, Description</li>
      <li><span class="material-icons-outlined">east</span> Maximum file size: 30.00 KB</li>
      <li><span class="material-icons-outlined">east</span> Template: <a
          href="https://snjya.s3.us-east-2.amazonaws.com/Files/Saved+Cart+Upload.csv">Download</a></li>
    </ul>
  </div>
  <div class="import-saved-cart-box">
    <h3>Select a file to upload. The file must be in CSV format.</h3>
    <input type="file" class="file-input" accept=".csv" (change)="onFileSelected($event)" #fileInput>
    <button type="button" (click)="fileInput.click()" class="upload-file-link"><span class="material-icons-outlined">
        cloud_upload</span> {{ fileName || 'Choose File' }}<span class="material-icons-outlined red" *ngIf="fileName" (click)="removeFile($event)">
        cancel</span></button>
  </div>
</div>
<div class="save-c-btn"><button type="button" class="btn btn-warning primary_color" [disabled]="!fileName || saving"
    (click)="upload()">{{ saving ? 'Importing...' : 'Import Saved Cart' }}</button></div>