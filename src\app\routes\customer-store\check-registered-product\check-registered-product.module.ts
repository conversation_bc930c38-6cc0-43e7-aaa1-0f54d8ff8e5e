import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";

import { CheckRegisteredProductComponent } from "./check-registered-product.component";
import { SharedModule } from "src/app/shared/shared.module";
import { NgbDatepickerModule } from "@ng-bootstrap/ng-bootstrap";

@NgModule({
  declarations: [CheckRegisteredProductComponent],
  imports: [
    CommonModule,
    SharedModule,
    NgbDatepickerModule,
    RouterModule.forChild([{ path: "", component: CheckRegisteredProductComponent }]),
  ],
})
export class CheckRegisteredProductModule { }
