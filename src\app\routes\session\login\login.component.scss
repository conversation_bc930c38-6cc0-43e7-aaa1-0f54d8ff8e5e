::ng-deep {
	body {
		background: url(../../../../assets/images/worldmap-bg.png) center center no-repeat;
		background-size: contain;
		height: 100vh;
		margin: 0;
		background-color: #f7f6f9;
	}
}

.sign_section {
	margin: 0;
	padding: 50px 0;
	height: 100vh;
	overflow: auto;

	a.navbar-brand {
		margin: 0 0 30px 0;
		padding: 0;
		position: relative;
		display: block;
		max-width: 150px;

		img {
			margin: 0;
			padding: 0;
			width: 100%;
		}
	}

	.login_box {
		margin: 0;
		background: var(--snjy-color-white);
		border-radius: 20px;
		min-height: 500px;
		max-width: 500px;
		border: 1px solid var(--snjy-border-color-secondary);
		box-shadow: var(--snjy-box-shadow);

		h1 {
			margin: 0 0 30px 0;
			font-size: 36px;
			font-weight: var(--snjy-font-weight-bold);
			color: var(--snjy-button-color-primary);
			line-height: 42px;

			span {
				margin: 8px 0 0 0;
				color: var(--snjy-color-dark-secondary);
				text-transform: capitalize;
				font-weight: var(--snjy-font-weight-bolder);
				font-size: 28px;
			}
		}

		.form-group {
			margin: 0 0 10px 0;
			padding: 0;
			position: relative;

			&::before {
				position: absolute;
				content: "";
				font-family: "Material icons Outlined";
				top: 0;
				left: 10px;
				bottom: 0;
				margin: auto 0;
				font-size: var(--snjy-font-size-2);
				color: var(--snjy-button-color-primary);
				font-weight: var(--snjy-font-weight-normal);
				line-height: 23px;
				height: fit-content;
			}

			label {
				margin: 0 0 8px 0;
				padding: 0;
				font-size: var(--snjy-font-size-0-875);
				font-weight: var(--snjy-font-weight-medium);
				color: var(--snjy-button-color-primary);
				line-height: 13px;
				display: flex;
				align-items: center;
				gap: 0 2px;
			}

			.form-control {
				margin: 0;
				padding: 0 15px 0 40px;
				height: 50px;
				border: none;
				background: #f5f5f5;
				border-radius: 10px;
				font-size: var(--snjy-font-size-0-875);
				font-weight: var(--snjy-font-weight-medium);
			}

			span {
				margin: 3px 0 0 0;
				padding: 0;
				display: block;
				text-align: right;
				font-size: var(--snjy-font-size-0-8);
				font-weight: var(--snjy-font-weight-medium);
				text-decoration: underline;
				color: #6c757d;
			}
		}

		.user-name::before {
			content: "\e7ff";
			top: 20px;
		}

		.user-pass::before {
			content: '\e897';
		}

		.form-group.form-check {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			gap: 0 8px;

			input {
				height: 16px;
				width: 16px;
				min-width: fit-content !important;
				min-height: fit-content !important;
				margin: 0;
			}

			label {
				margin: 0 !important;
				padding: 0;
			}
		}

		.button-section {
			display: flex;
			gap: 0 12px;

			button {
				margin: 0 0 20px 0;
				padding: 0;
				border-radius: 10px;
				display: flex;
				font-size: var(--snjy-font-size-0-875);
				height: 52px;
				justify-content: center;
				align-items: center;
				line-height: 14px;
				width: 100%;
				padding-bottom: 0;
				padding-top: 0;
				cursor: pointer;
				color: var(--snjy-color-white);
				background: var(--snjy-button-gradient);
				position: relative;
				text-transform: uppercase;
				font-weight: var(--snjy-font-weight-bold);
				transition: all0 0.3s ease-in-out;
				border: none;
			}

			button.btn.btn-primary {
				background: transparent !important;
				border: 1px solid var(--snjy-color-dark-secondary) !important;
				color: var(--snjy-color-dark-secondary) !important;
			}
		}
	}

	.img-fluid {
		max-height: 500px;
	}
}

.img-container {
	max-width: 48%;
}

@media (max-width: 770px) {
	.img-container {
		max-width: 96%;
	}
}
.sso-btn {
	color: var(--snjy-button-color-primary);
}