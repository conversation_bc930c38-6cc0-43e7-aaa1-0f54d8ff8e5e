import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NgbCarouselModule } from '@ng-bootstrap/ng-bootstrap';

import { CustomerDashboardComponent } from './customer-dashboard.component';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [
    CustomerDashboardComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    NgbCarouselModule,
    RouterModule.forChild([{ path: '', component: CustomerDashboardComponent }]),
  ]
})
export class CustomerDashboardModule { }
