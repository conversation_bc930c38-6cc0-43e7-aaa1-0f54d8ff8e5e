import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable, map } from "rxjs";
import { ApiConstant } from "src/app/constants/api.constants";

@Injectable({
  providedIn: "root",
})
export class BannerService {
  constructor(private http: HttpClient) {}

  getBanner(): Observable<any[]> {
    return this.http
      .get<any[]>(`${ApiConstant.BANNER}`)
      .pipe(map((res: any) => res?.data || []));
  }

  getCustGroupBanner(
    placement: any,
    custGroup?: any,
    custAccountGroup?: any
  ): Observable<any[]> {
    let params = new HttpParams();
    params = params.append("placement", placement);
    if (custGroup && custAccountGroup) {
      params = params.append("custGroup", custGroup);
      params = params.append("custAccountGroup", custAccountGroup);
    } else if (custGroup) {
      params = params.append("custGroup", custGroup);
    } else if (custAccountGroup) {
      params = params.append("custAccountGroup", custAccountGroup);
    }

    return this.http
      .get<any[]>(`${ApiConstant.BANNER}/customer-group`, { params })
      .pipe(map((res: any) => res?.data || []));
  }

  getBannerById(bannerId: number): Observable<any> {
    return this.http.get<any>(`${ApiConstant.BANNER}/${bannerId}`);
  }

  createBanner(banner: any): Observable<any> {
    return this.http.post<any>(`${ApiConstant.BANNER}`, banner);
  }

  updateBanner(bannerId: number, updatedBanner: any): Observable<any> {
    return this.http.put<any>(
      `${ApiConstant.BANNER}/${bannerId}`,
      updatedBanner
    );
  }

  deleteBanner(bannerId: number): Observable<any> {
    return this.http.delete<any>(`${ApiConstant.BANNER}/${bannerId}`);
  }
}
