import {
  Component,
  EventEmitter,
  HostListener,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import {
  ColDef,
  ColumnMovedEvent,
  GridOptions,
  IDatasource,
  IGetRowsParams,
} from "ag-grid-community";
import { GridColumn } from "src/app/shared/components/grid-column/grid-column.model";
import { Customer } from "../models/customer.model";
import { CustomerService } from "./customer.service";
import { tap } from "rxjs";

@Component({
  selector: "app-customer",
  templateUrl: "./customer.component.html",
  styleUrls: ["./customer.component.scss"],
})
export class CustomerComponent implements OnInit {
  public columnDefs: ColDef[] = [
    {
      field: "bp_id",
      headerName: this.getTranslate("backoffice.partner.id"),
      sortable: true,
    },
    {
      field: "bp_full_name",
      headerName: this.getTranslate("backoffice.partner.name"),
      sortable: true,
    },
    // {
    //   field: "bp_type",
    //   headerName: this.getTranslate("backoffice.partner.type"),
    //   sortable: true,
    //   filter: true,
    // },
    {
      field: "bp_category",
      headerName: this.getTranslate("backoffice.partner.category"),
      sortable: true,
    },
    // {
    //   field: "company",
    //   headerName: this.getTranslate("backoffice.partner.company"),
    //   sortable: true,
    //   filter: true,
    // },
    {
      field: "email",
      headerName: "Email",
      sortable: true,
    },
    {
      field: "phone",
      headerName: this.getTranslate("phone"),
      sortable: true,
    },
  ];
  public defaultColDef: ColDef = {
    flex: 1,
    minWidth: 150,
  };

  public rowData!: any[];
  private gridApi!: any;
  private gridColumnApi!: any;
  isLoading = true;
  query = {
    perPage: 10,
    pageNo: 1,
  };
  rowClicked: Boolean = false;
  model: Customer = {};
  searchText: string = '';
  searchBy: string = 'bp_full_name';
  pageSize = 10;
  gridOptions: GridOptions = {
    suppressMenuHide: true,
    pagination: true,
    cacheBlockSize: 10,
    paginationPageSize: 10,
    rowModelType: "infinite",
    onColumnMoved: (event: ColumnMovedEvent) => this._onColumnMoved(event),
  };

  constructor(
    private translate: TranslateService,
    private customerService: CustomerService
  ) { }

  ngOnInit(): void {
  }

  @HostListener("window:resize", ["$event"])
  onResize(event: any) {
    this.gridApi.sizeColumnsToFit();
  }

  onGridReady(params: { api: any; columnApi: any }) {

    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
    this.gridApi.setDatasource(this.dataSource);
  }

  dataSource: IDatasource = {
    getRows: (params: IGetRowsParams) => {
      this.query.pageNo = params.endRow / this.query.perPage;
      this.gridApi.showLoadingOverlay();
      this.customerService
        .getAll({
          perPage: this.pageSize,
          search: this.searchText,
          searchBy: this.searchBy,
          pageNo: params.endRow / this.pageSize
        })
        .pipe(
          tap(({ data, total }) => {
            return params.successCallback(data, total);
          }),
          tap(() => this.gridApi.hideOverlay()))
        .subscribe();
    },
  };

  onSearchChange(text: string) {
    this.searchText = text;
    this.updateTableAfterSearch()
  }

  onSearchByChange(option: string) {
    this.searchBy = option;
    this.updateTableAfterSearch()
  }

  updateTableAfterSearch() {
    this.gridApi.paginationGoToPage(0);
    setTimeout(() => {
      this.gridApi.purgeInfiniteCache();
    }, 100);
  }


  getTranslate(key: string) {
    return this.translate.instant(key);
  }

  handleCellClicked(row: any) {
    this.model = row.data;
    this.rowClicked = true;
  }

  @ViewChild("columnMenu") columnMenu: any;
  @Output() columnChange = new EventEmitter<GridColumn[]>();
  _onColumnChange(columns: any[]) {
    this.columnChange.emit(columns);

    const displayedColumns = Object.assign(
      [],
      this.getDisplayedColumnFields(columns)
    );
    const allColumns = Object.assign(
      [],
      this.getAllDisplayedColumnFields(columns)
    );

    this.gridColumnApi.setColumnsVisible(displayedColumns, false);
    this.gridColumnApi.setColumnsVisible(allColumns, true);
    this.gridApi.sizeColumnsToFit();
  }
  getDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => !item.show)
      .map((item: GridColumn) => item.field);
    return fields;
  }

  getAllDisplayedColumnFields(columns: GridColumn[]): any {
    const fields = columns
      .filter((item) => item.show)
      .map((item: GridColumn) => item.field);
    return fields;
  }

  _columnPositionChange(position: number[]) {
    this.gridColumnApi.moveColumnByIndex(position[0], position[1]);
    this.gridApi.sizeColumnsToFit();
  }

  _onColumnMoved(params: any) {
    const columnDragState = params.columnApi.getColumnState();
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map((e: { colDef: any }) => {
        return e.colDef;
      });
    const newColDef: ColDef[] = colIds.map((item: any, i: string | number) =>
      Object.assign({}, item, columnDragState[i])
    );
    this.columnDefs = newColDef;
  }

  refreshRowData(data: any) {
    const rowNode = this.gridApi.getRowNode(data.bp_id)!;
    rowNode && rowNode.setData(data);
  }

  getRowId(params: any) {
    return params.data.bp_id;
  };
}
