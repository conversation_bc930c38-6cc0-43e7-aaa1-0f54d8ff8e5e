<main>
  <div class="d-flex flex-column flex-shrink-0 sidebar" [ngClass]="{ 'expanded': isExpanded }">
    <div class="" title="Icon-only" class="sidebar-collapse-btn" (click)="handleSidebarToggle()">
      <span class="material-icons-outlined"> menu </span>
    </div>
    <ul class="nav nav-pills flex-column" [ngClass]="{ 'nav-flush': !isExpanded }">
      <ng-container *ngFor="let link of links">
        <ng-container *checkAppFeature="link.feature">
          <li class="nav-item" *checkPermission="link.permission">
            <a [routerLink]="link.routerLink" [routerLinkActive]="'active'" class="n-item-btn"
              [ngClass]="{ '': !isExpanded }" [title]="link.label" (click)="isExpanded = false">
              <span>
                <i class="material-icons-outlined font-icon" *ngIf="link.materialFontName">{{link.materialFontName}}</i>
                <img class="img-icon material-icons-outlined" *ngIf="!link.materialFontName"
                  [routerLinkActive]="'active'" [src]="link.fontIcon" />
              </span>
              <span *ngIf="isExpanded">{{ link.label }}</span>
            </a>
          </li>
        </ng-container>
      </ng-container>
    </ul>
  </div>
  <div class="content">
    <header class="sticky-top">
      <div class="header-body">
        <div class="logo-sec">
          <a routerLink="/store"><img src="/assets/images/logo.png" alt="" title="" class="img-fluid" /></a>
        </div>
        <div class="user-login-sec">
          <div class="d-flex align-items-end flex-column justify-content-center selected-cust"
            *ngIf="loggedInUser?.partner_function?.customer_id"
            [ngbTooltip]="'#'+loggedInUser?.partner_function?.customer_id+' - '+loggedInUser?.partner_function?.name">
            <span>#{{loggedInUser?.partner_function?.customer_id}}</span>
            <span class="text-truncate d-none d-md-block">{{loggedInUser?.partner_function?.name}}</span>
          </div>
          <a href="javascript:void(0);" class="h-notification-part" (click)="openDialog()"
            *ngIf="loggedInUser?.total_customer > 1">
            <span class="notification-count">{{loggedInUser?.total_customer || 0}}</span>
            <span class="material-icons-outlined">person_search</span>
          </a>
          <a class="h-notification-part" [routerLink]="['/store/cart']">
            <span class="notification-count">{{cart?.to_Item?.length || 0}}</span>
            <span class="material-icons-outlined">shopping_cart</span>
          </a>
          <div class="user-login" ngbDropdown>
            <div ngbDropdownToggle class="d-flex align-items-center gap-1">
              <div class="user-img" style="background-image: url('/assets/images/user-image.jpg')"></div>
              <div class="user-name">
                <h4>{{userName}}</h4>
              </div>
            </div>
            <div ngbDropdownMenu class="header-toggle-menu">
              <button ngbDropdownItem (click)="logout()"><span class="material-icons-outlined">logout</span>
                Logout</button>
            </div>
          </div>
        </div>
      </div>
    </header>
    <router-outlet></router-outlet>
    <footer class="footer-sec">
      <div class="footer-body">
        <ul>
          <li><a [href]="homeUrl"><span class="material-icons-outlined">home</span> Home</a></li>
          <li><a [href]="termsUrl"><span class="material-icons-outlined">edit_document</span> Terms & Conditions</a>
          </li>
          <li><a [href]="privacyPolicyUrl"><span class="material-icons-outlined">receipt_long</span> Privacy Policy</a>
          </li>
          <li><a [href]="contactUrl"><span class="material-icons-outlined">phone_in_talk</span> Contact Us</a></li>
        </ul>
      </div>
    </footer>
  </div>
  <app-product-added-sidebar></app-product-added-sidebar>
</main>