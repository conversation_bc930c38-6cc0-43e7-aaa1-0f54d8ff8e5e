import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Subject, takeUntil } from "rxjs";
import { NgbCalendar } from "@ng-bootstrap/ng-bootstrap";

import { AuthService } from "src/app/core/authentication/auth.service";
import { ProductRegisterService } from "../services/product-register/product-register.service";
import { AppToastService } from "src/app/shared/services/toast.service";

@Component({
  selector: "app-register-product",
  templateUrl: "./register-product.component.html",
  styleUrls: ["./register-product.component.scss"],
})
export class RegisterProductComponent implements OnInit, OnDestroy {
  private ngUnsubscribe = new Subject<void>();
  public submitted: boolean = false;
  public saving: boolean = false;
  public sellerDetails: any = null;
  public form: FormGroup;

  constructor(
    private calendarService: NgbCalendar,
    private formBuilder: FormBuilder,
    public authService: AuthService,
    public productRegisterService: ProductRegisterService,
    private _snackBar: AppToastService
  ) {
    this.sellerDetails = {
      ...this.authService.userDetail.partner_function,
    };
  }

  ngOnInit(): void {
    this.createForm();
  }

  createForm() {
    this.form = this.formBuilder.group({
      serial: ["", Validators.required],
      product_id: ["", Validators.required],
      purchase_place: [""],
      purchase_date: [null, Validators.required],
      purchase_price: [null],
      purchase_proof: [null],
    });
  }

  today() {
    return this.calendarService.getToday();
  }

  get f(): any {
    return this.form.controls;
  }

  public onSubmit() {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    const payload = this.form.value;
    const formData = new FormData();
    for (let key in payload) {
      if (payload.hasOwnProperty(key) && payload[key]) {
        if (key === "purchase_date") {
          const d = payload[key];
          formData.append(key, `${d.year}-${d.month}-${d.day}`);
        } else {
          formData.append(key, payload[key]);
        }
      }
    }

    this.saving = true;
    this.productRegisterService
      .create(formData)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.submitted = false;
          this.saving = false;
          this._snackBar.open(
            "Thank you for being a value customer. Your product is registered."
          );
        },
        error: (e: any) => {
          this.submitted = false;
          this.saving = false;
          if (e?.error?.message.includes("unique constraint violated")) {
            this._snackBar.open(
              `Product already registerd with serial # ${payload.serial}`,
              { type: "Error" }
            );
          } else if (
            e?.error?.message.includes("foreign key constraint violation")
          ) {
            this._snackBar.open(
              `Product #${payload.product_id} does not exist.`,
              { type: "Error" }
            );
          } else {
            this._snackBar.open("Error while processing your request.", {
              type: "Error",
            });
          }
        },
      });
  }

  onFileChange(event: any) {
    const file = event.target.files[0];
    this.form.patchValue({ purchase_proof: file });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
