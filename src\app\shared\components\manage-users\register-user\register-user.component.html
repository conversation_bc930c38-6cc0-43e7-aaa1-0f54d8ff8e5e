<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Register {{userRoleType==='STOREFRONT'? 'Contact' : 'User'}}</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form">
        <div class="form-group mb-3 required">
            <label class="input-label">Customer ID</label>
            <ng-select [items]="customers$ | async" bindLabel="customer_id" [multiple]="userRoleType!=='STOREFRONT'"
                [hideSelected]="true" [loading]="customerLoading" [minTermLength]="0" [typeahead]="customerInput$"
                formControlName="customers" (add)="onAddCustOption($event)" [maxSelectedItems]="10"
                [placeholder]="userRoleType!=='STOREFRONT' ? 'Select \'ALL\' or enter 2 or more chars to search customer' : ''"
                typeToSearchText="Enter 2 or more chars to search customer">
                <ng-template ng-option-tmp let-item="item">
                    <span>{{ item.customer_id }}</span>
                    <span *ngIf="item.customer_name">: {{ item.customer_name }}</span>
                </ng-template>
            </ng-select>
            <div *ngIf="submitted && f['customers'].errors" class="invalid-feedback d-block">
                <div *ngIf="f['customers'].errors['required']">Customer IDs are
                    required
                </div>
            </div>
        </div>

        <div class="form-group mb-3 required">
            <label class="input-label">Role</label>
            <select formControlName="role" class="form-select mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['role'].errors }">
                <option value="">Select User Role</option>
                <ng-container *ngFor="let ur of userRoles">
                    <option [value]="ur.id" *ngIf="roleHierarchy.includes(ur.id)">
                        {{ur.role_name}}</option>
                </ng-container>

            </select>
            <div *ngIf="submitted && f['role'].errors" class="invalid-feedback">
                <div *ngIf="submitted && f['role'].errors && f['role'].errors['required']">Role is
                    required
                </div>
            </div>
        </div>

        <div class="form-group mb-3 required">
            <label class="input-label">First Name</label>
            <input type="text" formControlName="firstName" class="form-control mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['firstName'].errors }" />
            <div *ngIf="submitted && f['firstName'].errors" class="invalid-feedback">
                <div *ngIf="submitted && f['firstName'].errors && f['firstName'].errors['required']">First Name is
                    required</div>
            </div>
        </div>

        <div class="form-group mb-3 required">
            <label class="input-label">Last Name</label>
            <input type="text" formControlName="lastName" class="form-control mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['lastName'].errors }" />
            <div *ngIf="submitted && f['lastName'].errors" class="invalid-feedback">
                <div *ngIf="f['lastName'].errors['required']">Last Name is required</div>
            </div>
        </div>

        <div class="form-group mb-3 required">
            <label class="input-label">Email Address</label>
            <input type="text" formControlName="email" class="form-control mt-1"
                [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" />
            <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
                <div *ngIf="f['email'].errors['required']">Email is required</div>
                <div *ngIf="f['email'].errors['email']">Email is invalid</div>
            </div>
        </div>

        <div class="form-group mb-3">
            <label>Address Line 1</label>
            <input type="text" formControlName="addressLine1" class="form-control mt-1" />
        </div>

        <div class="form-group mb-3">
            <label>Address Line 2</label>
            <input type="text" formControlName="addressLine2" class="form-control mt-1" />
        </div>

        <div class="form-group mb-3">
            <label>City</label>
            <input type="text" formControlName="city" class="form-control mt-1" />
        </div>

        <div class="form-group mb-3">
            <label>State</label>
            <select formControlName="state" class="form-select mt-1">
                <option *ngFor="let state of states" [value]="state.name">{{state.name}}</option>
            </select>
        </div>

        <div class="form-group mb-3">
            <label>Zip Code</label>
            <input type="text" formControlName="zipcode" class="form-control mt-1" />
        </div>

        <div class="form-group mb-3">
            <label>Country</label>
            <input type="text" formControlName="country" class="form-control mt-1" readonly />
        </div>

        <div class="form-group mb-3 form-check">
            <input type="checkbox" formControlName="acceptTerms" class="form-check-input"
                [ngClass]="{ 'is-invalid': submitted && f['acceptTerms'].errors }" />
            <label for="acceptTerms" class="form-check-label">By creating an account you are agreeing to the Privacy
                Policy and Tearms and Conditions.</label>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-light me-2" (click)="activeModal.dismiss()">Cancel</button>
    <button (click)="onSubmit()" [disabled]="saving" class="btn btn-primary">{{ saving ? 'Registering' :
        'Register' }}</button>
</div>