import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { SharedModule } from 'src/app/shared/shared.module';
import { CheckoutConfirmationComponent } from './checkout-confirmation.component';

@NgModule({
  declarations: [
    CheckoutConfirmationComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: '', component: CheckoutConfirmationComponent }]),
  ]
})
export class CheckoutConfirmaionModule { }
