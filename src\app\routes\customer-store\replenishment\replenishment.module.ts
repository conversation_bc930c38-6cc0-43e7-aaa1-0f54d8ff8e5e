import { NgModule } from '@angular/core';
import { CommonModule, CurrencyPipe } from '@angular/common';

import { ReplenishmentRoutingModule } from './replenishment-routing.module';
import { ReplenishmentHistoryComponent } from './replenishment-history/replenishment-history.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';

@NgModule({
  declarations: [
    ReplenishmentHistoryComponent
  ],
  providers: [CurrencyPipe],
  imports: [
    CommonModule,
    SharedModule,
    NgbDatepickerModule,
    ReplenishmentRoutingModule
  ]
})
export class ReplenishmentModule { }
