.banner-img {
  height: calc(100vh - 40vh);
  object-fit: cover;
}

.banner-caption {
  top: 20%;
  bottom: auto;
  right: auto;
  text-align: left;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  padding: 15px;
  .t-shadow {
    text-shadow: 2px 2px #000;
    font-weight: bolder;
  }
}

.dashboard-video-sec {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 40vh);
  position: relative;
  overflow: hidden;

  video {
    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    height: auto;
  }
}

@media only screen and (max-width: 1200px) {
  .dashboard-video-sec,
  .banner-img {
    height: calc(100vh - 50vh);
  }
}

@media only screen and (max-width: 1024px) {
  .dashboard-video-sec,
  .banner-img {
    height: 400px;
  }
}

@media only screen and (max-width: 791px) {
  .dashboard-video-sec,
  .banner-img {
    height: 350px;
  }
}
