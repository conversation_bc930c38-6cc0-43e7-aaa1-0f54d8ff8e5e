import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ImportCartComponent } from './import-cart.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { RouterModule } from '@angular/router';


@NgModule({
  declarations: [
    ImportCartComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild([{ path: '', component: ImportCartComponent }]),
  ]
})
export class ImportCartModule { }
