import { Component, Input, OnInit } from "@angular/core";
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidatorFn,
  Validators,
} from "@angular/forms";
import {
  NgbActiveModal,
  NgbCalendar,
  NgbDateStruct,
} from "@ng-bootstrap/ng-bootstrap";
import moment from "moment";

@Component({
  selector: "app-sales-orders-scheduler",
  templateUrl: "./sales-orders-scheduler.component.html",
  styleUrls: ["./sales-orders-scheduler.component.scss"],
})
export class SalesOrdersSchedulerComponent implements OnInit {
  @Input() data: any = null;
  public scheduleTypes: any[] = ["DAILY", "WEEKLY", "MONTHLY"];
  public frequency: any[] = (<any>Array(31)).fill().map((n: any, i: any) => i);
  public weekdays: any[] = [
    "MONDAY",
    "TUESDAY",
    "WEDNESDAY",
    "THURSDAY",
    "FRIDAY",
    "SATURDAY",
    "SUNDAY",
  ];
  public submitted: any = false;

  public form: FormGroup = this.formBuilder.group({
    start_date: [null, Validators.required],
    end_date: [null, Validators.required],
    schedule_type: ["DAILY", Validators.required],
    frequency: [1],
    day_of_month: [1],
    weekdays_to_generate: this.formBuilder.array(
      this.weekdays.map((x) => ["MONDAY"].indexOf(x) > -1)
    ),
    req_data: [null],
    is_cancelled: [false],
  });

  constructor(
    private formBuilder: FormBuilder,
    public activeModal: NgbActiveModal,
    private calendarService: NgbCalendar
  ) {}

  ngOnInit(): void {
    if (this.data) {
      const sd = moment(this.data.start_date, "YYYY-MM-DD").clone();
      const ed = moment(this.data.end_date, "YYYY-MM-DD").clone();
      this.form.patchValue({
        start_date: { year: sd.year(), month: sd.month() + 1, day: sd.date() },
        end_date: { year: ed.year(), month: ed.month() + 1, day: ed.date() },
        schedule_type: this.data.schedule_type,
        is_cancelled: this.data.status === "Cancelled",
      });
      switch (this.data.schedule_type) {
        case "DAILY":
          this.form.patchValue({
            frequency: this.data.frequency,
          });
          break;
        case "WEEKLY":
          this.form.patchValue({
            frequency: this.data.frequency,
          });
          const weekdaysGenerate: any = this.form.get("weekdays_to_generate");
          weekdaysGenerate?.reset();
          weekdaysGenerate?.patchValue(
            this.weekdays.map(
              (x) => this.data.weekdays_to_generate.split(",").indexOf(x) > -1
            )
          );
          weekdaysGenerate.setValidators(
            this.atLeastOneCheckboxCheckedValidator(1)
          );
          break;
        case "MONTHLY":
          this.form.patchValue({
            day_of_month: this.data.day_of_month,
          });
          break;
        default:
          break;
      }
    }
  }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  atLeastOneCheckboxCheckedValidator(minRequired = 1): ValidatorFn | any {
    return function validate(formGroup: FormGroup) {
      let checked = 0;

      Object.keys(formGroup.controls).forEach((key) => {
        const control = formGroup.controls[key];

        if (control.value) {
          checked++;
        }
      });

      if (checked < minRequired) {
        return {
          requireCheckboxToBeChecked: true,
        };
      }

      return null;
    };
  }

  onScheduleTypeChange(schedule_type: string) {
    this.form.patchValue({
      frequency: 1,
    });
    const weekdaysGenerate: any = this.form.get("weekdays_to_generate");
    if (schedule_type === "WEEKLY") {
      weekdaysGenerate.setValidators(
        this.atLeastOneCheckboxCheckedValidator(1)
      );
    } else {
      weekdaysGenerate.clearValidators();
    }
    weekdaysGenerate.updateValueAndValidity();
  }

  today() {
    return this.calendarService.getToday();
  }

  formatDate(date: NgbDateStruct) {
    if (!date) return "";
    let newDate = new Date(date["year"], date["month"] - 1, date["day"]);
    return moment(newDate).format("YYYY-MM-DD");
  }

  convertToValue() {
    const arr: any[] = this.form.value.weekdays_to_generate;
    return arr
      .map((x: any, i: any) => x && this.weekdays[i])
      .filter((x: any) => !!x)
      .join(",");
  }

  onCancelledReplenishment() {
    let d = moment().subtract(1, "days").clone();
    if (!this.form?.value?.is_cancelled) {
      this.form.get("end_date")?.enable();
      d = moment(this.data.end_date, "YYYY-MM-DD").clone();
    } else {
      this.form.get("end_date")?.disable();
    }
    this.form.patchValue({
      end_date: { year: d.year(), month: d.month() + 1, day: d.date() },
    });
  }

  createSalesOrderSchedule() {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    const fVal: any = this.form.getRawValue();
    const data: any = {};
    data.schedule_type = fVal.schedule_type;
    data.start_date = this.formatDate(fVal.start_date);
    if (fVal.end_date) {
      data.end_date = this.formatDate(fVal.end_date);
    }
    switch (fVal.schedule_type) {
      case "DAILY":
        data.frequency = fVal.frequency;
        break;
      case "WEEKLY":
        data.frequency = fVal.frequency;
        data.weekdays_to_generate = this.convertToValue();
        break;
      case "MONTHLY":
        data.day_of_month = fVal.day_of_month;
        break;
      default:
        break;
    }
    const status = "success";
    this.activeModal.close({ status, data });
  }
}
