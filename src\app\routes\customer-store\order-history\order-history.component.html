<section class="order-status-sec">
	<div class="order-status-body">
		<div class="all-main-title-sec">
			<h1>Order Status</h1>
			<div class="all-bedcrumbs">
				<ul>
					<li>
						<a [routerLink]="['/store/dashboard']">
							<span class="material-icons-outlined">home</span> Home
						</a>
					</li>
					<li>Orders</li>
				</ul>
			</div>
		</div>
		<div class="order-contact-list">
			<div class="order-c-box">
				<div class="order-c-icon"><img src="/assets/images/seller-icon.png" alt="" title="" /></div>
				<div class="order-c-details">
					<h4>Customer #</h4>
					<small>{{sellerDetails.bp_customer_number}}</small>
				</div>
			</div>
			<div class="order-c-box">
				<div class="order-c-icon"><img src="/assets/images/phone-icon.png" alt="" title="" /></div>
				<div class="order-c-details">
					<h4>Customer Name</h4>
					<small>{{sellerDetails.name}}</small>
				</div>
			</div>
			<div class="order-c-box address-box">
				<div class="order-c-icon"><img src="/assets/images/address-icon.png" alt="" title="" /></div>
				<div class="order-c-details">
					<h4>ADDRESS</h4>
					<small>{{sellerDetails.address}}</small>
				</div>
			</div>
		</div>
		<div class="order-status-form all-form-res">
			<div class="form">
				<div class="form-group fg">
					<label><span class="material-icons-outlined">calendar_month</span> Date From</label>
					<div class="input-group">
						<input class="form-control" name="picker1" [(ngModel)]="searchCriteria.fromDate" ngbDatepicker
							#d="ngbDatepicker" [maxDate]="today()" />
						<button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
							<i class="material-icons-outlined">
								calendar_month
							</i>
						</button>
					</div>
				</div>
				<div class="form-group fg">
					<label><span class="material-icons-outlined">calendar_month</span> Date To</label>
					<div class="input-group">
						<input class="form-control" name="picker2" [(ngModel)]="searchCriteria.toDate" ngbDatepicker
							#d1="ngbDatepicker" [minDate]="searchCriteria.fromDate" [maxDate]="today()" />
						<button class="btn btn-outline-secondary" (click)="d1.toggle()" type="button">
							<i class="material-icons-outlined">
								calendar_month
							</i>
						</button>
					</div>
				</div>
				<div class="form-group p-o-num fg-md">
					<label class="text-truncate"><span class="material-icons-outlined">fact_check</span> Purchase Order #</label>
					<input type="input" class="form-control" placeholder="Purchase Order #"
						[(ngModel)]="searchCriteria.purchaseOrder" (keyup.enter)="getOrderHistory()">
				</div>
				<div class="form-group o-num fg-md">
					<label><span class="material-icons-outlined">pin</span> Order #</label>
					<input type="input" class="form-control" placeholder="Enter Order #"
						(keyup.enter)="getOrderHistory()" [(ngModel)]="searchCriteria.sdDoc">
				</div>
				<div class="form-group fg-sm">
					<label class="text-truncate"><span class="material-icons-outlined">feed</span> Order Status</label>
					<select class="form-control select-arrow-down" [(ngModel)]="searchCriteria.docStauts">
						<option *ngFor="let status of statuses" [value]="status.code">{{status.description}}</option>
					</select>
				</div>
				<div class="form-group fg-sm">
					<label><span class="material-icons-outlined">checklist</span> Channel</label>
					<select class="form-control select-arrow-down" [(ngModel)]="searchCriteria.channel">
						<option value="all">All</option>
						<option value="Web Order">Web Order</option>
						<option value="S4 Order">S4 Order</option>
					</select>
				</div>
				<div class="form-btn-sec d-flex justify-content-center gap-1">
					<button type="button" class="mx-4 order-s-btn" (click)="clear()">Clear</button>
					<button type="button" class="mx-4 order-s-btn" (click)="search()" [disabled]="loading">{{loading ?
						'Searching...' : 'Search'}}</button>
				</div>
			</div>
		</div>
		<div class="order-s-table d-flex flex-column align-items-end">
			<app-grid [columns]="columnDefs" (rowClick)="goToOrder($event)" [data]="orders" [showExport]="true"
				(exportClick)="exportToExcel()" *ngIf="!loading && orders.length"></app-grid>
			<div class="w-100" *ngIf="loading || !orders.length">{{ loading ? 'Loading...' : 'No records found.' }}
			</div>
		</div>
	</div>
</section>