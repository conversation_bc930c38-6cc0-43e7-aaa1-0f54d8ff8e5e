<div class="main-container">
    <app-banner-preview [customerID]="customer_id" [settings]="settings"></app-banner-preview>
    <!-- <div class="dashboard-banner-sec">
        <video width="100%" height="100%" autoplay loop controls>
            <source src="/assets/images/city-building.mp4" type="video/mp4">
        </video>
    </div> -->
    <div class="container d-flex flex-column justify-content-center">
        <!-- <div class="d-company-name">
            <h2 class="welcome-msg fw-bolder">Welcome to</h2>
            <h2 class="company-name fw-bolder">{{settings?.company_name || ''}}</h2>
        </div> -->

        <div class="row links-container">
            <ng-container *ngFor="let link of links">
                <ng-container *checkAppFeature="link.feature">
                    <div class="col" *checkPermission="link.permission">
                        <a class="d-flex flex-column" [routerLink]="link.routerLink">
                            <div class="circle d-flex justify-content-center align-items-center">
                                <i class="material-icons-outlined"
                                    *ngIf="link.materialFontName">{{link.materialFontName}}</i>
                                <img [src]="link.img" *ngIf="link.img">
                            </div>
                            <p class="desc mt-2">{{link.label}}</p>
                        </a>
                    </div>
                </ng-container>
            </ng-container>
        </div>
    </div>
    <div class="fixed-bottom">
        <div class="chatbot-container">
            <div id='webchat'></div>
        </div>
    </div>
</div>