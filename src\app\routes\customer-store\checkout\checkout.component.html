<div class="all-main-title-sec cart-details-main-title">
  <div class="all-bedcrumbs">
    <ul>
      <li>
        <a [routerLink]="['/store/dashboard']">
          <span class="material-icons-outlined">home</span> Home
        </a>
      </li>
      <li>Secured Checkout</li>
    </ul>
  </div>
  <h1>Secured Checkout</h1>
</div>


<form class="checkout-form" [formGroup]="checkoutForm">
  <div class="secure-checkout-sec">
    <div class="items-container">
      <div class="progress-container d-flex justify-content-evenly">
        <div class="selected">
          <span class="circle"><span>1</span> </span>
          <span class="text">Payment Type</span>
          <span class="timeline timeline-r"></span>
        </div>
        <div [class.selected]="currentStep >= 2">
          <span class="circle"><span>2</span></span>
          <span class="text">Shipping Address</span>
          <span class="timeline timeline-r"></span>
        </div>
        <div [class.selected]="currentStep == 3">
          <span class="circle"><span>3</span></span>
          <span class="text">Final Review</span>
        </div>
      </div>

      <div class="steps-container">
        <ng-container [ngSwitch]="currentStep">
          <ng-container *ngSwitchCase="1">
            <div class="form-group payment-type">
              <input class="form-check-input me-3" type="radio" name="paymentType" formControlName="paymentType"
                [value]="'Credit Card'" id="creditCard" />
              <label class="form-label" for="creditCard">Credit Card</label>
            </div>
            <div class="form-group payment-type">
              <input class="form-check-input me-3" type="radio" name="paymentType" formControlName="paymentType"
                [value]="'Account Payment'" id="accountPayment" />
              <label class="form-label" for="accountPayment">Account Payment</label>
            </div>
            <div class="form-group">
              <label class="form-label"><span class="material-icons-outlined">pin</span> P.O. Number <span
                  class="text-danger">*</span></label>
              <input type="text" class="form-control" placeholder="Enter P.O Number"
                formControlName="PurchaseOrderByCustomer" />
              <div *ngIf="submitted && f['PurchaseOrderByCustomer'].errors" class="invalid-feedback d-block">
                <div
                  *ngIf=" submitted && f['PurchaseOrderByCustomer'].errors && f['PurchaseOrderByCustomer'].errors['required'] ">
                  P.O. Number is required
                </div>
              </div>
            </div>
            <div class="form-group" *ngIf="f['textCode'].value">
              <label class="form-label"><span class="material-icons-outlined">sticky_note_2</span> Special
                Instructions</label>
              <input type="input" class="form-control" placeholder="Enter Special Instructions"
                formControlName="specialInstructions" />
            </div>
            <div class="form-group">
              <label class="form-label"><span class="material-icons-outlined">calendar_month</span> Requested
                Date</label>
              <div class="input-group">
                <input class="form-control" name="picker1" formControlName="RequestedDeliveryDate" ngbDatepicker
                  #d="ngbDatepicker" [minDate]="minRequestedDeliveryDate" />
                <button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
                  <i class="material-icons-outlined">
                    calendar_month
                  </i>
                </button>
              </div>
            </div>
          </ng-container>
          <ng-container *ngSwitchCase="2">
            <div class="address-container mb-4">
              <div class="row">
                <div class="col">
                  <h3>Shipping Addresses</h3>
                  <small>Select an already added address from below.</small>
                </div>
                <div class="col d-flex flex-md-row-reverse">
                  <a class="btn btn-primary" [routerLink]="['/store/checkout/add-shipping-address']">
                    Create Shipping Address
                  </a>
                </div>
              </div>
              <div class="py-4" *ngFor="let address of addresses; let i = index">
                <div class="form-check d-flex align-items-center">
                  <input class="form-check-input me-3" type="radio" name="shippingAddress"
                    formControlName="shippingAddress" [value]="address" [id]="address?.id"
                    (change)="onChangeshipping()" />
                  <label class="form-check-label" [for]="address?.id">
                    <p>{{ address?.name }}</p>
                    <p>{{ address?.address }}</p>
                  </label>
                </div>
              </div>
              <div class="py-4">
                <label class="form-label fw-bold">Shipping Method</label>
                <select class="form-select select-arrow-down">
                  <option selected>Standard Shipping</option>
                </select>
              </div>
            </div>
          </ng-container>
          <div class="d-flex" *ngSwitchCase="3">
            <div class="shipping-address-container">
              <span class="material-icons-outlined">rocket_launch</span>
              <div class="shipping-address-box">
                <h3>Shipping Address</h3>
                <small>{{ checkoutForm?.value?.shippingAddress?.name }}</small>
                <small>{{ checkoutForm?.value?.shippingAddress?.address }}</small>
              </div>
            </div>
          </div>
        </ng-container>
        <div class="d-flex justify-content-between flex-wrap btn-container gap-3">
          <button type="button" class="btn fw-bolder" (click)="next()" *ngIf="currentStep != steps"
            [disabled]="!sosRes?.to_Item?.A_SalesOrderItemSimulationType.length"> Next</button>
          <button type="button" class="btn fw-bolder light" (click)="back()" *ngIf="currentStep > 1"> Back </button>
        </div>
      </div>

      <ng-container *ngIf="currentStep == 3">
        <div class="item-details">
          <h3>Items to be shipped</h3>
          <div class="item-box"
            *ngFor=" let product of sosRes?.to_Item?.A_SalesOrderItemSimulationType; trackBy: trackBy">
            <div class="item-box-img"
              [ngStyle]="{'background-image': 'url('+ (product.Material | getProductImage | async)+ ')'}"> </div>
            <div class="item-box-content">
              <a [routerLink]="['/store/product-details', product?.Material]">
                <h4>{{ product?.SalesOrderItemText }}</h4>
                <small>{{ product?.Material }}</small>
                <div class="item-box-bottom-content flex-wrap gap-3 justify-content-between">
                  <div class="quantity">Quantity: <span>{{ product.RequestedQuantity }}</span></div>
                  <div class="item-price">{{ product?.formatted_base_price }} <span>{{
                      product?.formatted_base_price_each }} each</span></div>
                </div>
              </a>

            </div>
          </div>
        </div>
      </ng-container>
    </div>
    <div class="checkout-id-summary">
      <div class="checkout-id-summary-body">
        <h3>Order Summary</h3>
        <ng-container *ngIf="currentStep != 3">
          <small>{{ sosRes?.to_Item?.A_SalesOrderItemSimulationType.length || 0 }} items to be delivered</small>
          <div class="item-box"
            *ngFor="let product of sosRes?.to_Item?.A_SalesOrderItemSimulationType; trackBy: trackBy">
            <div class="item-box-img"
              [ngStyle]="{'background-image': 'url('+ (product.Material | getProductImage | async)+ ')'}">
            </div>
            <div class="item-box-content">
              <a [routerLink]="['/store/product-details', product?.Material]">
                <h4 class="desc text-truncate">{{ product?.SalesOrderItemText }} </h4>
                <div class="item-box-list">
                  <small>Material No: <span>{{ product?.Material }}</span></small>
                  <small>Item Price: <span>{{ product?.formatted_base_price_each }}</span></small>
                </div>
                <small>Unit: <span>EACH</span></small>
                <div class="quantity">Qty: <span>{{ product.RequestedQuantity }}</span></div>
                <!-- <div class="item-box-bottom-content">
                          <div class="unit">Est Delivery Date <span>31-01-2023</span></div>
                          <div class="unit">Est Delivery Qty <span>1</span></div>
                      </div> -->
                <div class="item-price">{{ product?.formatted_base_price }}</div>
              </a>
            </div>
          </div>
        </ng-container>
        <div class="checkout-summary-price">
          <ul>
            <li>
              Subtotal
              <span>{{ sosRes?.formatted_sub_total || (0 | currency : sosRes?.TransactionCurrency)}}</span>
            </li>
            <li>
              Shipping
              <span *ngIf="currentStep === 3;else shipping">
                {{sosRes?.formatted_shipping || (0 | currency : sosRes?.TransactionCurrency)}}
              </span>
              <ng-template #shipping>
                <span>{{0 | currency : sosRes?.TransactionCurrency}}</span>
              </ng-template>
            </li>
            <li>
              Tax
              <span *ngIf="currentStep === 3;else tax">
                {{sosRes?.formatted_sales_tax || (0 | currency : sosRes?.TransactionCurrency)}}
              </span>
              <ng-template #tax>
                <span>{{0 | currency : sosRes?.TransactionCurrency}}</span>
              </ng-template>
          </ul>
          <ul>
            <li class="total-price">
              Total
              <span *ngIf="currentStep === 3;else total">
                {{sosRes?.formatted_total || (0 | currency : sosRes?.TransactionCurrency)}}
              </span>
              <ng-template #total>
                <span>{{ sosRes?.formatted_sub_total || (0 | currency : sosRes?.TransactionCurrency)}}</span>
              </ng-template>
            </li>
          </ul>
          <ng-container *ngIf="currentStep == 3">
            <button type="button" class="btn" (click)="confirm()" [disabled]="isOrderPlaced">
              <span class="material-icons-outlined" *ngIf="!isOrderPlaced">near_me</span>
              <i class="material-icons-outlined spin m-1" *ngIf="isOrderPlaced">sync</i> Place Order </button>
            <ng-container *checkAppFeature="'F0008'">
              <ng-container *checkPermission="'P0033'">
                <button type="button" class="btn" (click)="!isSOScheduled && openDialog()" [disabled]="isSOScheduled">
                  <span class="material-icons-outlined" *ngIf="!isSOScheduled">schedule</span>
                  <i class="material-icons-outlined spin m-1" *ngIf="isSOScheduled">sync</i>
                  SCHEDULE AUTO REPLENISHMENT</button>
              </ng-container>
            </ng-container>
            <button type="button" class="btn btn-light" [routerLink]="['../catalogues']"><span
                class="material-icons-outlined">local_mall</span> Continue Shopping </button>
            <p class="terms">By placing the order, you agree to our <a [href]="settings?.terms_url"
                target="_blank">Terms & Conditions</a> and our <a [href]="settings?.privacy_policy_url"
                target="_blank">Privacy Policy</a>.</p>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</form>