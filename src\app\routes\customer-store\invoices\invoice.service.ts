import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { ApiConstant } from 'src/app/constants/api.constants';

@Injectable({
  providedIn: 'root'
})
export class InvoiceService {

  constructor(private http: HttpClient) { }

  getAll(data: any) {
    const params = new HttpParams().appendAll(data);
    return this.http.get<any>(ApiConstant.INVOICE, {
      params,
    });
  }

  getInvoiveStatuses() {
     return this.http.get<any>(ApiConstant.GET_INVOICE_STATUSES);
  }

  getInvoiveTypes() {
    return this.http.get<any>(ApiConstant.GET_INVOICE_TYPES);
 }

 invoicePdf(url: string) {
  return this.http.get<Blob>(url, { observe: 'response', responseType: 'blob' as 'json'});

 }
}
